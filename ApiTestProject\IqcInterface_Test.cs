﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using OutsideService.IQC;
using OutsideService.IQC.Models;
using Services.AstroenergyService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [TestClass]
    public class IqcInterface_Test
    {
        [TestMethod]
        [DataRow("2024/04/28 00:00:00")]
        public void Test_Get(string specifiedTime)
        {
            DateTime dateTime= DateTime.Parse(specifiedTime);
            string Z0CALDAY_MVIM = dateTime.ToString("yyyy-MM-dd'T'HH:mm:ss");
            string Z0CALDAY_MVIMTo = dateTime.AddDays(4).AddSeconds(-1).ToString("yyyy-MM-dd'T'HH:mm:ss");
            IQCService _iQCService = new IQCService();
           var responseResult = _iQCService.Get_ZRQM_CP001_Q001("", Z0CALDAY_MVIM, Z0CALDAY_MVIMTo);

            Feed feed = IQCToMESService.Deserialize<Feed>(responseResult);
            var cnt = feed.Entries.Count(x => x.Content.Properties.ZXN_MATER == "1000000244");
            Console.WriteLine(cnt);
          Assert.IsTrue(cnt > 300);
        }

        [TestMethod]
        public void Test_Get2()
        {
            IQCToMESService _iqcService = new IQCToMESService();
            _iqcService.IQC_ScheduledTask().GetAwaiter().GetResult();
        }
    }
}
