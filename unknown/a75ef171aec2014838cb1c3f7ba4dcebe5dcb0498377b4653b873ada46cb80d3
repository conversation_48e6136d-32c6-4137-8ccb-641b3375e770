<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Runtime</name>
    </assembly>
    <members>
        <member name="T:System.Strings">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:System.Strings.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:System.Strings.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:System.Strings.ArgumentException_TupleIncorrectType">
            <summary>
              Looks up a localized string similar to Argument must be of type {0}..
            </summary>
        </member>
        <member name="P:System.Strings.ArgumentException_TupleLastArgumentNotATuple">
            <summary>
              Looks up a localized string similar to The last element of an eight element tuple must be a Tuple..
            </summary>
        </member>
        <member name="T:System.Collections.IStructuralEquatable">
            <summary>
                Defines methods to support the comparison of objects for structural equality. 
            </summary>
        </member>
        <member name="M:System.Collections.IStructuralEquatable.Equals(System.Object,System.Collections.IEqualityComparer)">
            <summary>
                Determines whether an object is structurally equal to the current instance.
            </summary>
            <param name="other">The object to compare with the current instance.</param>
            <param name="comparer">An object that determines whether the current instance and other are equal. </param>
            <returns>true if the two objects are equal; otherwise, false.</returns>
        </member>
        <member name="M:System.Collections.IStructuralEquatable.GetHashCode(System.Collections.IEqualityComparer)">
            <summary>
                Returns a hash code for the current instance.
            </summary>
            <param name="comparer">An object that computes the hash code of the current object.</param>
            <returns>The hash code for the current instance.</returns>
        </member>
        <member name="T:System.Collections.IStructuralComparable">
            <summary>
                Supports the structural comparison of collection objects.
            </summary>
        </member>
        <member name="M:System.Collections.IStructuralComparable.CompareTo(System.Object,System.Collections.IComparer)">
            <summary>
                Determines whether the current collection object precedes, occurs in the same position as, or follows another object in the sort order.
            </summary>
            <param name="other">The object to compare with the current instance.</param>
            <param name="comparer">An object that compares members of the current collection object with the corresponding members of other.</param>
            <returns>An integer that indicates the relationship of the current collection object to other.</returns>
            <exception cref="T:System.ArgumentException">
                This instance and other are not the same type.
            </exception>
        </member>
        <member name="T:System.Func`6">
            <summary>
                Encapsulates a method that has five parameters and returns a value of the type specified by the TResult parameter.
            </summary>
            <typeparam name="T1">The type of the first parameter of the method that this delegate encapsulates.</typeparam>
            <typeparam name="T2">The type of the second parameter of the method that this delegate encapsulates.</typeparam>
            <typeparam name="T3">The type of the third parameter of the method that this delegate encapsulates.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the method that this delegate encapsulates.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the method that this delegate encapsulates.</typeparam>
            <typeparam name="TResult">The type of the return value of the method that this delegate encapsulates.</typeparam>
            <param name="arg1">The first parameter of the method that this delegate encapsulates.</param>
            <param name="arg2">The second parameter of the method that this delegate encapsulates.</param>
            <param name="arg3">The third parameter of the method that this delegate encapsulates.</param>
            <param name="arg4">The fourth parameter of the method that this delegate encapsulates.</param>
            <param name="arg5">The fifth parameter of the method that this delegate encapsulates.</param>
            <returns>The return value of the method that this delegate encapsulates.</returns>
        </member>
        <member name="T:System.IProgress`1">
            <summary>Defines a provider for progress updates.</summary>
            <typeparam name="T">The type of progress update value.</typeparam>
        </member>
        <member name="M:System.IProgress`1.Report(`0)">
            <summary>Reports a progress update.</summary>
            <param name="value">The value of the updated progress.</param>
        </member>
        <member name="T:System.Runtime.CompilerServices.AsyncStateMachineAttribute">
            <summary>Identities the async state machine type for this method.</summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.StateMachineAttribute">
            <summary>Identities the state machine type for this method.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.StateMachineAttribute.#ctor(System.Type)">
            <summary>Initializes the attribute.</summary>
            <param name="stateMachineType">The type that implements the state machine.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.StateMachineAttribute.StateMachineType">
            <summary>Gets the type that implements the state machine.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.AsyncStateMachineAttribute.#ctor(System.Type)">
            <summary>Initializes the attribute.</summary>
            <param name="stateMachineType">The type that implements the state machine.</param>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerMemberNameAttribute">
            <summary>
            Allows you to obtain the method or property name of the caller to the method.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerLineNumberAttribute">
            <summary>
            Allows you to obtain the line number in the source file at which the method is called.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerFilePathAttribute">
            <summary>
            Allows you to obtain the full path of the source file that contains the caller.
            This is the file path at the time of compile.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.IteratorStateMachineAttribute">
            <summary>Identities the iterator state machine type for this method.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.IteratorStateMachineAttribute.#ctor(System.Type)">
            <summary>Initializes the attribute.</summary>
            <param name="stateMachineType">The type that implements the state machine.</param>
        </member>
        <member name="T:System.ITuple">
            <summary>
            Helper so we can call some tuple methods recursively without knowing the underlying types.
            </summary>
        </member>
        <member name="T:System.Tuple">
            <summary>
                Provides static methods for creating tuple objects. 
            </summary>
        </member>
        <member name="M:System.Tuple.Create``1(``0)">
            <summary>
                Creates a new 1-tuple, or singleton.
            </summary>
            <typeparam name="T1">The type of the only component of the tuple.</typeparam>
            <param name="item1">The value of the only component of the tuple.</param>
            <returns>A tuple whose value is (item1).</returns>
        </member>
        <member name="M:System.Tuple.Create``2(``0,``1)">
            <summary>
                Creates a new 3-tuple, or pair.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <returns>An 2-tuple (pair) whose value is (item1, item2).</returns>
        </member>
        <member name="M:System.Tuple.Create``3(``0,``1,``2)">
            <summary>
                Creates a new 3-tuple, or triple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <returns>An 3-tuple (triple) whose value is (item1, item2, item3).</returns>
        </member>
        <member name="M:System.Tuple.Create``4(``0,``1,``2,``3)">
            <summary>
                Creates a new 4-tuple, or quadruple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <returns>An 4-tuple (quadruple) whose value is (item1, item2, item3, item4).</returns>
        </member>
        <member name="M:System.Tuple.Create``5(``0,``1,``2,``3,``4)">
            <summary>
                Creates a new 5-tuple, or quintuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <returns>An 5-tuple (quintuple) whose value is (item1, item2, item3, item4, item5).</returns>
        </member>
        <member name="M:System.Tuple.Create``6(``0,``1,``2,``3,``4,``5)">
            <summary>
                Creates a new 6-tuple, or sextuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <typeparam name="T6">The type of the sixth component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <param name="item6">The value of the sixth component of the tuple.</param>
            <returns>An 6-tuple (sextuple) whose value is (item1, item2, item3, item4, item5, item6).</returns>
        </member>
        <member name="M:System.Tuple.Create``7(``0,``1,``2,``3,``4,``5,``6)">
            <summary>
                Creates a new 7-tuple, or septuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <typeparam name="T6">The type of the sixth component of the tuple.</typeparam>
            <typeparam name="T7">The type of the seventh component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <param name="item6">The value of the sixth component of the tuple.</param>
            <param name="item7">The value of the seventh component of the tuple.</param>
            <returns>An 7-tuple (septuple) whose value is (item1, item2, item3, item4, item5, item6, item7).</returns>
        </member>
        <member name="M:System.Tuple.Create``8(``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
                Creates a new 8-tuple, or octuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <typeparam name="T6">The type of the sixth component of the tuple.</typeparam>
            <typeparam name="T7">The type of the seventh component of the tuple.</typeparam>
            <typeparam name="T8">The type of the eighth component of the tuple.</typeparam>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <param name="item6">The value of the sixth component of the tuple.</param>
            <param name="item7">The value of the seventh component of the tuple.</param>
            <param name="item8">The value of the eighth component of the tuple.</param>
            <returns>An 8-tuple (octuple) whose value is (item1, item2, item3, item4, item5, item6, item7, item8).</returns>
        </member>
        <member name="T:System.Tuple`1">
            <summary>
                Represents a 1-tuple, or singleton. 
            </summary>
            <typeparam name="T1">The type of the tuple's only component.</typeparam>
        </member>
        <member name="M:System.Tuple`1.#ctor(`0)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`1"/> class.
            </summary>
            <param name="item1">The value of the current tuple object's single component.</param>
        </member>
        <member name="M:System.Tuple`1.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`1.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`1.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`1.Item1">
            <summary>
                Gets the value of the tuple object's single component. 
            </summary>
            <value>
                The value of the current tuple object's single component.
            </value>
        </member>
        <member name="T:System.Tuple`2">
            <summary>
                Represents an 2-tuple, or pair.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
        </member>
        <member name="M:System.Tuple`2.#ctor(`0,`1)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`2"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
        </member>
        <member name="M:System.Tuple`2.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`2.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`2.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`2.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`2.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="T:System.Tuple`3">
            <summary>
                Represents an 3-tuple, or triple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
        </member>
        <member name="M:System.Tuple`3.#ctor(`0,`1,`2)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`3"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
        </member>
        <member name="M:System.Tuple`3.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`3.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`3.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`3.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`3.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="P:System.Tuple`3.Item3">
            <summary>
                Gets the value of the current tuple object's third component.
            </summary>
            <value>
                The value of the current tuple object's third component.
            </value>
        </member>
        <member name="T:System.Tuple`4">
            <summary>
                Represents an 4-tuple, or quadruple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
        </member>
        <member name="M:System.Tuple`4.#ctor(`0,`1,`2,`3)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`4"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
        </member>
        <member name="M:System.Tuple`4.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`4.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`4.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`4.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`4.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="P:System.Tuple`4.Item3">
            <summary>
                Gets the value of the current tuple object's third component.
            </summary>
            <value>
                The value of the current tuple object's third component.
            </value>
        </member>
        <member name="P:System.Tuple`4.Item4">
            <summary>
                Gets the value of the current tuple object's fourth component.
            </summary>
            <value>
                The value of the current tuple object's fourth component.
            </value>
        </member>
        <member name="T:System.Tuple`5">
            <summary>
                Represents an 5-tuple, or quintuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
        </member>
        <member name="M:System.Tuple`5.#ctor(`0,`1,`2,`3,`4)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`5"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
        </member>
        <member name="M:System.Tuple`5.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`5.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`5.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`5.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`5.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="P:System.Tuple`5.Item3">
            <summary>
                Gets the value of the current tuple object's third component.
            </summary>
            <value>
                The value of the current tuple object's third component.
            </value>
        </member>
        <member name="P:System.Tuple`5.Item4">
            <summary>
                Gets the value of the current tuple object's fourth component.
            </summary>
            <value>
                The value of the current tuple object's fourth component.
            </value>
        </member>
        <member name="P:System.Tuple`5.Item5">
            <summary>
                Gets the value of the current tuple object's fifth component.
            </summary>
            <value>
                The value of the current tuple object's fifth component.
            </value>
        </member>
        <member name="T:System.Tuple`6">
            <summary>
                Represents an 6-tuple, or sextuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <typeparam name="T6">The type of the sixth component of the tuple.</typeparam>
        </member>
        <member name="M:System.Tuple`6.#ctor(`0,`1,`2,`3,`4,`5)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`6"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <param name="item6">The value of the sixth component of the tuple.</param>
        </member>
        <member name="M:System.Tuple`6.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`6.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`6.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`6.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`6.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="P:System.Tuple`6.Item3">
            <summary>
                Gets the value of the current tuple object's third component.
            </summary>
            <value>
                The value of the current tuple object's third component.
            </value>
        </member>
        <member name="P:System.Tuple`6.Item4">
            <summary>
                Gets the value of the current tuple object's fourth component.
            </summary>
            <value>
                The value of the current tuple object's fourth component.
            </value>
        </member>
        <member name="P:System.Tuple`6.Item5">
            <summary>
                Gets the value of the current tuple object's fifth component.
            </summary>
            <value>
                The value of the current tuple object's fifth component.
            </value>
        </member>
        <member name="P:System.Tuple`6.Item6">
            <summary>
                Gets the value of the current tuple object's sixth component.
            </summary>
            <value>
                The value of the current tuple object's sixth component.
            </value>
        </member>
        <member name="T:System.Tuple`7">
            <summary>
                Represents an 7-tuple, or septuple.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <typeparam name="T6">The type of the sixth component of the tuple.</typeparam>
            <typeparam name="T7">The type of the seventh component of the tuple.</typeparam>
        </member>
        <member name="M:System.Tuple`7.#ctor(`0,`1,`2,`3,`4,`5,`6)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`7"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <param name="item6">The value of the sixth component of the tuple.</param>
            <param name="item7">The value of the seventh component of the tuple.</param>
        </member>
        <member name="M:System.Tuple`7.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`7.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`7.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`7.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`7.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="P:System.Tuple`7.Item3">
            <summary>
                Gets the value of the current tuple object's third component.
            </summary>
            <value>
                The value of the current tuple object's third component.
            </value>
        </member>
        <member name="P:System.Tuple`7.Item4">
            <summary>
                Gets the value of the current tuple object's fourth component.
            </summary>
            <value>
                The value of the current tuple object's fourth component.
            </value>
        </member>
        <member name="P:System.Tuple`7.Item5">
            <summary>
                Gets the value of the current tuple object's fifth component.
            </summary>
            <value>
                The value of the current tuple object's fifth component.
            </value>
        </member>
        <member name="P:System.Tuple`7.Item6">
            <summary>
                Gets the value of the current tuple object's sixth component.
            </summary>
            <value>
                The value of the current tuple object's sixth component.
            </value>
        </member>
        <member name="P:System.Tuple`7.Item7">
            <summary>
                Gets the value of the current tuple object's seventh component.
            </summary>
            <value>
                The value of the current tuple object's seventh component.
            </value>
        </member>
        <member name="T:System.Tuple`8">
            <summary>
                Represents an n-tuple, where n is 8 or greater.
            </summary>
            <typeparam name="T1">The type of the first component of the tuple.</typeparam>
            <typeparam name="T2">The type of the second component of the tuple.</typeparam>
            <typeparam name="T3">The type of the third component of the tuple.</typeparam>
            <typeparam name="T4">The type of the fourth component of the tuple.</typeparam>
            <typeparam name="T5">The type of the fifth component of the tuple.</typeparam>
            <typeparam name="T6">The type of the sixth component of the tuple.</typeparam>
            <typeparam name="T7">The type of the seventh component of the tuple.</typeparam>
            <typeparam name="TRest">Any generic Tuple object that defines the types of the tuple's remaining components.</typeparam>
        </member>
        <member name="M:System.Tuple`8.#ctor(`0,`1,`2,`3,`4,`5,`6,`7)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Tuple`8"/> class.
            </summary>
            <param name="item1">The value of the first component of the tuple.</param>
            <param name="item2">The value of the second component of the tuple.</param>
            <param name="item3">The value of the third component of the tuple.</param>
            <param name="item4">The value of the fourth component of the tuple.</param>
            <param name="item5">The value of the fifth component of the tuple.</param>
            <param name="item6">The value of the sixth component of the tuple.</param>
            <param name="item7">The value of the seventh component of the tuple.</param>
            <param name="rest">Any generic Tuple object that contains the values of the tuple's remaining components.</param>
            <exception cref="T:System.ArgumentException">
                rest is not a generic Tuple object.
            </exception>
        </member>
        <member name="M:System.Tuple`8.Equals(System.Object)">
            <summary>
                Returns a value that indicates whether the current tuple object is equal to a specified object.
            </summary>
            <param name="obj">The object to compare with this instance.</param>
            <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
        </member>
        <member name="M:System.Tuple`8.GetHashCode">
            <summary>
                Calculates the hash code for the current tuple object.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:System.Tuple`8.ToString">
            <summary>
                Returns a string that represents the value of this tuple instance.
            </summary>
            <returns>The string representation of this tuple object.</returns>
        </member>
        <member name="P:System.Tuple`8.Item1">
            <summary>
                Gets the value of the current tuple object's first component.
            </summary>
            <value>
                The value of the current tuple object's first component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Item2">
            <summary>
                Gets the value of the current tuple object's second component.
            </summary>
            <value>
                The value of the current tuple object's second component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Item3">
            <summary>
                Gets the value of the current tuple object's third component.
            </summary>
            <value>
                The value of the current tuple object's third component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Item4">
            <summary>
                Gets the value of the current tuple object's fourth component.
            </summary>
            <value>
                The value of the current tuple object's fourth component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Item5">
            <summary>
                Gets the value of the current tuple object's fifth component.
            </summary>
            <value>
                The value of the current tuple object's fifth component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Item6">
            <summary>
                Gets the value of the current tuple object's sixth component.
            </summary>
            <value>
                The value of the current tuple object's sixth component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Item7">
            <summary>
                Gets the value of the current tuple object's seventh component.
            </summary>
            <value>
                The value of the current tuple object's seventh component.
            </value>
        </member>
        <member name="P:System.Tuple`8.Rest">
            <summary>
                Gets the current tuple object's remaining components.
            </summary>
            <value>
                The value of the current tuple object's remaining components.
            </value>
        </member>
    </members>
</doc>
