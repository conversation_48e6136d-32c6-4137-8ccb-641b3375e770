﻿using Entities.Login;
using Entities.Models.WMSTOMES;
using Infrastructure.Configurable;
using Infrastructure.Utilities;
using Log2RabbitMQ;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OutsideService.IQC.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [TestClass]
    public class UnitTest_ForLogHelper
    {
        [TestMethod]
        public void Test_Log2RabbitMQ()
        {
            // var esUtil = new EsUtil(ConfigReader.ESSetting.Hostnames, "c4_webapi_log", ConfigReader.ESSetting.Username, ConfigReader.ESSetting.Password);
            //esUtil.IndexAsync<ApiLogModel>(new ApiLogModel { logLevel = "Interface", message = new Entities.Models.InterfaceLogEntity
            // {
            //     Action = "Test",
            //     Request = "123213"
            // }
            // });


            LogHelper.InterfaceLogInfo(new Entities.Models.InterfaceLogEntity
            {
                Action = "Test",
                Request = "123213"
            });

            Thread.Sleep(1000);
        }

        [TestMethod]
        public void Test_Log2RabbitMQ_WMS()
        {
            // var esUtil = new EsUtil(ConfigReader.ESSetting.Hostnames, "c4_webapi_log", ConfigReader.ESSetting.Username, ConfigReader.ESSetting.Password);
            //esUtil.IndexAsync<ApiLogModel>(new ApiLogModel { logLevel = "Interface", message = new Entities.Models.InterfaceLogEntity
            // {
            //     Action = "Test",
            //     Request = "123213"
            // }
            // });
            //var esUtil = new EsUtil(ConfigReader.ESSetting.Hostnames, "c4_webapi_log", ConfigReader.ESSetting.Username, ConfigReader.ESSetting.Password);
            //esUtil.IndexAsync<ApiLogModel>(new ApiLogModel
            //{
            //    logLevel = "WMS",
            //    message = new WMSInterfaceLogEntity
            //    {
            //        ExecuteTime = 1000,
            //        Request = "test",
            //        RequestDate = DateTime.Now,
            //        Requesturl = "http:132.2321.321.312",
            //        Response = "response test"
            //    }
            //}).GetAwaiter().GetResult();

            LogHelper.WMSRequest(new WMSInterfaceLogEntity
            {
                ExecuteTime = 1000,
                Request = "test",
                RequestDate = DateTime.Now,
                Requesturl = "http:132.2321.321.312",
                Response = "response test"
            });

            Thread.Sleep(100);
        }

        class ApiLogModel : LogsModel
        {

        }
    }
}
