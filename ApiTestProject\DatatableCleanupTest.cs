﻿using Entities.Models.Task;
using Entities.Models.WMSTOMES;
using Infrastructure.Configurable;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using OutsideService.WMS;
using OutsideService.WMS.Models;
using Services;
using Services.AstroenergyRepository;
using Services.AstroenergyService;
using Services.CoreBusRepository;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [TestClass]
    public class DatatableCleanupTest
    {
        private readonly TaskRepository _taskRepository;

        public DatatableCleanupTest()
        {
            _taskRepository = new TaskRepository();
        }
        /// <summary>
        /// 获取清理列表
        /// </summary>
        [TestMethod]
        public void Test_GetDatatableCleanupList()
        {
            List<DatatableCleanup> listTable = _taskRepository.GetDatatableCleanupList().Result;

            Assert.IsNotNull(listTable);
            Assert.IsTrue(listTable.Count > 0);
        }

        /// <summary>
        /// 获取主键
        /// </summary>
        [TestMethod]
        public void Test_GetDatatableCleanupPK()
        {
            string pk = _taskRepository.GetDatatableCleanupPK("EAPWIPMOVEHISTORY").Result;
            Assert.AreEqual(string.Empty, pk);
        }

        /// <summary>
        /// 获取主键
        /// </summary>
        [TestMethod]
        public void Test_GetDatatableCleanupPKs()
        {
            List<string> pk = _taskRepository.GetDatatableCleanupPKs("COLLECTIONIVLXT").Result;
            Assert.IsTrue(pk?.Contains("EQUIPMENT"));
        }

        /// <summary>
        /// 获取主键，如果没有主键
        /// </summary>
        [TestMethod]
        public void Test_GetDatatableCleanupPK_without_PK()
        {
            string pk = _taskRepository.GetDatatableCleanupPK("JAEQPPARAMMAIN").Result;
            Assert.AreEqual("EQPPARAMMAINID", pk);
        }

        [TestMethod]
        public void Test_DeleteDatatableCleanupTop1000ByTime()
        {
            int beforeCnt;
            int beforeCnt_detail;
            //增加一些假数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                //可以是 Dictionary 或者 List<Dictionary >
                var dc = new List<Dictionary<string, object>>();
                var dc_detail = new List<Dictionary<string, object>>();
                for (global::System.Int32 i = 0; i < 10000; i++)
                {
                    dc.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"ACTIONCODE",i.ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                    dc_detail.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"EQPPARAMDETAILID","PK_"+i.ToString() },
                        {"ACTIONCODE",i.ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                }

                db.Deleteable<object>().AS("JAEQPPARAMMAIN").ExecuteCommand();
                db.Deleteable<object>().AS("JAEQPPARAMDETAIL_PK").ExecuteCommand();

                db.Insertable(dc).AS("JAEQPPARAMMAIN").ExecuteCommand();
                db.Insertable(dc_detail).AS("JAEQPPARAMDETAIL_PK").ExecuteCommand();

                beforeCnt = db.Queryable<object>().AS("JAEQPPARAMMAIN", "o").Count();
                beforeCnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_PK", "o").Count();

                Assert.AreEqual(10000, beforeCnt);
                Assert.AreEqual(10000, beforeCnt_detail);
            }

            List<string> vals = _taskRepository.GetDatatableCleanupTop1000ByTime("JAEQPPARAMMAIN", "EQPPARAMMAINID", "INSERTTIME", DatatableCleanupConditionEnum.LESS, "90").GetAwaiter().GetResult();

            Assert.AreEqual(5000, vals.Count());

            List<string> vals_details = _taskRepository.GetDatatableCleanupTop1000ByCondition("JAEQPPARAMDETAIL_PK", "JAEQPPARAMMAIN", "EQPPARAMDETAILID", "EQPPARAMMAINID", DatatableCleanupConditionEnum.IN, vals,"").GetAwaiter().GetResult();

            Assert.AreEqual(5000, vals_details.Count());

            _taskRepository.DeleteDatatableCleanupByPK("JAEQPPARAMMAIN", "EQPPARAMMAINID", vals).GetAwaiter().GetResult();

            _taskRepository.DeleteDatatableCleanupByPK("JAEQPPARAMDETAIL_PK", "EQPPARAMDETAILID", vals_details).GetAwaiter().GetResult();

            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var cnt = db.Queryable<object>().AS("JAEQPPARAMMAIN", "o").Count();
                var cnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_PK", "o").Count();
                Assert.AreEqual(5000, beforeCnt - cnt);
                Assert.AreEqual(5000, beforeCnt_detail - cnt_detail);
            }




        }

        /// <summary>
        /// 主键个数大于1
        /// </summary>
        [TestMethod]
        public void Test_DeleteDatatableCleanupTop1000ByTime_PKs_COUNT_GREATER_THAN_ONE()
        {
            int beforeCnt;
            int beforeCnt_detail;
            //增加一些假数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                //可以是 Dictionary 或者 List<Dictionary >
                var dc = new List<Dictionary<string, object>>();
                var dc_detail = new List<Dictionary<string, object>>();
                for (global::System.Int32 i = 0; i < 10000; i++)
                {
                    dc.Add(new Dictionary<string, object>
                    {
                        {"EQUIPMENT",i.ToString() },
                        {"FILENAME",i.ToString() },
                        {"UniqueID",i.ToString() },
                        {"TESTDATE",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                }

                db.Deleteable<object>().AS("COLLECTIONIVLXT").ExecuteCommand();

                db.Insertable(dc).AS("COLLECTIONIVLXT").ExecuteCommand();

                beforeCnt = db.Queryable<object>().AS("COLLECTIONIVLXT", "o").Count();

                Assert.AreEqual(10000, beforeCnt);
            }

            List<string> pks = _taskRepository.GetDatatableCleanupPKs("COLLECTIONIVLXT").Result;

            List<dynamic> vals = _taskRepository.GetDatatableCleanupTop1000ByTime("COLLECTIONIVLXT", pks, "TESTDATE", DatatableCleanupConditionEnum.LESS, "180").GetAwaiter().GetResult();

            Assert.AreEqual(5000, vals.Count());

            _taskRepository.DeleteDatatableCleanupByPKs("COLLECTIONIVLXT", pks, vals).GetAwaiter().GetResult();

            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var cnt = db.Queryable<object>().AS("COLLECTIONIVLXT", "o").Count();
                Assert.AreEqual(5000, beforeCnt - cnt);
            }
        }

        [TestMethod]
        public void Test_DatabaseCleanup_old()
        {
            int beforeCnt;
            int beforeCnt_detail;
            //增加一些假数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                //可以是 Dictionary 或者 List<Dictionary >
                var dc = new List<Dictionary<string, object>>();
                var dc_detail = new List<Dictionary<string, object>>();
                for (global::System.Int32 i = 0; i < ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 2; i++)
                {
                    dc.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"ACTIONCODE",i.ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                    dc_detail.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"EQPPARAMDETAILID","PK_"+i.ToString() },
                        {"ACTIONCODE",i.ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                    dc_detail.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"EQPPARAMDETAILID","PK_"+(i+ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS*2).ToString() },
                        {"ACTIONCODE",(i+ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS*2).ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                }

                db.Deleteable<object>().AS("JAEQPPARAMMAIN").ExecuteCommand();
                db.Deleteable<object>().AS("JAEQPPARAMDETAIL_PK").ExecuteCommand();
                db.Deleteable<object>().AS("JAEQPPARAMDETAIL_LK").ExecuteCommand();

                db.Insertable(dc).AS("JAEQPPARAMMAIN").ExecuteCommand();
                db.Insertable(dc_detail).AS("JAEQPPARAMDETAIL_PK").ExecuteCommand();
                db.Insertable(dc_detail).AS("JAEQPPARAMDETAIL_LK").ExecuteCommand();

                beforeCnt = db.Queryable<object>().AS("JAEQPPARAMMAIN", "o").Count();
                beforeCnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_PK", "o").Count();
                beforeCnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_LK", "o").Count();

                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 2, beforeCnt);
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 4, beforeCnt_detail);
            }

            TaskService _taskService = new TaskService();

            _taskService.DatabaseCleanup().GetAwaiter().GetResult();

            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var cnt = db.Queryable<object>().AS("JAEQPPARAMMAIN", "o").Count();
                var cnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_PK", "o").Count();
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS, beforeCnt - cnt);
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 2, beforeCnt_detail - cnt_detail);
            }
        }

        [TestMethod]
        public void Test_DatabaseCleanup_new()
        {
            int beforeCnt;
            int beforeCnt_detail;
            int beforeCnt_collect;
            //增加一些假数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                //可以是 Dictionary 或者 List<Dictionary >
                var dc = new List<Dictionary<string, object>>();
                var dc_detail = new List<Dictionary<string, object>>();
                var dc_collect = new List<Dictionary<string, object>>();
                for (global::System.Int32 i = 0; i < ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS*2; i++)
                {
                    dc.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"ACTIONCODE",i.ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                    dc_detail.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"EQPPARAMDETAILID","PK_"+i.ToString() },
                        {"ACTIONCODE",i.ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                    dc_detail.Add(new Dictionary<string, object>
                    {
                        {"EQPPARAMMAINID",i.ToString() },
                        {"EQPPARAMDETAILID","PK_"+(i+ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS*2).ToString() },
                        {"ACTIONCODE",(i+ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS*2).ToString() },
                        {"INSERTTIME",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                    dc_collect.Add(new Dictionary<string, object>
                    {
                        {"EQUIPMENT",i.ToString() },
                        {"FILENAME",i.ToString() },
                        {"UniqueID",i.ToString() },
                        {"TESTDATE",DateTime.Parse("2023-05-08 14:07:14") },
                    });
                }

                db.Deleteable<object>().AS("JAEQPPARAMMAIN").ExecuteCommand();
                db.Deleteable<object>().AS("JAEQPPARAMDETAIL_PK").ExecuteCommand();
                db.Deleteable<object>().AS("JAEQPPARAMDETAIL_LK").ExecuteCommand();
                db.Deleteable<object>().AS("COLLECTIONIVLXT").ExecuteCommand();

                db.Insertable(dc).AS("JAEQPPARAMMAIN").ExecuteCommand();
                db.Insertable(dc_detail).AS("JAEQPPARAMDETAIL_PK").ExecuteCommand();
                db.Insertable(dc_detail).AS("JAEQPPARAMDETAIL_LK").ExecuteCommand();
                db.Insertable(dc_collect).AS("COLLECTIONIVLXT").ExecuteCommand();

                beforeCnt = db.Queryable<object>().AS("JAEQPPARAMMAIN", "o").Count();
                beforeCnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_PK", "o").Count();
                beforeCnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_LK", "o").Count();
                beforeCnt_collect = db.Queryable<object>().AS("COLLECTIONIVLXT", "o").Count();

                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 2, beforeCnt);
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 4, beforeCnt_detail);
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 2, beforeCnt_collect);
            }

            TaskService _taskService = new TaskService();

            _taskService.DatabaseCleanup().GetAwaiter().GetResult();

            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var cnt = db.Queryable<object>().AS("JAEQPPARAMMAIN", "o").Count();
                var cnt_detail = db.Queryable<object>().AS("JAEQPPARAMDETAIL_PK", "o").Count();
                var cnt_collect = db.Queryable<object>().AS("COLLECTIONIVLXT", "o").Count();
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS , beforeCnt - cnt);
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS * 2, beforeCnt_detail - cnt_detail);
                Assert.AreEqual(ConfigReader.ConfigEntity.DatatableCleanupSetting.EACH_CLEANUP_ROWS , beforeCnt_collect - cnt_collect);
            }
        }

        [TestMethod]
        public void Test_SendMail()
        {
            _taskRepository.SaveDatatableCleanupStatus(new DatatableCleanupStatus
            {
                CleanupTable = "JAEQPPARAMDETAIL_PK",
                CLEANUP_STATUS = "0",
                CLEANUP_MSG = "清理失败，表没数据",
                CLEANUP_TIMES = 0
            }).GetAwaiter().GetResult();
            TaskService _taskService = new TaskService();
            _taskService.SendMailWhenCleanupFail().GetAwaiter().GetResult();
        }


        [TestMethod]
        public void Test_SaveDatatableCleanupStatus2()
        {
            _taskRepository.SaveDatatableCleanupStatus(new DatatableCleanupStatus
            {
                CleanupTable = "JAEQPPARAMDETAIL_LK",
                CLEANUP_STATUS = "0",
                CLEANUP_MSG = "LK清理失败，表没数据",
                CLEANUP_TIMES = 5,
                SEND_EMAIL_DATE = DateTime.Now.AddDays(-1),
            }).GetAwaiter().GetResult();
            
            _taskRepository.SaveDatatableCleanupStatus(new DatatableCleanupStatus
            {
                CleanupTable = "JAEQPPARAMDETAIL_LK",
                CLEANUP_STATUS = "0",
                CLEANUP_MSG = "LK清理失败，表没数据",
                CLEANUP_TIMES = 5
            }).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void Test_SaveDatatableCleanupStatus()
        {
            
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                db.Deleteable<DatatableCleanupStatus>(
                     new List<DatatableCleanupStatus>(){
  new DatatableCleanupStatus() { CleanupTable = "JAEQPPARAMDETAIL_LK" },
  new DatatableCleanupStatus() { CleanupTable = "JAEQPPARAMDETAIL_YS" }
}).ExecuteCommand();
                db.Insertable<DatatableCleanupStatus>(new DatatableCleanupStatus() {
                    CleanupTable = "JAEQPPARAMDETAIL_YS",
                    CLEANUP_STATUS = "0",
                    CLEANUP_MSG = "清理1000条",
                    CLEANUP_TIMES = 10
                }).ExecuteCommand();
            }
            //插入
            _taskRepository.SaveDatatableCleanupStatus(new DatatableCleanupStatus
            {
                CleanupTable = "JAEQPPARAMDETAIL_LK",
                CLEANUP_STATUS = "0",
                CLEANUP_MSG = "LK清理失败，表没数据",
                CLEANUP_TIMES = 5
            }).GetAwaiter().GetResult();
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var lk = db.Queryable<DatatableCleanupStatus>().Where(x => x.CleanupTable == "JAEQPPARAMDETAIL_LK").Single();
                Assert.AreEqual("0", lk.CLEANUP_STATUS);
                Assert.AreEqual("LK清理失败，表没数据", lk.CLEANUP_MSG);
                Assert.AreEqual(5,lk.CLEANUP_TIMES);
            }

            //更新
            _taskRepository.SaveDatatableCleanupStatus(new DatatableCleanupStatus
            {
                CleanupTable = "JAEQPPARAMDETAIL_YS",
                CLEANUP_STATUS = "1",
                CLEANUP_MSG = "YS清理失败，表没数据",
                CLEANUP_TIME=DateTime.Now,
                CLEANUP_TIMES = 20
            }).GetAwaiter().GetResult();
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var lk = db.Queryable<DatatableCleanupStatus>().Where(x => x.CleanupTable == "JAEQPPARAMDETAIL_YS").Single();
                Assert.AreEqual("1", lk.CLEANUP_STATUS);
                Assert.AreEqual("YS清理失败，表没数据", lk.CLEANUP_MSG);
                Assert.AreEqual(30, lk.CLEANUP_TIMES);
            }
        }

        [TestMethod]
        public void Test_RebuildIndex()
        {
            //_taskRepository.RebuildDatatableCleanupIndex("IDX_EAPWIPMOVEHISTORY_CARRIERLD").GetAwaiter().GetResult();
            TaskService _taskService = new TaskService();

            _taskService.RebuildDatatableCleanupIndex("JAEQPPARAMDETAIL_YS").GetAwaiter().GetResult();
        }

        [TestMethod]
        public void Test_CleanupPartition()
        {
            TaskService _taskService = new TaskService();
            _taskService.DatabasePartitionCleanup().GetAwaiter().GetResult();
        }

        [TestMethod]
        public void Test_UnitTest()
        {
            TaskService _taskService = new TaskService();
            _taskService.DatabaseCleanup().GetAwaiter().GetResult();
        }
    }
}
