<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections xdt:Transform="InsertIfMissing">
  </configSections> 

  <configSections xdt:Transform="InsertBefore(/configuration/*[1])">
  </configSections>   

  <!-- remove existing entry -->
  <configSections xdt:Locator="XPath(/configuration/configSections[last()])">
      <section name="oracle.manageddataaccess.client" xdt:Transform="Remove" xdt:Locator="Match(name)" />
  </configSections>

  <!-- insert new entry -->
  <configSections xdt:Locator="XPath(/configuration/configSections[last()])">
      <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" xdt:Transform="Insert" />
  </configSections>

  <configSections xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

  <!-- If system.data tag is absent -->
  <system.data xdt:Transform="InsertIfMissing">
    <DbProviderFactories>
    </DbProviderFactories>
  </system.data>

  <!-- If system.data tag is present, but DbProviderFactories tag is absent -->
  <system.data>
    <DbProviderFactories xdt:Transform="InsertIfMissing">
    </DbProviderFactories>
  </system.data>
  
  <!-- remove existing ODPM entry -->  
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" xdt:Transform="Remove" xdt:Locator="Match(invariant)" />
      <add name="ODP.NET, Managed Driver" xdt:Transform="Remove" xdt:Locator="Match(name)" />      
    </DbProviderFactories>
  </system.data>

  <!-- add new ODPM entry -->
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" xdt:Transform="Insert"/>
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" xdt:Transform="Insert" />      
    </DbProviderFactories>
  </system.data>

</configuration>
