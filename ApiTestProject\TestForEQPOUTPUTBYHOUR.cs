using DataAccessor;
using Entities.Models.Eap;
using Infrastructure.Configurable;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Moq;
using Services.Common;

namespace ApiTestProject
{
    [TestClass]
    public class TestForEQPOUTPUTBYHOUR
    {
        public TestForEQPOUTPUTBYHOUR()
        {
            if (!ConfigReader.IsTestEnviroment)
            {
                Assert.Fail();
            }
        }

        /// <summary>
        /// 普通工序产量
        /// </summary>
        [TestMethod]
        public void Test_PrintQtyInfoWithMockedTime()
        {
            // 清理测试数据
            CleanTestData();
            // 清理Redis中的测试数据
            string redisKey = $"EQP_OUTPUT:测试设备";
            var redisClient = DataAccessor.RedisProvider.RedisClient.Init;
            try
            {
                redisClient.DeleteKey(redisKey);
            }
            catch
            {
                // 忽略删除Redis键可能发生的异常
            }

            try
            {
                // 创建测试设备
                string testEquipment = "TEST_EQP_PRINTQTY";


                // 模拟资源定义表数据
                InsertResourceDefForTestEquipment(testEquipment);

                // 测试场景1: 模拟上午6:40时间 - 首次上报
                {
                    var mockTime1 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 6, 40, 0);
                    var mockProvider1 = new Mock<IDateTimeProvider>();
                    mockProvider1.Setup(p => p.Now).Returns(mockTime1);

                    var equipmentService1 = new Services.CoreBusServices.EquipmentService(mockProvider1.Object);
                    var model1 = CreatePrintQtyModel(testEquipment, 100, "0", "0");
                    bool result1 = equipmentService1.PrintQtyInfo(model1);

                    Assert.IsTrue(result1, "首次上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "100");
                    VerifyHourlyOutput("测试设备", "07:30", "100");
                }

                // 测试场景2: 模拟上午7:10时间 - 同一时间段内的数据增长
                {
                    var mockTime2 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 10, 0);
                    var mockProvider2 = new Mock<IDateTimeProvider>();
                    mockProvider2.Setup(p => p.Now).Returns(mockTime2);

                    var equipmentService2 = new Services.CoreBusServices.EquipmentService(mockProvider2.Object);
                    var model2 = CreatePrintQtyModel(testEquipment, 120, "0", "1000");
                    bool result2 = equipmentService2.PrintQtyInfo(model2);

                    Assert.IsTrue(result2, "同一时间段内数据增长上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "120");
                    // 验证同一时间段内的数据更新 - 应该是最新值与上一次的差值+上一次的产量
                    VerifyHourlyOutput("测试设备", "07:30", "120"); //
                }

                // 测试场景3: 模拟上午7:25时间 - 同一时间段内的再次数据增长
                {
                    var mockTime3 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 25, 0);
                    var mockProvider3 = new Mock<IDateTimeProvider>();
                    mockProvider3.Setup(p => p.Now).Returns(mockTime3);

                    var equipmentService3 = new Services.CoreBusServices.EquipmentService(mockProvider3.Object);
                    var model3 = CreatePrintQtyModel(testEquipment, 150, "0", "0");
                    bool result3 = equipmentService3.PrintQtyInfo(model3);

                    Assert.IsTrue(result3, "同一时间段内再次数据增长上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "150");
                    // 验证同一时间段内的数据更新 - 应该是最新值与上一次的差值+上一次的产量
                    VerifyHourlyOutput("测试设备", "07:30", "150"); // 150-120+120
                }

                // 测试场景4: 模拟上午7:40时间 - 跨越到新时间段(7:30-8:30)
                {
                    var mockTime4 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 40, 0);
                    var mockProvider4 = new Mock<IDateTimeProvider>();
                    mockProvider4.Setup(p => p.Now).Returns(mockTime4);

                    var equipmentService4 = new Services.CoreBusServices.EquipmentService(mockProvider4.Object);
                    var model4 = CreatePrintQtyModel(testEquipment, 180, "0", "160");
                    bool result4 = equipmentService4.PrintQtyInfo(model4);

                    Assert.IsTrue(result4, "跨越到新时间段上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "180");
                    // 验证新时间段的数据 - 应该是最新值与上一个时间段最后一笔的差值
                    VerifyHourlyOutput("测试设备", "08:30", "20"); // output-totaloutput  180-160
                    VerifyHourlyOutput("测试设备", "07:30", "160"); // totaloutput-pre output+pre 产量 160-150+150
                }

                // 测试场景5: 模拟上午8:15时间 - 同一时间段内的数据增长
                {
                    var mockTime5 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 15, 0);
                    var mockProvider5 = new Mock<IDateTimeProvider>();
                    mockProvider5.Setup(p => p.Now).Returns(mockTime5);

                    var equipmentService5 = new Services.CoreBusServices.EquipmentService(mockProvider5.Object);
                    var model5 = CreatePrintQtyModel(testEquipment, 200, "0", "160");
                    bool result5 = equipmentService5.PrintQtyInfo(model5);

                    Assert.IsTrue(result5, "同一时间段内数据增长上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "200");
                    // 验证同一时间段内的数据更新
                    VerifyHourlyOutput("测试设备", "08:30", "40"); // 200-180+20=40
                }

                // 测试场景6: 模拟上午8:45时间 - 跨越到新时间段(8:30-9:30)
                {
                    var mockTime6 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 45, 0);
                    var mockProvider6 = new Mock<IDateTimeProvider>();
                    mockProvider6.Setup(p => p.Now).Returns(mockTime6);

                    var equipmentService6 = new Services.CoreBusServices.EquipmentService(mockProvider6.Object);
                    var model6 = CreatePrintQtyModel(testEquipment, 230, "0", "160");
                    bool result6 = equipmentService6.PrintQtyInfo(model6);

                    Assert.IsTrue(result6, "跨越到新时间段上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "230");
                    VerifyHourlyOutput("测试设备", "09:30", "30"); // 230-200=30
                }

                // 测试场景7: 模拟上午9:20时间 - 产量异常清零
                {
                    var mockTime7 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 9, 20, 0);
                    var mockProvider7 = new Mock<IDateTimeProvider>();
                    mockProvider7.Setup(p => p.Now).Returns(mockTime7);

                    var equipmentService7 = new Services.CoreBusServices.EquipmentService(mockProvider7.Object);
                    var model7 = CreatePrintQtyModel(testEquipment, 50, "0", "0");
                    bool result7 = equipmentService7.PrintQtyInfo(model7);

                    Assert.IsTrue(result7, "产量减少上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "50");
                    VerifyHourlyOutput("测试设备", "09:30", "80"); // 异常值处理，50+30
                }

                // 测试场景8: 模拟上午9:40时间 - 异常后恢复增长
                {
                    var mockTime8 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 9, 40, 0);
                    var mockProvider8 = new Mock<IDateTimeProvider>();
                    mockProvider8.Setup(p => p.Now).Returns(mockTime8);

                    var equipmentService8 = new Services.CoreBusServices.EquipmentService(mockProvider8.Object);
                    var model8 = CreatePrintQtyModel(testEquipment, 80, "0", "0");
                    bool result8 = equipmentService8.PrintQtyInfo(model8);

                    Assert.IsTrue(result8, "异常后恢复增长上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "80");
                    VerifyHourlyOutput("测试设备", "10:30", "30"); //跨时间段 80-50
                }

                // 测试场景9: 模拟下午20:20时间 - 班次切换点
                {
                    var mockTime9 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 20, 20, 0);
                    var mockProvider9 = new Mock<IDateTimeProvider>();
                    mockProvider9.Setup(p => p.Now).Returns(mockTime9);

                    var equipmentService9 = new Services.CoreBusServices.EquipmentService(mockProvider9.Object);
                    var model9 = CreatePrintQtyModel(testEquipment, 300, "200", "200");
                    bool result9 = equipmentService9.PrintQtyInfo(model9);

                    Assert.IsTrue(result9, "班次切换时间点上报应返回true");
                    VerifyPrintQtyInfo(testEquipment, "300");
                    VerifyHourlyOutput("测试设备", "20:30", "100"); // 300-200=100
                }
            }
            finally
            {
                // 清理测试数据
                CleanTestData();

                // 清理Redis中的测试数据
                try
                {
                    redisClient.DeleteKey(redisKey);
                }
                catch
                {
                    // 忽略删除Redis键可能发生的异常
                }
            }
        }

        [TestMethod]
        public void Test_PrintQtyInfoWithMultipleDataInSameTimeSlot()
        {
            // 清理测试数据
            CleanTestData();

            try
            {
                // 创建测试设备
                string testEquipment = "TEST_EQP_PRINTQTY_MULTI";

                // 清理Redis中可能存在的测试数据
                string redisKey = $"EQP_OUTPUT:{testEquipment}";
                var redisClient = DataAccessor.RedisProvider.RedisClient.Init;
                redisClient.DeleteKey(redisKey);

                // 模拟资源定义表数据
                InsertResourceDefForTestEquipment(testEquipment);

                // 测试场景1: 模拟上午6:40时间 - 首次上报
                var mockTime1 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 6, 40, 0);
                var mockProvider1 = new Mock<IDateTimeProvider>();
                mockProvider1.Setup(p => p.Now).Returns(mockTime1);

                var equipmentService1 = new Services.CoreBusServices.EquipmentService(mockProvider1.Object);
                var model1 = CreatePrintQtyModel(testEquipment, 100, "0", "0");
                bool result1 = equipmentService1.PrintQtyInfo(model1);

                Assert.IsTrue(result1, "首次上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "100");
                VerifyHourlyOutput(testEquipment, "07:30", "100");

                // 测试场景2: 模拟上午7:10时间 - 同一时间段内的数据增长
                var mockTime2 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 10, 0);
                var mockProvider2 = new Mock<IDateTimeProvider>();
                mockProvider2.Setup(p => p.Now).Returns(mockTime2);

                var equipmentService2 = new Services.CoreBusServices.EquipmentService(mockProvider2.Object);
                var model2 = CreatePrintQtyModel(testEquipment, 120, "0", "0");
                bool result2 = equipmentService2.PrintQtyInfo(model2);

                Assert.IsTrue(result2, "同一时间段内数据增长上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "120");
                VerifyHourlyOutput(testEquipment, "07:30", "20"); // 120-100=20

                // 测试场景3: 模拟上午7:20时间 - 同一时间段内的再次数据增长
                var mockTime3 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 20, 0);
                var mockProvider3 = new Mock<IDateTimeProvider>();
                mockProvider3.Setup(p => p.Now).Returns(mockTime3);

                var equipmentService3 = new Services.CoreBusServices.EquipmentService(mockProvider3.Object);
                var model3 = CreatePrintQtyModel(testEquipment, 150, "0", "0");
                bool result3 = equipmentService3.PrintQtyInfo(model3);

                Assert.IsTrue(result3, "同一时间段内再次数据增长上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "150");
                VerifyHourlyOutput(testEquipment, "07:30", "30"); // 150-120=30

                // 测试场景4: 模拟上午7:35时间 - 跨越到新时间段(7:30-8:30)，这会影响上一个班次的最后产量
                var mockTime4 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 35, 0);
                var mockProvider4 = new Mock<IDateTimeProvider>();
                mockProvider4.Setup(p => p.Now).Returns(mockTime4);

                var equipmentService4 = new Services.CoreBusServices.EquipmentService(mockProvider4.Object);
                var model4 = CreatePrintQtyModel(testEquipment, 180, "150", "150");
                bool result4 = equipmentService4.PrintQtyInfo(model4);

                Assert.IsTrue(result4, "跨越到新时间段上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "180");
                // 验证新时间段的数据 - 应该是最新值与TotalOutputQty的差值
                VerifyHourlyOutput(testEquipment, "08:30", "30"); // 180-150=30

                // 测试场景5: 模拟上午7:45时间 - 同一时间段内的数据增长
                var mockTime5 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 7, 45, 0);
                var mockProvider5 = new Mock<IDateTimeProvider>();
                mockProvider5.Setup(p => p.Now).Returns(mockTime5);

                var equipmentService5 = new Services.CoreBusServices.EquipmentService(mockProvider5.Object);
                var model5 = CreatePrintQtyModel(testEquipment, 200, "150", "150");
                bool result5 = equipmentService5.PrintQtyInfo(model5);

                Assert.IsTrue(result5, "同一时间段内数据增长上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "200");
                // 验证同一时间段内的数据更新
                VerifyHourlyOutput(testEquipment, "08:30", "20"); // 200-180=20

                // 测试场景6: 模拟上午8:00时间 - 同一时间段内的数据增长
                var mockTime6 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 0, 0);
                var mockProvider6 = new Mock<IDateTimeProvider>();
                mockProvider6.Setup(p => p.Now).Returns(mockTime6);

                var equipmentService6 = new Services.CoreBusServices.EquipmentService(mockProvider6.Object);
                var model6 = CreatePrintQtyModel(testEquipment, 220, "150", "150");
                bool result6 = equipmentService6.PrintQtyInfo(model6);

                Assert.IsTrue(result6, "同一时间段内数据增长上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "220");
                VerifyHourlyOutput(testEquipment, "08:30", "20"); // 220-200=20

                // 测试场景7: 模拟上午8:15时间 - 同一时间段内的数据增长
                var mockTime7 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 15, 0);
                var mockProvider7 = new Mock<IDateTimeProvider>();
                mockProvider7.Setup(p => p.Now).Returns(mockTime7);

                var equipmentService7 = new Services.CoreBusServices.EquipmentService(mockProvider7.Object);
                var model7 = CreatePrintQtyModel(testEquipment, 250, "150", "150");
                bool result7 = equipmentService7.PrintQtyInfo(model7);

                Assert.IsTrue(result7, "同一时间段内数据增长上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "250");
                VerifyHourlyOutput(testEquipment, "08:30", "30"); // 250-220=30

                // 测试场景8: 模拟上午8:25时间 - 同一时间段内的数据增长
                var mockTime8 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 25, 0);
                var mockProvider8 = new Mock<IDateTimeProvider>();
                mockProvider8.Setup(p => p.Now).Returns(mockTime8);

                var equipmentService8 = new Services.CoreBusServices.EquipmentService(mockProvider8.Object);
                var model8 = CreatePrintQtyModel(testEquipment, 280, "150", "150");
                bool result8 = equipmentService8.PrintQtyInfo(model8);

                Assert.IsTrue(result8, "同一时间段内数据增长上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "280");
                VerifyHourlyOutput(testEquipment, "08:30", "30"); // 280-250=30

                // 测试场景9: 模拟上午8:35时间 - 跨越到新时间段(8:30-9:30)
                var mockTime9 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 35, 0);
                var mockProvider9 = new Mock<IDateTimeProvider>();
                mockProvider9.Setup(p => p.Now).Returns(mockTime9);

                var equipmentService9 = new Services.CoreBusServices.EquipmentService(mockProvider9.Object);
                var model9 = CreatePrintQtyModel(testEquipment, 300, "150", "150");
                bool result9 = equipmentService9.PrintQtyInfo(model9);

                Assert.IsTrue(result9, "跨越到新时间段上报应返回true");
                VerifyPrintQtyInfo(testEquipment, "300");
                VerifyHourlyOutput(testEquipment, "09:30", "20"); // 300-280=20

                // 验证所有时间段的数据
                using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 获取计算结果
                    var results = db.Ado.SqlQuery<OutputResult>(
                        $"SELECT EQUIPMENT, HOURS1, OUTPUT FROM EQPOUTPUTBYHOUR WHERE EQUIPMENT = '{testEquipment}' ORDER BY HOURS1");

                    Assert.IsNotNull(results, "计算结果不应为空");
                    Assert.AreEqual(3, results.Count, "应该有3个时间段的数据");

                    // 验证每个时间段的最终产量
                    var timePoints = new Dictionary<string, string>
                    {
                        { "07:30", "30" },  // 7:30窗口内(6:30-7:30)的最终产量
                        { "08:30", "30" },  // 8:30窗口内(7:30-8:30)的最终产量
                        { "09:30", "20" }   // 9:30窗口内(8:30-9:30)的最终产量
                    };

                    foreach (var result in results)
                    {
                        if (timePoints.ContainsKey(result.HOURS1))
                        {
                            Assert.AreEqual(timePoints[result.HOURS1], result.OUTPUT,
                                $"时间段{result.HOURS1}的产量计算错误，预期{timePoints[result.HOURS1]}，实际{result.OUTPUT}");
                        }
                    }
                }
            }
            finally
            {
                // 清理测试数据
                CleanTestData();

                // 清理Redis中的测试数据
                string redisKey = $"EQP_OUTPUT:TEST_EQP_PRINTQTY_MULTI";
                var redisClient = DataAccessor.RedisProvider.RedisClient.Init;
                try
                {
                    redisClient.DeleteKey(redisKey);
                }
                catch
                {
                    // 忽略删除Redis键可能发生的异常
                }
            }
        }

        /// <summary>
        /// 丝网产量，测试分选
        /// </summary>
        [TestMethod]
        public void Test_PrintQtyInfoForSilkScreen()
        {
            // 清理测试数据
            CleanTestData();

            try
            {
                // 创建测试设备 - 丝网设备
                string testSilkScreenEquipment = "丝网上料机13#";
                string testSortingEquipment = "测试分选" + testSilkScreenEquipment.Substring(5);

                // 清理Redis中可能存在的测试数据
                string redisKeySilkScreen = $"EQP_OUTPUT:{testSilkScreenEquipment}";
                string redisKeySorting = $"EQP_OUTPUT:{testSortingEquipment}";
                var redisClient = DataAccessor.RedisProvider.RedisClient.Init;
                redisClient.DeleteKey(redisKeySilkScreen);
                redisClient.DeleteKey(redisKeySorting);

                // 模拟资源定义表数据
                InsertResourceDefForTestEquipment(testSilkScreenEquipment, "丝网");

                // 测试场景1: 模拟上午8:15时间 - 首次上报丝网设备数据
                {
                    var mockTime1 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 15, 0);
                    var mockProvider1 = new Mock<IDateTimeProvider>();
                    mockProvider1.Setup(p => p.Now).Returns(mockTime1);

                    var equipmentService1 = new Services.CoreBusServices.EquipmentService(mockProvider1.Object);

                    // 创建丝网设备的数据模型
                    var model1 = new PrintQtyModel.RequestInfo
                    {
                        Equipment = testSilkScreenEquipment,
                        OutputQty = 100,
                        InputQTY = 120, // 丝网设备的投入通常大于产出
                        TotalInputQty = "120",
                        TotalOutputQty = "120",
                        TotalInQty = 120,
                        TotalOutQty = 120,
                        InProcessQty = 20, // 在制品数量 = 投入 - 产出
                        OrderType = "Normal",
                        Shift = "白班",
                        Spec = "丝网", // 关键：设置为丝网工序
                        SubDescription = testSilkScreenEquipment // 设备ID作为子描述
                    };

                    bool result1 = equipmentService1.PrintQtyInfo(model1);

                    Assert.IsTrue(result1, "丝网设备首次上报应返回true");

                    // 验证丝网设备数据
                    VerifyPrintQtyInfo(testSilkScreenEquipment, "100"); // 测试分选设备的OutputQty应该保持原值
                    VerifyHourlyOutput(testSilkScreenEquipment, "08:30", "120"); // 首次上报，小时产量应该等于OutputQty

                    // 验证测试分选设备数据
                    VerifyHourlyOutput(testSortingEquipment, "08:30", "100"); // 首次上报，小时产量应该等于OutputQty
                }

                // 测试场景2: 模拟上午8:45时间 - 丝网设备数据增长
                {
                    var mockTime2 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 8, 45, 0);
                    var mockProvider2 = new Mock<IDateTimeProvider>();
                    mockProvider2.Setup(p => p.Now).Returns(mockTime2);

                    var equipmentService2 = new Services.CoreBusServices.EquipmentService(mockProvider2.Object);

                    // 创建丝网设备的数据模型 - 数据增长
                    var model2 = new PrintQtyModel.RequestInfo
                    {
                        Equipment = testSilkScreenEquipment,
                        OutputQty = 150,
                        InputQTY = 180, // 丝网设备的投入通常大于产出
                        TotalInputQty = "120",
                        TotalOutputQty = "120",
                        TotalInQty = 120,
                        TotalOutQty = 120,
                        InProcessQty = 30, // 在制品数量 = 投入 - 产出
                        OrderType = "Normal",
                        Shift = "白班",
                        Spec = "丝网", // 关键：设置为丝网工序
                        SubDescription = testSilkScreenEquipment // 设备ID作为子描述
                    };

                    bool result2 = equipmentService2.PrintQtyInfo(model2);

                    Assert.IsTrue(result2, "丝网设备数据增长上报应返回true");

                    // 验证丝网设备数据
                    VerifyPrintQtyInfo(testSilkScreenEquipment, "150"); // 测试分选设备的OutputQty应该保持原值
                    VerifyHourlyOutput(testSilkScreenEquipment, "09:30", "60"); // 180-120=60

                    // 验证测试分选设备数据
                    VerifyHourlyOutput(testSortingEquipment, "09:30", "50"); // 150-100=50
                }

                // 测试场景3: 模拟上午9:15时间 - 跨越到新时间段(8:30-9:30)
                {
                    var mockTime3 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 9, 15, 0);
                    var mockProvider3 = new Mock<IDateTimeProvider>();
                    mockProvider3.Setup(p => p.Now).Returns(mockTime3);

                    var equipmentService3 = new Services.CoreBusServices.EquipmentService(mockProvider3.Object);

                    // 创建丝网设备的数据模型 - 跨时间段
                    var model3 = new PrintQtyModel.RequestInfo
                    {
                        Equipment = testSilkScreenEquipment,
                        OutputQty = 200,
                        InputQTY = 240, // 丝网设备的投入通常大于产出
                        TotalInputQty = "120",
                        TotalOutputQty = "120",
                        TotalInQty = 120,
                        TotalOutQty = 120,
                        InProcessQty = 40, // 在制品数量 = 投入 - 产出
                        OrderType = "Normal",
                        Shift = "白班",
                        Spec = "丝网", // 关键：设置为丝网工序
                        SubDescription = testSilkScreenEquipment // 设备ID作为子描述
                    };

                    bool result3 = equipmentService3.PrintQtyInfo(model3);

                    Assert.IsTrue(result3, "丝网设备跨时间段上报应返回true");

                    // 验证丝网设备数据
                    VerifyPrintQtyInfo(testSilkScreenEquipment, "200"); // 测试分选设备的OutputQty应该保持原值
                    VerifyHourlyOutput(testSilkScreenEquipment, "09:30", "120"); // 240-180+60=120

                    // 验证测试分选设备数据
                    VerifyHourlyOutput(testSortingEquipment, "09:30", "100"); // 200-150+50=100
                }

                // 测试场景4: 模拟上午9:45时间 - 产量异常清零
                {
                    var mockTime4 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 9, 45, 0);
                    var mockProvider4 = new Mock<IDateTimeProvider>();
                    mockProvider4.Setup(p => p.Now).Returns(mockTime4);

                    var equipmentService4 = new Services.CoreBusServices.EquipmentService(mockProvider4.Object);

                    // 创建丝网设备的数据模型 - 产量清零
                    var model4 = new PrintQtyModel.RequestInfo
                    {
                        Equipment = testSilkScreenEquipment,
                        OutputQty = 50,
                        InputQTY = 60, // 丝网设备的投入通常大于产出
                        TotalInputQty = "120",
                        TotalOutputQty = "120",
                        TotalInQty = 120,
                        TotalOutQty = 120,
                        InProcessQty = 10, // 在制品数量 = 投入 - 产出
                        OrderType = "Normal",
                        Shift = "白班",
                        Spec = "丝网", // 关键：设置为丝网工序
                        SubDescription = testSilkScreenEquipment // 设备ID作为子描述
                    };

                    bool result4 = equipmentService4.PrintQtyInfo(model4);

                    Assert.IsTrue(result4, "丝网设备产量清零上报应返回true");

                    // 验证丝网设备数据
                    VerifyPrintQtyInfo(testSilkScreenEquipment, "50"); // 测试分选设备的OutputQty应该保持原值
                    VerifyHourlyOutput(testSilkScreenEquipment, "10:30", "60"); // 异常值处理，60+0

                    // 验证测试分选设备数据
                    VerifyHourlyOutput(testSortingEquipment, "10:30", "50"); // 异常值处理，50+0
                }

                // 测试场景5: 模拟上午10:15时间 - 异常后恢复增长
                {
                    var mockTime5 = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 10, 15, 0);
                    var mockProvider5 = new Mock<IDateTimeProvider>();
                    mockProvider5.Setup(p => p.Now).Returns(mockTime5);

                    var equipmentService5 = new Services.CoreBusServices.EquipmentService(mockProvider5.Object);

                    // 创建丝网设备的数据模型 - 恢复增长
                    var model5 = new PrintQtyModel.RequestInfo
                    {
                        Equipment = testSilkScreenEquipment,
                        OutputQty = 80,
                        InputQTY = 100, // 丝网设备的投入通常大于产出
                        TotalInputQty = "120",
                        TotalOutputQty = "120",
                        TotalInQty = 120,
                        TotalOutQty = 120,
                        InProcessQty = 20, // 在制品数量 = 投入 - 产出
                        OrderType = "Normal",
                        Shift = "白班",
                        Spec = "丝网", // 关键：设置为丝网工序
                        SubDescription = testSilkScreenEquipment // 设备ID作为子描述
                    };

                    bool result5 = equipmentService5.PrintQtyInfo(model5);

                    Assert.IsTrue(result5, "丝网设备恢复增长上报应返回true");

                    // 验证丝网设备数据
                    VerifyPrintQtyInfo(testSilkScreenEquipment, "80"); // 测试分选设备的OutputQty应该保持原值
                    VerifyHourlyOutput(testSilkScreenEquipment, "10:30", "100"); // 100-60=40+60=100

                    // 验证测试分选设备数据
                    VerifyHourlyOutput(testSortingEquipment, "10:30", "80"); // 80-50=30+50
                }

                // 验证所有时间段的数据
                using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 获取丝网设备的计算结果
                    var silkScreenResults = db.Ado.SqlQuery<OutputResult>(
                        $"SELECT EQUIPMENT, HOURS1, OUTPUT FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = '{testSilkScreenEquipment}' ORDER BY HOURS1");

                    Assert.IsNotNull(silkScreenResults, "丝网设备计算结果不应为空");
                    Assert.AreEqual(3, silkScreenResults.Count, "丝网设备应该有3个时间段的数据");

                    // 验证丝网设备每个时间段的最终产量
                    var silkScreenTimePoints = new Dictionary<string, string>
                    {
                        { "08:30", "120" },  // 8:30窗口内(7:30-8:30)的最终产量
                        { "09:30", "120" },  // 9:30窗口内(8:30-9:30)的最终产量
                        { "10:30", "100" }   // 10:30窗口内(9:30-10:30)的最终产量
                    };

                    foreach (var result in silkScreenResults)
                    {
                        if (silkScreenTimePoints.ContainsKey(result.HOURS1))
                        {
                            Assert.AreEqual(silkScreenTimePoints[result.HOURS1], result.OUTPUT,
                                $"丝网设备时间段{result.HOURS1}的产量计算错误，预期{silkScreenTimePoints[result.HOURS1]}，实际{result.OUTPUT}");
                        }
                    }

                    // 获取测试分选设备的计算结果
                    var sortingResults = db.Ado.SqlQuery<OutputResult>(
                        $"SELECT EQUIPMENT, HOURS1, OUTPUT FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = '{testSortingEquipment}' ORDER BY HOURS1");

                    Assert.IsNotNull(sortingResults, "测试分选设备计算结果不应为空");
                    Assert.AreEqual(3, sortingResults.Count, "测试分选设备应该有3个时间段的数据");

                    // 验证测试分选设备每个时间段的最终产量
                    var sortingTimePoints = new Dictionary<string, string>
                    {
                        { "08:30", "100" },  // 8:30窗口内(7:30-8:30)的最终产量
                        { "09:30", "100" },  // 9:30窗口内(8:30-9:30)的最终产量
                        { "10:30", "80" }   // 10:30窗口内(9:30-10:30)的最终产量
                    };

                    foreach (var result in sortingResults)
                    {
                        if (sortingTimePoints.ContainsKey(result.HOURS1))
                        {
                            Assert.AreEqual(sortingTimePoints[result.HOURS1], result.OUTPUT,
                                $"测试分选设备时间段{result.HOURS1}的产量计算错误，预期{sortingTimePoints[result.HOURS1]}，实际{result.OUTPUT}");
                        }
                    }
                }
            }
            finally
            {
                // 清理测试数据
                CleanTestData();

                // 清理Redis中的测试数据
                string testSilkScreenEquipment = "丝网上料机13#";
                string testSortingEquipment = "测试分选" + testSilkScreenEquipment.Substring(5);
                string redisKeySilkScreen = $"EQP_OUTPUT:{testSilkScreenEquipment}";
                string redisKeySorting = $"EQP_OUTPUT:{testSortingEquipment}";
                var redisClient = DataAccessor.RedisProvider.RedisClient.Init;
                try
                {
                    redisClient.DeleteKey(redisKeySilkScreen);
                    redisClient.DeleteKey(redisKeySorting);
                }
                catch
                {
                    // 忽略删除Redis键可能发生的异常
                }
            }
        }


        [TestMethod]
        public void Test_BatchDataCalculation()
        {
            // 清理测试数据
            CleanTestData();

            try
            {
                // 插入模拟产量数据
                InsertMockPrintQtyData();

                // 创建EquipmentService实例
                var equipmentService = new Services.CoreBusServices.EquipmentService();

                // 由于没有批量计算方法，我们模拟对每个设备的最后一条数据进行产量计算
                using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 获取测试设备列表
                    var testEquipments = new string[] { "TEST_EQP1", "TEST_EQP2", "TEST_EQP3" };

                    foreach (var equipment in testEquipments)
                    {
                        // 获取设备最后一条数据
                        var lastRecord = db.Ado.SqlQuerySingle<dynamic>(
                            "SELECT TOP 1 Equipment, OutputQty, CREATEDATE FROM JAPRINTQTYINFO WHERE Equipment = @eqp ORDER BY CREATEDATE DESC",
                            new { eqp = equipment });

                        if (lastRecord != null)
                        {
                            // 创建PrintQtyModel并调用产量计算
                            var model = CreatePrintQtyModel(
                                equipment,
                                Convert.ToInt32(lastRecord.OutputQty),
                                lastRecord.OutputQty.ToString(),
                                lastRecord.OutputQty.ToString());

                            // 设置创建时间为数据库中的时间
                            var mockProvider = new Mock<IDateTimeProvider>();
                            mockProvider.Setup(p => p.Now).Returns(Convert.ToDateTime(lastRecord.CREATEDATE));

                            var service = new Services.CoreBusServices.EquipmentService(mockProvider.Object);
                            bool result = service.PrintQtyInfo(model);

                            Assert.IsTrue(result, $"设备{equipment}的产量计算应返回true");
                        }
                    }
                }

                // 验证计算结果
                VerifyCalculationResults();
            }
            finally
            {
                // 清理测试数据
                CleanTestData();
            }
        }

        /// <summary>
        /// 使用真实数据测试产量计算 - 从JAPRINTQTYINFO表获取C04LDA01设备数据
        /// </summary>
        [TestMethod]
        public void Test_RealDataFromJAPRINTQTYINFO()
        {
            // 清理测试数据
            CleanTestData();

            try
            {
                // 为C04LDA01设备插入资源定义（如果需要的话）
                InsertResourceDefForTestEquipment("C04LDA01");

                using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 查询C04LDA01设备在2025-05-26之后的数据
                    var realDataRecords = db.Ado.SqlQuery<dynamic>(
                        @"SELECT Equipment, InputQTY, OutputQty, CREATEDATE,
                                 COALESCE(TotalInputQty, '0') as TotalInputQty,
                                 COALESCE(TotalOutputQty, '0') as TotalOutputQty,
                                 COALESCE(TotalInQty, 0) as TotalInQty,
                                 COALESCE(TotalOutQty, 0) as TotalOutQty
                          FROM JAPRINTQTYINFO j
                          WHERE j.EQUIPMENT = 'C04LDA01'
                            AND j.CREATEDATE > TO_DATE('2025-05-26 00:00:00','yyyy-mm-dd hh24:mi:ss')
                          ORDER BY j.CREATEDATE");

                    if (realDataRecords == null || realDataRecords.Count == 0)
                    {
                        Assert.Inconclusive("没有找到C04LDA01设备在2025-05-26之后的数据，无法进行测试");
                        return;
                    }

                    Console.WriteLine($"找到 {realDataRecords.Count} 条C04LDA01设备的数据记录");

                    // 依次处理每条数据
                    for (int i = 0; i < realDataRecords.Count; i++)
                    {
                        var record = realDataRecords[i];

                        Console.WriteLine($"处理第 {i + 1} 条数据: Equipment={record.Equipment}, " +
                                        $"OutputQty={record.OutputQty}, CreateDate={record.CREATEDATE}");

                        // 创建模拟时间提供者，使用记录中的创建时间
                        var mockTimeProvider = new Mock<IDateTimeProvider>();
                        mockTimeProvider.Setup(p => p.Now).Returns(Convert.ToDateTime(record.CREATEDATE));

                        // 创建EquipmentService实例
                        var equipmentService = new Services.CoreBusServices.EquipmentService(mockTimeProvider.Object);

                        // 创建PrintQtyModel.RequestInfo对象
                        var model = new PrintQtyModel.RequestInfo
                        {
                            Equipment = record.Equipment.ToString(),
                            InputQTY = Convert.ToInt32(record.InputQTY ?? 0),
                            OutputQty = Convert.ToInt32(record.OutputQty ?? 0),
                            TotalInputQty = record.TotalInputQty?.ToString() ?? "0",
                            TotalOutputQty = record.TotalOutputQty?.ToString() ?? "0",
                            TotalInQty = Convert.ToInt32(record.TotalInQty ?? 0),
                            TotalOutQty = Convert.ToInt32(record.TotalOutQty ?? 0),
                            InProcessQty = 0, // 默认值
                            OrderType = "Normal",
                            Shift = DateTime.Now.Hour >= 8 && DateTime.Now.Hour < 20 ? "白班" : "夜班",
                            CreateDate = Convert.ToDateTime(record.CREATEDATE)
                        };

                        // 调用产量计算方法
                        bool result = equipmentService.PrintQtyInfo(model);

                        // 验证结果
                        Assert.IsTrue(result, $"第 {i + 1} 条数据处理失败: Equipment={record.Equipment}, " +
                                            $"OutputQty={record.OutputQty}, CreateDate={record.CREATEDATE}");

                        Console.WriteLine($"第 {i + 1} 条数据处理成功");
                    }

                    Console.WriteLine($"所有 {realDataRecords.Count} 条数据处理完成");

                    // 验证EQPOUTPUTBYHOUR表中是否有相应的计算结果
                    var outputResults = db.Ado.SqlQuery<dynamic>(
                        "SELECT EQUIPMENT, HOURS1, OUTPUT, NOWDAY FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = 'C04LDA01' ORDER BY NOWDAY, HOURS1");

                    if (outputResults != null && outputResults.Count > 0)
                    {
                        Console.WriteLine($"EQPOUTPUTBYHOUR2表中生成了 {outputResults.Count} 条计算结果:");
                        foreach (var result in outputResults)
                        {
                            Console.WriteLine($"  Equipment={result.EQUIPMENT}, Hours={result.HOURS1}, " +
                                            $"Output={result.OUTPUT}, NowDay={result.NOWDAY}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("EQPOUTPUTBYHOUR2表中没有生成计算结果");
                    }
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"测试过程中发生异常: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                // 清理测试数据（包括C04LDA01的数据）
                CleanTestDataIncludingC04LDA01();
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        private void CleanTestData()
        {
            // 定义所有测试设备名称
            string[] testEquipments = new string[] {
                "TEST_EQP_PRINTQTY",
                "TEST_EQP_PRINTQTY_MULTI",
                "TEST_EQP1",
                "TEST_EQP2",
                "TEST_EQP3",
                "丝网上料机13#",
                "测试分选13#",
                "测试设备"
            };

            using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
            {
                foreach (var testEquipment in testEquipments)
                {
                    // 删除JAPRINTQTYINFO表中的测试数据
                    db.Ado.ExecuteCommand($"DELETE FROM JAPRINTQTYINFO WHERE Equipment = '{testEquipment}'");

                    // 删除EQPOUTPUTBYHOUR表中的测试数据
                    db.Ado.ExecuteCommand($"DELETE FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = '{testEquipment}'");
                }
            }

            try
            {
                using (var db = new BaseRepository(DBProvider.MesDbProvider).GetSqlSugarClient())
                {
                    // 删除资源定义表中的测试数据
                    db.Ado.ExecuteCommand("DELETE FROM resourcedef WHERE resourcename LIKE 'TEST_EQP%'");
                }
            }
            catch
            {
                // 忽略资源定义表清理失败的异常
            }
        }

        /// <summary>
        /// 清理测试数据（包括C04LDA01设备）
        /// </summary>
        private void CleanTestDataIncludingC04LDA01()
        {
            // 定义所有测试设备名称（包括C04LDA01）
            string[] testEquipments = new string[] {
                "TEST_EQP_PRINTQTY",
                "TEST_EQP_PRINTQTY_MULTI",
                "TEST_EQP1",
                "TEST_EQP2",
                "TEST_EQP3",
                "丝网上料机13#",
                "测试分选13#",
                "测试设备",
                "C04LDA01"
            };

            using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
            {
                foreach (var testEquipment in testEquipments)
                {
                    // 删除EQPOUTPUTBYHOUR表中的测试数据（注意：不删除JAPRINTQTYINFO中的C04LDA01数据，因为那是真实数据）
                    if (testEquipment == "C04LDA01")
                    {
                        // 只删除EQPOUTPUTBYHOUR表中的计算结果，保留原始数据
                        db.Ado.ExecuteCommand($"DELETE FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = '{testEquipment}'");
                    }
                    else
                    {
                        // 删除JAPRINTQTYINFO表中的测试数据
                        db.Ado.ExecuteCommand($"DELETE FROM JAPRINTQTYINFO WHERE Equipment = '{testEquipment}'");
                        // 删除EQPOUTPUTBYHOUR表中的测试数据
                        db.Ado.ExecuteCommand($"DELETE FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = '{testEquipment}'");
                    }
                }
            }

            try
            {
                using (var db = new BaseRepository(DBProvider.MesDbProvider).GetSqlSugarClient())
                {
                    // 删除资源定义表中的测试数据（包括C04LDA01）
                    db.Ado.ExecuteCommand("DELETE FROM resourcedef WHERE resourcename LIKE 'TEST_EQP%' OR resourcename = 'C04LDA01'");
                    // 删除ResourceGroupEntries表中的测试数据
                    db.Ado.ExecuteCommand("DELETE FROM ResourceGroupEntries WHERE ENTRIESID IN (SELECT resourceid FROM resourcedef WHERE resourcename LIKE 'TEST_EQP%' OR resourcename = 'C04LDA01')");
                }
            }
            catch
            {
                // 忽略资源定义表清理失败的异常
            }
        }

        /// <summary>
        /// 插入模拟产量数据
        /// </summary>
        private void InsertMockPrintQtyData()
        {
            using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
            {
                // 获取当前日期，用于创建测试数据
                DateTime today = DateTime.Today;

                // 模拟白班（8:30-19:30）的设备数据
                // 测试场景1：正常数据连续上报（首小时）- 设备1
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(6).AddMinutes(10), "3");   // 6:10，在6:30窗口内(5:30-6:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(6).AddMinutes(40), "5");   // 6:40，在7:30窗口内(6:30-7:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(7).AddMinutes(20), "10");  // 7:20，在7:30窗口内(6:30-7:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(8).AddMinutes(10), "50");  // 8:10，在8:30窗口内(7:30-8:30)（白班首小时）

                // 测试场景2：中间时段正常数据 - 设备1
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(8).AddMinutes(40), "60");  // 8:40，在9:30窗口内(8:30-9:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(9).AddMinutes(10), "80");  // 9:10，在9:30窗口内(8:30-9:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(9).AddMinutes(40), "100"); // 9:40，在10:30窗口内(9:30-10:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(10).AddMinutes(10), "120"); // 10:10，在10:30窗口内(9:30-10:30)

                // 测试场景3：异常清零 - 设备2
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(6).AddMinutes(10), "2");   // 6:10，在6:30窗口内(5:30-6:30)
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(7).AddMinutes(20), "5");   // 7:20，在7:30窗口内(6:30-7:30)
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(8).AddMinutes(20), "35");  // 8:20，在8:30窗口内(7:30-8:30)（白班首小时）
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(8).AddMinutes(40), "45");  // 8:40，在9:30窗口内(8:30-9:30)
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(9).AddMinutes(10), "60");  // 9:10，在9:30窗口内(8:30-9:30)
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(9).AddMinutes(30), "10");  // 9:30，在10:30窗口内(9:30-10:30)，异常清零
                InsertPrintQtyData(db, "TEST_EQP2", today.AddHours(9).AddMinutes(45), "25");  // 9:45，在10:30窗口内(9:30-10:30)，恢复增长

                // 测试场景4：最后一个小时 - 设备1
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(18).AddMinutes(10), "400"); // 18:10，在18:30窗口内(17:30-18:30)
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(19).AddMinutes(10), "420"); // 19:10，在19:30窗口内(18:30-19:30)（白班最后小时）
                InsertPrintQtyData(db, "TEST_EQP1", today.AddHours(19).AddMinutes(40), "440"); // 19:40，在20:30窗口内(19:30-20:30)（晚班首小时）

                // 测试场景5：数据缺失 - 设备3（只有部分小时有数据）
                InsertPrintQtyData(db, "TEST_EQP3", today.AddHours(6).AddMinutes(10), "8");   // 6:10，在6:30窗口内(5:30-6:30)
                InsertPrintQtyData(db, "TEST_EQP3", today.AddHours(7).AddMinutes(10), "15");  // 7:10，在7:30窗口内(6:30-7:30)

                // 模拟资源定义表数据，用于理论产能计算
                InsertResourceDefData();
            }
        }

        /// <summary>
        /// 插入单条产量数据
        /// </summary>
        private void InsertPrintQtyData(SqlSugarClient db, string equipmentId, DateTime createDate, string outputQty)
        {
            // 构建插入SQL
            string sql = @"INSERT INTO JAPRINTQTYINFO(Equipment, InputQTY, OutputQty, CREATEDATE)
                          VALUES(:EquipmentID, :InputQTY, :OutputQty, :CREATEDATE)";

            // 执行插入
            db.Ado.ExecuteCommand(sql, new
            {
                EquipmentID = equipmentId,
                InputQTY = outputQty,  // 简化处理，输入=输出
                OutputQty = outputQty,
                CREATEDATE = createDate
            });
        }

        /// <summary>
        /// 插入资源定义数据
        /// </summary>
        private void InsertResourceDefData()
        {
            try
            {
                using (var db = new BaseRepository(DBProvider.MesDbProvider).GetSqlSugarClient())
                {
                    // 检查是否已存在测试资源定义数据
                    var existingData = db.Ado.SqlQuerySingle<int>("SELECT COUNT(1) FROM resourcedef WHERE resourcename LIKE 'TEST_EQP%'");

                    if (existingData == 0)
                    {
                        // 插入测试资源定义数据
                        string sql = @"INSERT INTO resourcedef(resourceid,resourcename, description, trc_cap)
                                  VALUES(:resourceid,:resourcename, :description, :trc_cap)";

                        // 插入测试设备1的理论产能
                        db.Ado.ExecuteCommand(sql, new
                        {
                            resourceid = "5880168000000004",
                            resourcename = "TEST_EQP1",
                            description = "测试设备1",
                            trc_cap = 100 // 理论产能100，实际使用时会乘以1.15
                        });

                        // 插入测试设备2的理论产能
                        db.Ado.ExecuteCommand(sql, new
                        {
                            resourceid = "5880168000000005",
                            resourcename = "TEST_EQP2",
                            description = "测试设备2",
                            trc_cap = 50 // 理论产能50，实际使用时会乘以1.15
                        });

                        // 插入测试设备3的理论产能
                        db.Ado.ExecuteCommand(sql, new
                        {
                            resourceid = "5880168000000006",
                            resourcename = "TEST_EQP3",
                            description = "测试设备3",
                            trc_cap = 30 // 理论产能30，实际使用时会乘以1.15
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("插入资源定义数据失败: " + ex.Message);
                // 如果插入失败，不影响测试继续执行
            }
        }

        /// <summary>
        /// 验证计算结果
        /// </summary>
        private void VerifyCalculationResults()
        {
            using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
            {
                // 获取计算结果
                var results = db.Ado.SqlQuery<OutputResult>(
                    "SELECT EQUIPMENT, HOURS1, OUTPUT FROM EQPOUTPUTBYHOUR WHERE EQUIPMENT LIKE 'TEST_EQP%' ORDER BY EQUIPMENT, HOURS1");

                Assert.IsNotNull(results, "计算结果不应为空");

                // 预期结果集合 - 根据新的时间窗口逻辑调整预期值
                Dictionary<string, Dictionary<string, int>> expectedResults = new Dictionary<string, Dictionary<string, int>>
                {
                    // 设备1的预期结果
                    {
                        "TEST_EQP1", new Dictionary<string, int>
                        {
                            { "06:30", 3 },   // 6:30窗口内(5:30-6:30)的数据，最后一笔为3
                            { "07:30", 10 },  // 7:30窗口内(6:30-7:30)的数据，最后一笔为10，与前一窗口差值10-3=7
                            { "08:30", 50 },  // 8:30窗口内(7:30-8:30)的数据（白班首小时），直接使用窗口内最后一笔的值50
                            { "09:30", 80 },  // 9:30窗口内(8:30-9:30)最后一笔(80)与8:30窗口内最后一笔(50)差值：80-50=30
                            { "10:30", 120 }, // 10:30窗口内(9:30-10:30)最后一笔(120)与9:30窗口内最后一笔(80)差值：120-80=40
                            { "18:30", 400 }, // 18:30窗口内(17:30-18:30)最后一笔为400
                            { "19:30", 420 }, // 19:30窗口内(18:30-19:30)最后一笔（白班最后小时）为420，与18:30差值：420-400=20
                            { "20:30", 440 }, // 20:30窗口内(19:30-20:30)最后一笔（晚班首小时）为440，与19:30差值：440-420=20
                        }
                    },
                    // 设备2的预期结果
                    {
                        "TEST_EQP2", new Dictionary<string, int>
                        {
                            { "06:30", 2 },   // 6:30窗口内(5:30-6:30)的数据，最后一笔为2
                            { "07:30", 5 },   // 7:30窗口内(6:30-7:30)的数据，最后一笔为5，与前一窗口差值5-2=3
                            { "08:30", 35 },  // 8:30窗口内(7:30-8:30)的数据（白班首小时），直接使用窗口内最后一笔的值35
                            { "09:30", 60 },  // 9:30窗口内(8:30-9:30)最后一笔(60)与8:30窗口内最后一笔(35)差值：60-35=25
                            { "10:30", 25 },  // 10:30窗口内(9:30-10:30)数据，异常清零，使用修正计算：最大值25-前一末笔60+异常后首值10=25-60+10=-25，取0
                        }
                    },
                    // 设备3的预期结果
                    {
                        "TEST_EQP3", new Dictionary<string, int>
                        {
                            { "06:30", 8 },   // 6:30窗口内(5:30-6:30)的数据，最后一笔为8
                            { "07:30", 15 },  // 7:30窗口内(6:30-7:30)的数据，最后一笔为15，与前一窗口差值15-8=7
                        }
                    }
                };

                // 验证每个设备的计算结果
                foreach (var result in results)
                {
                    // 检查设备是否在预期结果中
                    if (expectedResults.ContainsKey(result.EQUIPMENT))
                    {
                        var expectedEquipmentResults = expectedResults[result.EQUIPMENT];

                        // 检查该时间点是否有预期结果
                        if (expectedEquipmentResults.ContainsKey(result.HOURS1))
                        {
                            int expectedOutput = expectedEquipmentResults[result.HOURS1];
                            int actualOutput = int.Parse(result.OUTPUT);

                            // 验证产量计算是否正确
                            Assert.AreEqual(expectedOutput, actualOutput,
                                $"设备{result.EQUIPMENT}在{result.HOURS1}的产量计算错误，预期{expectedOutput}，实际{actualOutput}");

                            // 从预期结果中移除已验证的项
                            expectedEquipmentResults.Remove(result.HOURS1);
                        }
                        else
                        {
                            Assert.Fail($"设备{result.EQUIPMENT}在{result.HOURS1}没有预期结果");
                        }

                        // 如果该设备的所有预期结果都已验证，从预期结果中移除该设备
                        if (expectedEquipmentResults.Count == 0)
                        {
                            expectedResults.Remove(result.EQUIPMENT);
                        }
                    }
                    else
                    {
                        Assert.Fail($"计算结果中包含非预期设备：{result.EQUIPMENT}");
                    }
                }

                // 验证所有预期结果都已验证
                foreach (var equipment in expectedResults.Keys)
                {
                    foreach (var hour in expectedResults[equipment].Keys)
                    {
                        Assert.Fail($"设备{equipment}在{hour}的预期结果未被计算");
                    }
                }
            }
        }

        /// <summary>
        /// 创建测试用的PrintQtyModel.RequestInfo对象
        /// </summary>
        private PrintQtyModel.RequestInfo CreatePrintQtyModel(string equipment, int outputQty, string totalInputQty, string totalOutputQty)
        {
            return new PrintQtyModel.RequestInfo
            {
                Equipment = equipment,
                OutputQty = outputQty,
                InputQTY = outputQty, // 简化处理，输入=输出
                TotalInputQty = totalInputQty,
                TotalOutputQty = totalOutputQty,
                InProcessQty = 0,
                OrderType = "Normal",
                Shift = DateTime.Now.Hour >= 8 && DateTime.Now.Hour < 20 ? "白班" : "夜班"
            };
        }

        /// <summary>
        /// 获取当前时间点（整点后30分钟格式）
        /// </summary>
        private string GetCurrentTimePoint()
        {
            DateTime now = DateTime.Now;
            DateTime timePoint = new DateTime(now.Year, now.Month, now.Day, now.Hour, 30, 0);
            if (now.Minute < 30)
            {
                timePoint = timePoint.AddHours(-1);
            }
            return timePoint.ToString("HH:mm");
        }

        /// <summary>
        /// 为测试设备插入资源定义，可指定工序
        /// </summary>
        private void InsertResourceDefForTestEquipment(string equipmentName, string spec = null)
        {
            try
            {
                using (var db = new BaseRepository(DBProvider.MesDbProvider).GetSqlSugarClient())
                {
                    // 检查是否已存在测试资源定义数据
                    var existingData = db.Ado.SqlQuerySingle<int>($"SELECT COUNT(1) FROM resourcedef WHERE resourcename = '{equipmentName}'");

                    if (existingData == 0)
                    {
                        // 生成一个随机的资源ID
                        string resourceId = Guid.NewGuid().ToString().Replace("-", "").Substring(0, 16);

                        // 插入测试资源定义数据
                        string sql = @"INSERT INTO resourcedef(resourceid, resourcename, description, trc_cap, LIFETIME,FACTORYID,RESOURCEFAMILYID)
                                      VALUES(:resourceid, :resourcename, :description, :trc_cap,  :lifetime,:factoryid,:resourcefamilyid)";

                        db.Ado.ExecuteCommand(sql, new
                        {
                            resourceid = resourceId,
                            resourcename = equipmentName,
                            description = spec == "丝网" ? $"丝网上料机13#" : "测试设备",
                            trc_cap = 100,
                            lifetime = "产能", // 设置为产能，确保会调用产量计算
                            factoryid = "0004e2800000004d", // 设置为默认工厂，确保会调用产量计算
                            resourcefamilyid = spec == "丝网" ? "48804680000000b5" : "4880468000000099" // 设置为默认设备，确保会调用产量计算
                        });

                        // 先查询ResourceGroupEntries中最大值
                        var maxSequence = db.Ado.SqlQuerySingle<int>($"SELECT MAX(SEQUENCE) FROM ResourceGroupEntries WHERE RESOURCEGROUPID = :resourcegroupid",
                        new { resourcegroupid = spec == "丝网" ? "4880208000000067" : "488020800000001c" }); // 设置为默认设备，确保会调用产量计算
                        int sequence = maxSequence + 1;

                        // 插入到ResourceGroupEntries表，确保会调用产量计算
                        db.Ado.ExecuteCommand("INSERT INTO ResourceGroupEntries(ENTRIESID, RESOURCEGROUPID,FIELDID,SEQUENCE) VALUES(:entriesid, :resourcegroupid,:fieldid,:sequence)", new
                        {
                            entriesid = resourceId,
                            resourcegroupid = spec == "丝网" ? "4880208000000067" : "488020800000001c", // 设置为默认设备，确保会调用产量计算
                            fieldid = "160", // 设置为默认设备，确保会调用产量计算
                            sequence // 设置为默认设备，确保会调用产量计算
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"插入资源定义数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证JAPRINTQTYINFO表中的数据
        /// </summary>
        private void VerifyPrintQtyInfo(string equipment, string expectedOutputQty)
        {
            using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
            {
                var result = db.Ado.SqlQuerySingle<string>(
                    $"SELECT OutputQty FROM JAPRINTQTYINFO WHERE Equipment = '{equipment}' ORDER BY CREATEDATE DESC");

                Assert.IsNotNull(result, $"设备{equipment}在JAPRINTQTYINFO表中没有记录");
                Assert.AreEqual(expectedOutputQty, result, $"设备{equipment}的OutputQty不正确，预期{expectedOutputQty}，实际{result}");
            }
        }

        /// <summary>
        /// 验证EQPOUTPUTBYHOUR表中的小时产量数据
        /// </summary>
        private void VerifyHourlyOutput(string equipment, string timePoint, string expectedOutput)
        {
            using (var db = new BaseRepository(DBProvider.MidDbProvider).GetSqlSugarClient())
            {
                var result = db.Ado.SqlQuerySingle<string>(
                    $"SELECT OUTPUT FROM EQPOUTPUTBYHOUR2 WHERE EQUIPMENT = '{equipment}' AND HOURS1 = '{timePoint}' ORDER BY CREATEDATE DESC");

                Assert.IsNotNull(result, $"设备{equipment}在{timePoint}没有产量记录");
                Assert.AreEqual(expectedOutput, result, $"设备{equipment}在{timePoint}的产量计算错误，预期{expectedOutput}，实际{result}");
            }
        }

        /// <summary>
        /// 创建具有不同输入输出量的PrintQtyModel.RequestInfo对象（用于测试丝网工序）
        /// </summary>
        private PrintQtyModel.RequestInfo CreatePrintQtyModelWithDifferentIO(string equipment, int inputQty, int outputQty, string totalInputQty, string totalOutputQty)
        {
            return new PrintQtyModel.RequestInfo
            {
                Equipment = equipment,
                OutputQty = outputQty,
                InputQTY = inputQty, // 丝网工序的输入量通常大于输出量
                TotalInputQty = totalInputQty,
                TotalOutputQty = totalOutputQty,
                InProcessQty = 0,
                OrderType = "Normal",
                Spec = "丝网", // 设置工序为丝网
                SubDescription = equipment + "_丝网",
                Shift = DateTime.Now.Hour >= 8 && DateTime.Now.Hour < 20 ? "白班" : "夜班"
            };
        }


        /// <summary>
        /// 产量计算结果类
        /// </summary>
        private class OutputResult
        {
            public string EQUIPMENT { get; set; }
            public string HOURS1 { get; set; }
            public string OUTPUT { get; set; }
        }

    }
}
