<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Castle.Core</name>
    </assembly>
    <members>
        <member name="T:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter">
            <summary>
            Abstract adapter for the <see cref="T:System.Collections.IDictionary"/> support
            needed by the <see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory"/>
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Add(System.Object,System.Object)">
            <summary>
            Adds an element with the provided key and value to the <see cref="T:System.Collections.IDictionary"></see> object.
            </summary>
            <param name="key">The <see cref="T:System.Object"></see> to use as the key of the element to add.</param>
            <param name="value">The <see cref="T:System.Object"></see> to use as the value of the element to add.</param>
            <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.IDictionary"></see> object. </exception>
            <exception cref="T:System.ArgumentNullException">key is null. </exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.IDictionary"></see> is read-only.-or- The <see cref="T:System.Collections.IDictionary"></see> has a fixed size. </exception>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Clear">
            <summary>
            Removes all elements from the <see cref="T:System.Collections.IDictionary"></see> object.
            </summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.IDictionary"></see> object is read-only. </exception>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Contains(System.Object)">
            <summary>
            Determines whether the <see cref="T:System.Collections.IDictionary"></see> object contains an element with the specified key.
            </summary>
            <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary"></see> object.</param>
            <returns>
            true if the <see cref="T:System.Collections.IDictionary"></see> contains an element with the key; otherwise, false.
            </returns>
            <exception cref="T:System.ArgumentNullException">key is null. </exception>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.GetEnumerator">
            <summary>
            Returns an <see cref="T:System.Collections.IDictionaryEnumerator"></see> object for the <see cref="T:System.Collections.IDictionary"></see> object.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IDictionaryEnumerator"></see> object for the <see cref="T:System.Collections.IDictionary"></see> object.
            </returns>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.IsFixedSize">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"></see> object has a fixed size.
            </summary>
            <returns>true if the <see cref="T:System.Collections.IDictionary"></see> object has a fixed size; otherwise, false.</returns>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"></see> object is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.IDictionary"></see> object is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Keys">
            <summary>
            Gets an <see cref="T:System.Collections.ICollection"></see> object containing the keys of the <see cref="T:System.Collections.IDictionary"></see> object.
            </summary>
            <returns>An <see cref="T:System.Collections.ICollection"></see> object containing the keys of the <see cref="T:System.Collections.IDictionary"></see> object.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Remove(System.Object)">
            <summary>
            Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary"></see> object.
            </summary>
            <param name="key">The key of the element to remove.</param>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.IDictionary"></see> object is read-only.-or- The <see cref="T:System.Collections.IDictionary"></see> has a fixed size. </exception>
            <exception cref="T:System.ArgumentNullException">key is null. </exception>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Values">
            <summary>
            Gets an <see cref="T:System.Collections.ICollection"></see> object containing the values in the <see cref="T:System.Collections.IDictionary"></see> object.
            </summary>
            <returns>An <see cref="T:System.Collections.ICollection"></see> object containing the values in the <see cref="T:System.Collections.IDictionary"></see> object.</returns>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Item(System.Object)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified key.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.ICollection"></see> to an <see cref="T:System.Array"></see>, starting at a particular <see cref="T:System.Array"></see> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"></see> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"></see>. The <see cref="T:System.Array"></see> must have zero-based indexing.</param>
            <param name="index">The zero-based index in array at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">array is null. </exception>
            <exception cref="T:System.ArgumentException">The type of the source <see cref="T:System.Collections.ICollection"></see> cannot be cast automatically to the type of the destination array. </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">index is less than zero. </exception>
            <exception cref="T:System.ArgumentException">array is multidimensional.-or- index is equal to or greater than the length of array.-or- The number of elements in the source <see cref="T:System.Collections.ICollection"></see> is greater than the available space from index to the end of the destination array. </exception>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.ICollection"></see>.
            </summary>
            <returns>The number of elements contained in the <see cref="T:System.Collections.ICollection"></see>.</returns>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.IsSynchronized">
            <summary>
            Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection"></see> is synchronized (thread safe).
            </summary>
            <returns>true if access to the <see cref="T:System.Collections.ICollection"></see> is synchronized (thread safe); otherwise, false.</returns>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.SyncRoot">
            <summary>
            Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"></see>.
            </summary>
            <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"></see>.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapter.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"></see> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.AbstractDictionaryAdapterVisitor">
            <summary>
            Abstract implementation of <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryAdapterVisitor"/>.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.ComponentAttribute">
            <summary>
            Identifies a property should be represented as a nested component.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.ComponentAttribute.NoPrefix">
            <summary>
            Applies no prefix.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.ComponentAttribute.Prefix">
            <summary>
            Gets or sets the prefix.
            </summary>
            <value>The prefix.</value>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.DictionaryAdapterAttribute">
            <summary>
            Identifies the dictionary adapter types.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.DictionaryBehaviorAttribute">
            <summary>
            Assigns a specific dictionary key.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.FetchAttribute">
            <summary>
            Identifies an interface or property to be pre-fetched.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.FetchAttribute.#ctor">
            <summary>
            Instructs fetching to occur.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.FetchAttribute.#ctor(System.Boolean)">
            <summary>
            Instructs fetching according to <paramref name="fetch"/>
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.FetchAttribute.Fetch">
            <summary>
            Gets whether or not fetching should occur.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.GroupAttribute">
            <summary>
            Assigns a property to a group.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.GroupAttribute.#ctor(System.Object)">
            <summary>
            Constructs a group assignment.
            </summary>
            <param name="group">The group name.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.GroupAttribute.#ctor(System.Object[])">
            <summary>
            Constructs a group assignment.
            </summary>
            <param name="group">The group name.</param>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.GroupAttribute.Group">
            <summary>
            Gets the group the property is assigned to.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IfExistsAttribute">
            <summary>
            Suppresses any on-demand behaviors.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.KeyAttribute">
            <summary>
            Assigns a specific dictionary key.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.KeyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.KeyAttribute"/> class.
            </summary>
            <param name="key">The key.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.KeyAttribute.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.KeyAttribute"/> class.
            </summary>
            <param name="keys">The compound key.</param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.KeyPrefixAttribute">
            <summary>
            Assigns a prefix to the keyed properties of an interface.
            </summary>
            <remarks>
            Key prefixes are not inherited by sub-interfaces.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.KeyPrefixAttribute.#ctor">
            <summary>
            Initializes a default instance of the <see cref="T:Castle.Components.DictionaryAdapter.KeyPrefixAttribute"/> class.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.KeyPrefixAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.KeyPrefixAttribute"/> class.
            </summary>
            <param name="keyPrefix">The prefix for the keyed properties of the interface.</param>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.KeyPrefixAttribute.KeyPrefix">
            <summary>
            Gets the prefix key added to the properties of the interface.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.KeySubstitutionAttribute">
            <summary>
            Substitutes part of key with another string.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.KeySubstitutionAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.KeySubstitutionAttribute"/> class.
            </summary>
            <param name="oldValue">The old value.</param>
            <param name="newValue">The new value.</param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.MultiLevelEditAttribute">
            <summary>
            Requests support for multi-level editing.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.NewGuidAttribute">
            <summary>
            Generates a new GUID on demand.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.OnDemandAttribute">
            <summary>
            Support for on-demand value resolution.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.ReferenceAttribute">
            <summary>
            Specifies assignment by reference rather than by copying.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.RemoveIfAttribute">
            <summary>
            Removes a property if matches value.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.RemoveIfEmptyAttribute">
            <summary>
            Removes a property if null or empty string, guid or collection.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.StringFormatAttribute">
            <summary>
            Provides simple string formatting from existing properties.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.StringFormatAttribute.Format">
            <summary>
            Gets the string format.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.StringFormatAttribute.Properties">
            <summary>
            Gets the format properties.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.StringListAttribute">
            <summary>
            Identifies a property should be represented as a delimited string value.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.StringListAttribute.Separator">
            <summary>
            Gets the separator.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.StringValuesAttribute">
            <summary>
            Converts all properties to strings.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.StringValuesAttribute.Format">
            <summary>
            Gets or sets the format.
            </summary>
            <value>The format.</value>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.SuppressNotificationsAttribute">
            <summary>
            Suppress property change notifications.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.TypeKeyPrefixAttribute">
            <summary>
            Assigns a prefix to the keyed properties using the interface name.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.VolatileAttribute">
            <summary>
            Indicates that underlying values are changeable and should not be cached.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.Xml.XmlAdapter.#ctor(Castle.Components.DictionaryAdapter.Xml.IXmlNode,Castle.Components.DictionaryAdapter.Xml.XmlReferenceManager)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.Xml.XmlAdapter"/> class
            that represents a child object in a larger object graph.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.DefaultPropertyGetter">
            <summary>
            Manages conversion between property values.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DefaultPropertyGetter.#ctor(System.ComponentModel.TypeConverter)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.DefaultPropertyGetter"/> class.
            </summary>
            <param name="converter">The converter.</param>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.DefaultPropertyGetter.ExecutionOrder">
            <summary>
            
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DefaultPropertyGetter.GetPropertyValue(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,System.Object,Castle.Components.DictionaryAdapter.PropertyDescriptor,System.Boolean)">
            <summary>
            Gets the effective dictionary value.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The key.</param>
            <param name="storedValue">The stored value.</param>
            <param name="property">The property.</param>
            <param name="ifExists">true if return only existing.</param>
            <returns>The effective property value.</returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory">
            <summary>
            Uses Reflection.Emit to expose the properties of a dictionary
            through a dynamic implementation of a typed interface.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter``1(System.Collections.IDictionary)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter(System.Type,System.Collections.IDictionary)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter(System.Type,System.Collections.IDictionary,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter``2(System.Collections.Generic.IDictionary{System.String,``1})">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter``1(System.Type,System.Collections.Generic.IDictionary{System.String,``0})">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter``1(System.Collections.Specialized.NameValueCollection)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter(System.Type,System.Collections.Specialized.NameValueCollection)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter``1(System.Xml.XmlNode)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapter(System.Type,System.Xml.XmlNode)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapterMeta(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapterMeta(System.Type,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <inheritdoc />
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.DictionaryAdapterFactory.GetAdapterMeta(System.Type,Castle.Components.DictionaryAdapter.DictionaryAdapterMeta)">
            <inheritdoc />
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.DynamicDictionary">
            <summary>
            Wraps a <see cref="T:System.Collections.IDictionary"/> with a dynamic object to expose a bit better looking API.
            The implementation is trivial and assumes keys are <see cref="T:System.String"/>s.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryAdapter">
            <summary>
            Contract for manipulating the Dictionary adapter.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory">
            <summary>
            Defines the contract for building typed dictionary adapters.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter``1(System.Collections.IDictionary)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <typeparam name="T">The typed interface.</typeparam>
            <param name="dictionary">The underlying source of properties.</param>
            <returns>An implementation of the typed interface bound to the dictionary.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter(System.Type,System.Collections.IDictionary)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <param name="type">The typed interface.</param>
            <param name="dictionary">The underlying source of properties.</param>
            <returns>An implementation of the typed interface bound to the dictionary.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter(System.Type,System.Collections.IDictionary,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <param name="type">The typed interface.</param>
            <param name="dictionary">The underlying source of properties.</param>
            <param name="descriptor">The property descriptor.</param>
            <returns>An implementation of the typed interface bound to the dictionary.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter``1(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Collections.Specialized.NameValueCollection"/>.
            </summary>
            <typeparam name="T">The typed interface.</typeparam>
            <param name="nameValues">The underlying source of properties.</param>
            <returns>An implementation of the typed interface bound to the namedValues.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter(System.Type,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Collections.Specialized.NameValueCollection"/>.
            </summary>
            <param name="type">The typed interface.</param>
            <param name="nameValues">The underlying source of properties.</param>
            <returns>An implementation of the typed interface bound to the namedValues.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter``1(System.Xml.XmlNode)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Xml.XmlNode"/>.
            </summary>
            <typeparam name="T">The typed interface.</typeparam>
            <param name="xmlNode">The underlying source of properties.</param>
            <returns>An implementation of the typed interface bound to the <see cref="T:System.Xml.XmlNode"/>.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapter(System.Type,System.Xml.XmlNode)">
            <summary>
            Gets a typed adapter bound to the <see cref="T:System.Xml.XmlNode"/>.
            </summary>
            <param name="type">The typed interface.</param>
            <param name="xmlNode">The underlying source of properties.</param>
            <returns>An implementation of the typed interface bound to the <see cref="T:System.Xml.XmlNode"/>.</returns>
            <remarks>
            The type represented by T must be an interface with properties.
            </remarks>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapterMeta(System.Type)">
            <summary>
            Gets the <see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterMeta"/> associated with the type.
            </summary>
            <param name="type">The typed interface.</param>
            <returns>The adapter meta-data.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapterMeta(System.Type,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Gets the <see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterMeta"/> associated with the type.
            </summary>
            <param name="type">The typed interface.</param>
            <param name="descriptor">The property descriptor.</param>
            <returns>The adapter meta-data.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory.GetAdapterMeta(System.Type,Castle.Components.DictionaryAdapter.DictionaryAdapterMeta)">
            <summary>
            Gets the <see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterMeta"/> associated with the type.
            </summary>
            <param name="type">The typed interface.</param>
            <param name="other">Another <see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterMeta"/> from which to copy behaviors.</param>
            <returns>The adapter meta-data.</returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryAdapterVisitor">
            <summary>
            Contract for traversing a <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryAdapter"/>.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryBehavior">
            <summary>
            Defines the contract for customizing dictionary access.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.IDictionaryBehavior.ExecutionOrder">
            <summary>
            Determines relative order to apply related behaviors.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryBehavior.Copy">
            <summary>
            Copies the dictionary behavior.
            </summary>
            <returns>null if should not be copied.  Otherwise copy.</returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryBehaviorBuilder">
            <summary>
            Defines the contract for building <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryBehavior"/>s.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryBehaviorBuilder.BuildBehaviors">
            <summary>
            Builds the dictionary behaviors.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryCreate">
            <summary>
            Contract for creating additional Dictionary adapters.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryEdit">
            <summary>
            Contract for editing the Dictionary adapter.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryInitializer">
            <summary>
             Contract for dictionary initialization.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryInitializer.Initialize(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.Object[])">
            <summary>
            Performs any initialization of the <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryAdapter"/>
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="behaviors">The dictionary behaviors.</param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryKeyBuilder">
            <summary>
            Defines the contract for building typed dictionary keys.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryKeyBuilder.GetKey(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Builds the specified key.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The current key.</param>
            <param name="property">The property.</param>
            <returns>The updated key</returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryMetaInitializer">
            <summary>
             Contract for dictionary meta-data initialization.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryMetaInitializer.Initialize(Castle.Components.DictionaryAdapter.IDictionaryAdapterFactory,Castle.Components.DictionaryAdapter.DictionaryAdapterMeta)">
            <summary>
            	Initializes the given <see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterMeta"/> object.
            </summary>
            <param name="factory">The dictionary adapter factory.</param>
            <param name="dictionaryMeta">The dictionary adapter meta.</param>
            
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryMetaInitializer.ShouldHaveBehavior(System.Object)">
            <summary>
            	Determines whether the given behavior should be included in a new
            	<see cref="T:Castle.Components.DictionaryAdapter.DictionaryAdapterMeta"/> object.
            </summary>
            <param name="behavior">A dictionary behavior or annotation.</param>
            <returns>True if the behavior should be included; otherwise, false.</returns>
            <remarks>
            	<see cref="T:Castle.Components.DictionaryAdapter.IDictionaryMetaInitializer"/> behaviors are always included,
            	regardless of the result of this method.
            </remarks>
            
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryNotify">
            <summary>
            Contract for managing Dictionary adapter notifications.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryPropertyGetter">
            <summary>
            Defines the contract for retrieving dictionary values.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryPropertyGetter.GetPropertyValue(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,System.Object,Castle.Components.DictionaryAdapter.PropertyDescriptor,System.Boolean)">
            <summary>
            Gets the effective dictionary value.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The key.</param>
            <param name="storedValue">The stored value.</param>
            <param name="property">The property.</param>
            <param name="ifExists">true if return only existing.</param>
            <returns>The effective property value.</returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryPropertySetter">
            <summary>
            Defines the contract for updating dictionary values.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryPropertySetter.SetPropertyValue(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,System.Object@,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Sets the stored dictionary value.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The key.</param>
            <param name="value">The stored value.</param>
            <param name="property">The property.</param>
            <returns>true if the property should be stored.</returns>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryValidate">
            <summary>
            Contract for validating Dictionary adapter.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDictionaryValidator">
            <summary>
            Contract for dictionary validation.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryValidator.IsValid(Castle.Components.DictionaryAdapter.IDictionaryAdapter)">
            <summary>
            Determines if <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryAdapter"/> is valid.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <returns>true if valid.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryValidator.Validate(Castle.Components.DictionaryAdapter.IDictionaryAdapter)">
            <summary>
            Validates the <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryAdapter"/>.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <returns>The error summary information.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryValidator.Validate(Castle.Components.DictionaryAdapter.IDictionaryAdapter,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Validates the <see cref="T:Castle.Components.DictionaryAdapter.IDictionaryAdapter"/> for a property.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="property">The property to validate.</param>
            <returns>The property summary information.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IDictionaryValidator.Invalidate(Castle.Components.DictionaryAdapter.IDictionaryAdapter)">
            <summary>
            Invalidates any results cached by the validator.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IPropertyDescriptorInitializer">
            <summary>
             Contract for property descriptor initialization.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.IPropertyDescriptorInitializer.Initialize(Castle.Components.DictionaryAdapter.PropertyDescriptor,System.Object[])">
            <summary>
            Performs any initialization of the <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/>
            </summary>
            <param name="propertyDescriptor">The property descriptor.</param>
            <param name="behaviors">The property behaviors.</param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter.#ctor(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter"/> class.
            </summary>
            <param name="nameValues">The name values.</param>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"></see> object is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.IDictionary"></see> object is read-only; otherwise, false.</returns>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter.Contains(System.Object)">
            <summary>
            Determines whether the <see cref="T:System.Collections.IDictionary"></see> object contains an element with the specified key.
            </summary>
            <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary"></see> object.</param>
            <returns>
            true if the <see cref="T:System.Collections.IDictionary"></see> contains an element with the key; otherwise, false.
            </returns>
            <exception cref="T:System.ArgumentNullException">key is null. </exception>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter.Item(System.Object)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified key.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.NameValueCollectionAdapter.Adapt(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Adapts the specified name values.
            </summary>
            <param name="nameValues">The name values.</param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.PropertyDescriptor">
            <summary>
            Describes a dictionary property.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.#ctor">
            <summary>
            Initializes an empty <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/> class.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.#ctor(System.Reflection.PropertyInfo,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/> class.
            </summary>
            <param name="property">The property.</param>
            <param name="annotations">The annotations.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.#ctor(System.Object[])">
            <summary>
            Initializes a new instance <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/> class.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.#ctor(Castle.Components.DictionaryAdapter.PropertyDescriptor,System.Boolean)">
            <summary>
             Copies an existing instance of the <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/> class.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.ExecutionOrder">
            <summary>
            
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.PropertyName">
            <summary>
            Gets the property name.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.PropertyType">
            <summary>
            Gets the property type.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Property">
            <summary>
            Gets the property.
            </summary>
            <value>The property.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.IsDynamicProperty">
            <summary>
            Returns true if the property is dynamic.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.State">
            <summary>
            Gets additional state.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Fetch">
            <summary>
            Determines if property should be fetched.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.IfExists">
            <summary>
            Determines if property must exist first.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.SuppressNotifications">
            <summary>
            Determines if notifications should occur.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Annotations">
            <summary>
            Gets the property behaviors.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.TypeConverter">
            <summary>
            Gets the type converter.
            </summary>
            <value>The type converter.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.ExtendedProperties">
            <summary>
            Gets the extended properties.
            </summary>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Behaviors">
            <summary>
            Gets the setter.
            </summary>
            <value>The setter.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.KeyBuilders">
            <summary>
            Gets the key builders.
            </summary>
            <value>The key builders.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Setters">
            <summary>
            Gets the setter.
            </summary>
            <value>The setter.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Getters">
            <summary>
            Gets the getter.
            </summary>
            <value>The getter.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.Initializers">
            <summary>
            Gets the initializers.
            </summary>
            <value>The initializers.</value>
        </member>
        <member name="P:Castle.Components.DictionaryAdapter.PropertyDescriptor.MetaInitializers">
            <summary>
            Gets the meta-data initializers.
            </summary>
            <value>The meta-data initializers.</value>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.GetKey(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Gets the key.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The key.</param>
            <param name="descriptor">The descriptor.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.GetPropertyValue(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,System.Object,Castle.Components.DictionaryAdapter.PropertyDescriptor,System.Boolean)">
            <summary>
            Gets the property value.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The key.</param>
            <param name="storedValue">The stored value.</param>
            <param name="descriptor">The descriptor.</param>
            <param name="ifExists">true if return only existing.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.SetPropertyValue(Castle.Components.DictionaryAdapter.IDictionaryAdapter,System.String,System.Object@,Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Sets the property value.
            </summary>
            <param name="dictionaryAdapter">The dictionary adapter.</param>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <param name="descriptor">The descriptor.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.AddBehavior(Castle.Components.DictionaryAdapter.IDictionaryBehavior)">
            <summary>
            Adds a single behavior.
            </summary>
            <param name="behavior">The behavior.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.AddBehaviors(Castle.Components.DictionaryAdapter.IDictionaryBehavior[])">
            <summary>
            Adds the behaviors.
            </summary>
            <param name="behaviors">The behaviors.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.AddBehaviors(System.Collections.Generic.IEnumerable{Castle.Components.DictionaryAdapter.IDictionaryBehavior})">
            <summary>
            Adds the behaviors.
            </summary>
            <param name="behaviors">The behaviors.</param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.CopyBehaviors(Castle.Components.DictionaryAdapter.PropertyDescriptor)">
            <summary>
            Copies the behaviors to the other <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/>
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.PropertyDescriptor.Copy">
            <summary>
            Copies the <see cref="T:Castle.Components.DictionaryAdapter.PropertyDescriptor"/>
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.BindingList`1">
            <summary>
              Provides a generic collection that supports data binding.
            </summary>
            <remarks>
              This class wraps the CLR <see cref="T:System.ComponentModel.BindingList`1"/>
              in order to implement the Castle-specific <see cref="T:Castle.Components.DictionaryAdapter.IBindingList`1"/>.
            </remarks>
            <typeparam name="T">The type of elements in the list.</typeparam>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.BindingList`1.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.BindingList`1"/> class
              using default values.
            </summary>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.BindingList`1.#ctor(System.Collections.Generic.IList{`0})">
            <summary>
              Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.BindingList`1"/> class
              with the specified list.
            </summary>
            <param name="list">
              An <see cref="T:System.Collections.Generic.IList`1"/> of items
              to be contained in the <see cref="T:Castle.Components.DictionaryAdapter.BindingList`1"/>.
            </param>
        </member>
        <member name="M:Castle.Components.DictionaryAdapter.BindingList`1.#ctor(System.ComponentModel.BindingList{`0})">
            <summary>
              Initializes a new instance of the <see cref="T:Castle.Components.DictionaryAdapter.BindingList`1"/> class
              wrapping the specified <see cref="T:System.ComponentModel.BindingList`1"/> instance.
            </summary>
            <param name="list">
              A <see cref="T:System.ComponentModel.BindingList`1"/>
              to be wrapped by the <see cref="T:Castle.Components.DictionaryAdapter.BindingList`1"/>.
            </param>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.ICondition">
            <summary>
            Contract for value matching.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDynamicValue">
            <summary>
            Contract for dynamic value resolution.
            </summary>
        </member>
        <member name="T:Castle.Components.DictionaryAdapter.IDynamicValue`1">
            <summary>
            Contract for typed dynamic value resolution.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Castle.Core.Configuration.AbstractConfiguration">
            <summary>
              This is an abstract <see cref = "T:Castle.Core.Configuration.IConfiguration" /> implementation
              that deals with methods that can be abstracted away
              from underlying implementations.
            </summary>
            <remarks>
              <para><b>AbstractConfiguration</b> makes easier to implementers 
                to create a new version of <see cref = "T:Castle.Core.Configuration.IConfiguration" /></para>
            </remarks>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Attributes">
            <summary>
              Gets node attributes.
            </summary>
            <value>
              All attributes of the node.
            </value>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Children">
            <summary>
              Gets all child nodes.
            </summary>
            <value>The <see cref = "T:Castle.Core.Configuration.ConfigurationCollection" /> of child nodes.</value>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Name">
            <summary>
              Gets the name of the <see cref = "T:Castle.Core.Configuration.IConfiguration" />.
            </summary>
            <value>
              The Name of the <see cref = "T:Castle.Core.Configuration.IConfiguration" />.
            </value>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Value">
            <summary>
              Gets the value of <see cref = "T:Castle.Core.Configuration.IConfiguration" />.
            </summary>
            <value>
              The Value of the <see cref = "T:Castle.Core.Configuration.IConfiguration" />.
            </value>
        </member>
        <member name="M:Castle.Core.Configuration.AbstractConfiguration.GetValue(System.Type,System.Object)">
            <summary>
              Gets the value of the node and converts it
              into specified <see cref = "T:System.Type" />.
            </summary>
            <param name = "type">The <see cref = "T:System.Type" /></param>
            <param name = "defaultValue">
              The Default value returned if the conversion fails.
            </param>
            <returns>The Value converted into the specified type.</returns>
        </member>
        <member name="T:Castle.Core.Configuration.ConfigurationCollection">
            <summary>
            A collection of <see cref="T:Castle.Core.Configuration.IConfiguration"/> objects.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.ConfigurationCollection.#ctor">
            <summary>
            Creates a new instance of <c>ConfigurationCollection</c>.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.ConfigurationCollection.#ctor(System.Collections.Generic.IEnumerable{Castle.Core.Configuration.IConfiguration})">
            <summary>
            Creates a new instance of <c>ConfigurationCollection</c>.
            </summary>
        </member>
        <member name="T:Castle.Core.Configuration.IConfiguration">
            <summary>
            <see cref="T:Castle.Core.Configuration.IConfiguration"/> is a interface encapsulating a configuration node
            used to retrieve configuration values.
            </summary>
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Name">
            <summary>
            Gets the name of the node.
            </summary>
            <value>
            The Name of the node.
            </value> 
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Value">
            <summary>
            Gets the value of the node.
            </summary>
            <value>
            The Value of the node.
            </value> 
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Children">
            <summary>
            Gets an <see cref="T:Castle.Core.Configuration.ConfigurationCollection"/> of <see cref="T:Castle.Core.Configuration.IConfiguration"/>
            elements containing all node children.
            </summary>
            <value>The Collection of child nodes.</value>
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Attributes">
            <summary>
            Gets an <see cref="T:System.Collections.IDictionary"/> of the configuration attributes.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.IConfiguration.GetValue(System.Type,System.Object)">
            <summary>
            Gets the value of the node and converts it 
            into specified <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/></param>
            <param name="defaultValue">
            The Default value returned if the conversion fails.
            </param>
            <returns>The Value converted into the specified type.</returns>
        </member>
        <member name="M:Castle.Core.Configuration.MutableConfiguration.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.Configuration.MutableConfiguration"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Castle.Core.Configuration.MutableConfiguration.Value">
            <summary>
            Gets the value of <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </summary>
            <value>
            The Value of the <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </value>
        </member>
        <member name="M:Castle.Core.Configuration.Xml.XmlConfigurationDeserializer.Deserialize(System.Xml.XmlNode)">
            <summary>
              Deserializes the specified node into an abstract representation of configuration.
            </summary>
            <param name = "node">The node.</param>
        </member>
        <member name="M:Castle.Core.Configuration.Xml.XmlConfigurationDeserializer.GetConfigValue(System.String)">
            <summary>
              If a config value is an empty string we return null, this is to keep
              backward compatibility with old code
            </summary>
        </member>
        <member name="T:Castle.Core.Internal.AttributesUtil">
            <summary>
              Helper class for retrieving attributes.
            </summary>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetAttribute``1(System.Type)">
            <summary>
            Gets the attribute.
            </summary>
            <param name="type">The type.</param>
            <returns>The type attribute.</returns>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetAttributes``1(System.Type)">
            <summary>
            Gets the attributes. Does not consider inherited attributes!
            </summary>
            <param name="type">The type.</param>
            <returns>The type attributes.</returns>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetAttribute``1(System.Reflection.MemberInfo)">
            <summary>
            Gets the attribute.
            </summary>
            <param name="member">The member.</param>
            <returns>The member attribute.</returns>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetAttributes``1(System.Reflection.MemberInfo)">
            <summary>
            Gets the attributes. Does not consider inherited attributes!
            </summary>
            <param name="member">The member.</param>
            <returns>The member attributes.</returns>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetTypeAttribute``1(System.Type)">
            <summary>
              Gets the type attribute.
            </summary>
            <param name = "type">The type.</param>
            <returns>The type attribute.</returns>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetTypeAttributes``1(System.Type)">
            <summary>
              Gets the type attributes.
            </summary>
            <param name = "type">The type.</param>
            <returns>The type attributes.</returns>
        </member>
        <member name="M:Castle.Core.Internal.AttributesUtil.GetTypeConverter(System.Reflection.MemberInfo)">
            <summary>
            Gets the type converter.
            </summary>
            <param name="member">The member.</param>
        </member>
        <member name="F:Castle.Core.Internal.InternalsVisible.ToCastleCore">
            <summary>
              Constant to use when making assembly internals visible to Castle.Core 
              <c>[assembly: InternalsVisibleTo(CoreInternalsVisible.ToCastleCore)]</c>
            </summary>
        </member>
        <member name="F:Castle.Core.Internal.InternalsVisible.ToDynamicProxyGenAssembly2">
            <summary>
              Constant to use when making assembly internals visible to proxy types generated by DynamicProxy. Required when proxying internal types.
              <c>[assembly: InternalsVisibleTo(CoreInternalsVisible.ToDynamicProxyGenAssembly2)]</c>
            </summary>
        </member>
        <member name="M:Castle.Core.Internal.TypeExtensions.GetBestName(System.Type)">
            <summary>
            Find the best available name to describe a type.
            </summary>
            <remarks>
            Usually the best name will be <see cref="P:System.Type.FullName"/>, but
            sometimes that's null (see http://msdn.microsoft.com/en-us/library/system.type.fullname%28v=vs.110%29.aspx)
            in which case the method falls back to <see cref="P:System.Reflection.MemberInfo.Name"/>.
            </remarks>
            <param name="type">the type to name</param>
            <returns>the best name</returns>
        </member>
        <member name="T:Castle.Core.IServiceEnabledComponent">
            <summary>
            Defines that the implementation wants a 
            <see cref="T:System.IServiceProvider"/> in order to 
            access other components. The creator must be aware
            that the component might (or might not) implement 
            the interface.
            </summary>
            <remarks>
            Used by Castle Project components to, for example, 
            gather logging factories
            </remarks>
        </member>
        <member name="T:Castle.Core.IServiceProviderEx">
            <summary>
            Increments <c>IServiceProvider</c> with a generic service resolution operation.
            </summary>
        </member>
        <member name="T:Castle.Core.IServiceProviderExAccessor">
            <summary>
            This interface should be implemented by classes
            that are available in a bigger context, exposing
            the container to different areas in the same application.
            <para>
            For example, in Web application, the (global) HttpApplication
            subclasses should implement this interface to expose 
            the configured container
            </para>
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Create(System.Type)">
            <summary>
              Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Create(System.String)">
            <summary>
              Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.Type)">
            <summary>
              Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.String)">
            <summary>
              Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.AbstractExtendedLoggerFactory.GetConfigFile(System.String)">
            <summary>
              Gets the configuration file.
            </summary>
            <param name = "fileName">i.e. log4net.config</param>
        </member>
        <member name="M:Castle.Core.Logging.AbstractLoggerFactory.GetConfigFile(System.String)">
            <summary>
              Gets the configuration file.
            </summary>
            <param name = "fileName">i.e. log4net.config</param>
        </member>
        <member name="T:Castle.Core.Logging.ConsoleLogger">
            <summary>
            The Logger sending everything to the standard output streams.
            This is mainly for the cases when you have a utility that
            does not have a logger to supply.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor">
            <summary>
              Creates a new ConsoleLogger with the <c>Level</c>
              set to <c>LoggerLevel.Debug</c> and the <c>Name</c>
              set to <c>string.Empty</c>.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor(Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new ConsoleLogger with the <c>Name</c>
              set to <c>string.Empty</c>.
            </summary>
            <param name = "logLevel">The logs Level.</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor(System.String)">
            <summary>
              Creates a new ConsoleLogger with the <c>Level</c>
              set to <c>LoggerLevel.Debug</c>.
            </summary>
            <param name = "name">The logs Name.</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new ConsoleLogger.
            </summary>
            <param name = "name">The logs Name.</param>
            <param name = "logLevel">The logs Level.</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.Log(Castle.Core.Logging.LoggerLevel,System.String,System.String,System.Exception)">
            <summary>
              A Common method to log.
            </summary>
            <param name = "loggerLevel">The level of logging</param>
            <param name = "loggerName">The name of the logger</param>
            <param name = "message">The Message</param>
            <param name = "exception">The Exception</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.CreateChildLogger(System.String)">
            <summary>
              Returns a new <c>ConsoleLogger</c> with the name
              added after this loggers name, with a dot in between.
            </summary>
            <param name = "loggerName">The added hierarchical name.</param>
            <returns>A new <c>ConsoleLogger</c>.</returns>
        </member>
        <member name="T:Castle.Core.Logging.DiagnosticsLogger">
            <summary>
              The Logger using standard Diagnostics namespace.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.DiagnosticsLogger.#ctor(System.String)">
            <summary>
              Creates a logger based on <see cref = "T:System.Diagnostics.EventLog" />.
            </summary>
            <param name = "logName"><see cref = "P:System.Diagnostics.EventLog.Log" /></param>
        </member>
        <member name="M:Castle.Core.Logging.DiagnosticsLogger.#ctor(System.String,System.String)">
            <summary>
              Creates a logger based on <see cref = "T:System.Diagnostics.EventLog" />.
            </summary>
            <param name = "logName"><see cref = "P:System.Diagnostics.EventLog.Log" /></param>
            <param name = "source"><see cref = "P:System.Diagnostics.EventLog.Source" /></param>
        </member>
        <member name="M:Castle.Core.Logging.DiagnosticsLogger.#ctor(System.String,System.String,System.String)">
            <summary>
              Creates a logger based on <see cref = "T:System.Diagnostics.EventLog" />.
            </summary>
            <param name = "logName"><see cref = "P:System.Diagnostics.EventLog.Log" /></param>
            <param name = "machineName"><see cref = "P:System.Diagnostics.EventLog.MachineName" /></param>
            <param name = "source"><see cref = "P:System.Diagnostics.EventLog.Source" /></param>
        </member>
        <member name="T:Castle.Core.Logging.IContextProperties">
            <summary>
              Interface for Context Properties implementations
            </summary>
            <remarks>
              <para>
                This interface defines a basic property get set accessor.
              </para>
              <para>
                Based on the ContextPropertiesBase of log4net, by Nicko Cadell.
              </para>
            </remarks>
        </member>
        <member name="P:Castle.Core.Logging.IContextProperties.Item(System.String)">
            <summary>
              Gets or sets the value of a property
            </summary>
            <value>
              The value for the property with the specified key
            </value>
            <remarks>
              <para>
                Gets or sets the value of a property
              </para>
            </remarks>
        </member>
        <member name="T:Castle.Core.Logging.IExtendedLogger">
            <summary>
              Provides an interface that supports <see cref = "T:Castle.Core.Logging.ILogger" /> and
              allows the storage and retrieval of Contexts. These are supported in
              both log4net and NLog.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.IExtendedLogger.GlobalProperties">
            <summary>
              Exposes the Global Context of the extended logger.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.IExtendedLogger.ThreadProperties">
            <summary>
              Exposes the Thread Context of the extended logger.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.IExtendedLogger.ThreadStacks">
            <summary>
              Exposes the Thread Stack of the extended logger.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.IExtendedLoggerFactory">
            <summary>
              Provides a factory that can produce either <see cref = "T:Castle.Core.Logging.ILogger" /> or
              <see cref = "T:Castle.Core.Logging.IExtendedLogger" /> classes.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.Type)">
            <summary>
              Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.String)">
            <summary>
              Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new extended logger.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.ILogger">
            <summary>
              Manages logging.
            </summary>
            <remarks>
              This is a facade for the different logging subsystems.
              It offers a simplified interface that follows IOC patterns
              and a simplified priority/level/severity abstraction.
            </remarks>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsTraceEnabled">
            <summary>
              Determines if messages of priority "trace" will be logged.
            </summary>
            <value>True if "trace" messages will be logged.</value>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsDebugEnabled">
            <summary>
              Determines if messages of priority "debug" will be logged.
            </summary>
            <value>True if "debug" messages will be logged.</value>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsErrorEnabled">
            <summary>
              Determines if messages of priority "error" will be logged.
            </summary>
            <value>True if "error" messages will be logged.</value>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsFatalEnabled">
            <summary>
              Determines if messages of priority "fatal" will be logged.
            </summary>
            <value>True if "fatal" messages will be logged.</value>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsInfoEnabled">
            <summary>
              Determines if messages of priority "info" will be logged.
            </summary>
            <value>True if "info" messages will be logged.</value>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsWarnEnabled">
            <summary>
              Determines if messages of priority "warn" will be logged.
            </summary>
            <value>True if "warn" messages will be logged.</value>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.CreateChildLogger(System.String)">
            <summary>
              Create a new child logger.
              The name of the child logger is [current-loggers-name].[passed-in-name]
            </summary>
            <param name = "loggerName">The Subname of this logger.</param>
            <returns>The New ILogger instance.</returns>
            <exception cref = "T:System.ArgumentException">If the name has an empty element name.</exception>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Trace(System.String)">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Trace(System.Func{System.String})">
            <summary>
              Logs a trace message with lazily constructed message. The message will be constructed only if the <see cref = "P:Castle.Core.Logging.ILogger.IsTraceEnabled" /> is true.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Trace(System.String,System.Exception)">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.TraceFormat(System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.TraceFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.TraceFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.TraceFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Debug(System.String)">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Debug(System.Func{System.String})">
            <summary>
              Logs a debug message with lazily constructed message. The message will be constructed only if the <see cref = "P:Castle.Core.Logging.ILogger.IsDebugEnabled" /> is true.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Debug(System.String,System.Exception)">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Error(System.String)">
            <summary>
              Logs an error message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Error(System.Func{System.String})">
            <summary>
              Logs an error message with lazily constructed message. The message will be constructed only if the <see cref = "P:Castle.Core.Logging.ILogger.IsErrorEnabled" /> is true.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Error(System.String,System.Exception)">
            <summary>
              Logs an error message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Fatal(System.String)">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Fatal(System.Func{System.String})">
            <summary>
              Logs a fatal message with lazily constructed message. The message will be constructed only if the <see cref = "P:Castle.Core.Logging.ILogger.IsFatalEnabled" /> is true.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Fatal(System.String,System.Exception)">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Info(System.String)">
            <summary>
              Logs an info message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Info(System.Func{System.String})">
            <summary>
              Logs a info message with lazily constructed message. The message will be constructed only if the <see cref = "P:Castle.Core.Logging.ILogger.IsInfoEnabled" /> is true.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Info(System.String,System.Exception)">
            <summary>
              Logs an info message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Warn(System.String)">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Warn(System.Func{System.String})">
            <summary>
              Logs a warn message with lazily constructed message. The message will be constructed only if the <see cref = "P:Castle.Core.Logging.ILogger.IsWarnEnabled" /> is true.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Warn(System.String,System.Exception)">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="T:Castle.Core.Logging.ILoggerFactory">
            <summary>
              Manages the instantiation of <see cref = "T:Castle.Core.Logging.ILogger" />s.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.Type)">
            <summary>
              Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.String)">
            <summary>
              Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates a new logger.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.LevelFilteredLogger">
            <summary>
            The Level Filtered Logger class.  This is a base class which
            provides a LogLevel attribute and reroutes all functions into
            one Log method.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.#ctor">
            <summary>
              Creates a new <c>LevelFilteredLogger</c>.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.Level">
            <value>
              The <c>LoggerLevel</c> that this logger
              will be using. Defaults to <c>LoggerLevel.Off</c>
            </value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.Name">
            <value>
              The name that this logger will be using. 
              Defaults to <c>string.Empty</c>
            </value>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Trace(System.String)">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Trace(System.Func{System.String})">
            <summary>
              Logs a trace message.
            </summary>
            <param name="messageFactory">A functor to create the message</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Trace(System.String,System.Exception)">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.TraceFormat(System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.TraceFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.TraceFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.TraceFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a trace message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Debug(System.String)">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Debug(System.String,System.Exception)">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a debug message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Info(System.String)">
            <summary>
              Logs an info message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Info(System.String,System.Exception)">
            <summary>
              Logs an info message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an info message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Warn(System.String)">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Warn(System.String,System.Exception)">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a warn message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Error(System.String)">
            <summary>
              Logs an error message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Error(System.String,System.Exception)">
            <summary>
              Logs an error message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs an error message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Fatal(System.String)">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Fatal(System.String,System.Exception)">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.Exception,System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              Logs a fatal message.
            </summary>
            <param name = "exception">The exception to log</param>
            <param name = "formatProvider">The format provider to use</param>
            <param name = "format">Format string for the message to log</param>
            <param name = "args">Format arguments for the message to log</param>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsTraceEnabled">
            <summary>
              Determines if messages of priority "trace" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref = "F:Castle.Core.Logging.LoggerLevel.Trace" /> bit</value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsDebugEnabled">
            <summary>
              Determines if messages of priority "debug" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref = "F:Castle.Core.Logging.LoggerLevel.Debug" /> bit</value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsInfoEnabled">
            <summary>
              Determines if messages of priority "info" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref = "F:Castle.Core.Logging.LoggerLevel.Info" /> bit</value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsWarnEnabled">
            <summary>
              Determines if messages of priority "warn" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref = "F:Castle.Core.Logging.LoggerLevel.Warn" /> bit</value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsErrorEnabled">
            <summary>
              Determines if messages of priority "error" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref = "F:Castle.Core.Logging.LoggerLevel.Error" /> bit</value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsFatalEnabled">
            <summary>
              Determines if messages of priority "fatal" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref = "F:Castle.Core.Logging.LoggerLevel.Fatal" /> bit</value>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Log(Castle.Core.Logging.LoggerLevel,System.String,System.String,System.Exception)">
            <summary>
              Implementors output the log content by implementing this method only.
              Note that exception can be null
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.LoggerLevel">
            <summary>
              Supporting Logger levels.
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Off">
            <summary>
              Logging will be off
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Fatal">
            <summary>
              Fatal logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Error">
            <summary>
              Error logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Warn">
            <summary>
              Warn logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Info">
            <summary>
              Info logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Debug">
            <summary>
              Debug logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Trace">
            <summary>
              Trace logging level
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.NullLogFactory">
            <summary>
            NullLogFactory used when logging is turned off.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.NullLogFactory.Create(System.String)">
            <summary>
              Creates an instance of ILogger with the specified name.
            </summary>
            <param name = "name">Name.</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
              Creates an instance of ILogger with the specified name and LoggerLevel.
            </summary>
            <param name = "name">Name.</param>
            <param name = "level">Level.</param>
        </member>
        <member name="T:Castle.Core.Logging.NullLogger">
            <summary>
              The Null Logger class.  This is useful for implementations where you need
              to provide a logger to a utility class, but do not want any output from it.
              It also helps when you have a utility that does not have a logger to supply.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.GlobalProperties">
            <summary>
              Returns empty context properties.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.ThreadProperties">
            <summary>
              Returns empty context properties.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.ThreadStacks">
            <summary>
              Returns empty context stacks.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsTraceEnabled">
            <summary>
              No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsDebugEnabled">
            <summary>
              No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsErrorEnabled">
            <summary>
              No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsFatalEnabled">
            <summary>
              No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsInfoEnabled">
            <summary>
              No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsWarnEnabled">
            <summary>
              No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.CreateChildLogger(System.String)">
            <summary>
              Returns this <c>NullLogger</c>.
            </summary>
            <param name = "loggerName">Ignored</param>
            <returns>This ILogger instance.</returns>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Trace(System.String)">
            <summary>
              No-op.
            </summary>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Trace(System.String,System.Exception)">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.TraceFormat(System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.TraceFormat(System.Exception,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.TraceFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.TraceFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Debug(System.String)">
            <summary>
              No-op.
            </summary>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Debug(System.String,System.Exception)">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.Exception,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Error(System.String)">
            <summary>
              No-op.
            </summary>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Error(System.String,System.Exception)">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.Exception,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Fatal(System.String)">
            <summary>
              No-op.
            </summary>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Fatal(System.String,System.Exception)">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.Exception,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Info(System.String)">
            <summary>
              No-op.
            </summary>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Info(System.String,System.Exception)">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.Exception,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Warn(System.String)">
            <summary>
              No-op.
            </summary>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Warn(System.String,System.Exception)">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.Exception,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
              No-op.
            </summary>
            <param name = "exception">Ignored</param>
            <param name = "formatProvider">Ignored</param>
            <param name = "format">Ignored</param>
            <param name = "args">Ignored</param>
        </member>
        <member name="T:Castle.Core.Logging.StreamLogger">
            <summary>
            The Stream Logger class.  This class can stream log information
            to any stream, it is suitable for storing a log file to disk,
            or to a <c>MemoryStream</c> for testing your components.
            </summary>
            <remarks>
            This logger is not thread safe.
            </remarks>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.Stream)">
            <summary>
              Creates a new <c>StreamLogger</c> with default encoding 
              and buffer size. Initial Level is set to Debug.
            </summary>
            <param name = "name">
              The name of the log.
            </param>
            <param name = "stream">
              The stream that will be used for logging,
              seeking while the logger is alive 
            </param>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.Stream,System.Text.Encoding)">
            <summary>
              Creates a new <c>StreamLogger</c> with default buffer size.
              Initial Level is set to Debug.
            </summary>
            <param name = "name">
              The name of the log.
            </param>
            <param name = "stream">
              The stream that will be used for logging,
              seeking while the logger is alive 
            </param>
            <param name = "encoding">
              The encoding that will be used for this stream.
              <see cref = "T:System.IO.StreamWriter" />
            </param>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.Stream,System.Text.Encoding,System.Int32)">
            <summary>
              Creates a new <c>StreamLogger</c>. 
              Initial Level is set to Debug.
            </summary>
            <param name = "name">
              The name of the log.
            </param>
            <param name = "stream">
              The stream that will be used for logging,
              seeking while the logger is alive 
            </param>
            <param name = "encoding">
              The encoding that will be used for this stream.
              <see cref = "T:System.IO.StreamWriter" />
            </param>
            <param name = "bufferSize">
              The buffer size that will be used for this stream.
              <see cref = "T:System.IO.StreamWriter" />
            </param>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.StreamWriter)">
            <summary>
              Creates a new <c>StreamLogger</c> with 
              Debug as default Level.
            </summary>
            <param name = "name">The name of the log.</param>
            <param name = "writer">The <c>StreamWriter</c> the log will write to.</param>
        </member>
        <member name="T:Castle.Core.Logging.StreamLoggerFactory">
            <summary>
              Creates <see cref = "T:Castle.Core.Logging.StreamLogger" /> outputting
              to files. The name of the file is derived from the log name
              plus the 'log' extension.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.TraceLogger">
            <summary>
              The TraceLogger sends all logging to the System.Diagnostics.TraceSource
              built into the .net framework.
            </summary>
            <remarks>
              Logging can be configured in the system.diagnostics configuration 
              section. 
            
              If logger doesn't find a source name with a full match it will
              use source names which match the namespace partially. For example you can
              configure from all castle components by adding a source name with the
              name "Castle". 
            
              If no portion of the namespace matches the source named "Default" will
              be used.
            </remarks>
        </member>
        <member name="M:Castle.Core.Logging.TraceLogger.#ctor(System.String)">
            <summary>
            Build a new trace logger based on the named TraceSource
            </summary>
            <param name="name">The name used to locate the best TraceSource. In most cases comes from the using type's fullname.</param>
        </member>
        <member name="M:Castle.Core.Logging.TraceLogger.#ctor(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Build a new trace logger based on the named TraceSource
            </summary>
            <param name="name">The name used to locate the best TraceSource. In most cases comes from the using type's fullname.</param>
            <param name="level">The default logging level at which this source should write messages. In almost all cases this
            default value will be overridden in the config file. </param>
        </member>
        <member name="M:Castle.Core.Logging.TraceLogger.CreateChildLogger(System.String)">
            <summary>
            Create a new child logger.
            The name of the child logger is [current-loggers-name].[passed-in-name]
            </summary>
            <param name="loggerName">The Subname of this logger.</param>
            <returns>The New ILogger instance.</returns> 
        </member>
        <member name="T:Castle.Core.Logging.TraceLoggerFactory">
            <summary>
              Used to create the TraceLogger implementation of ILogger interface. See <see cref = "T:Castle.Core.Logging.TraceLogger" />.
            </summary>
        </member>
        <member name="T:Castle.Core.ProxyServices">
            <summary>
            List of utility methods related to dynamic proxy operations
            </summary>
        </member>
        <member name="M:Castle.Core.ProxyServices.IsDynamicProxy(System.Type)">
            <summary>
            Determines whether the specified type is a proxy generated by
            DynamicProxy (1 or 2).
            </summary>
            <param name="type">The type.</param>
            <returns>
            	<c>true</c> if it is a proxy; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Castle.Core.ReflectionBasedDictionaryAdapter">
            <summary>
            Readonly implementation of <see cref="T:System.Collections.IDictionary"/> which uses an anonymous object as its source. Uses names of properties as keys, and property values as... well - values. Keys are not case sensitive.
            </summary>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.#ctor(System.Object)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.Core.ReflectionBasedDictionaryAdapter" /> class.
            </summary>
            <param name = "target">The target.</param>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Count">
            <summary>
              Gets the number of elements contained in the <see cref = "T:System.Collections.ICollection" />.
            </summary>
            <returns>The number of elements contained in the <see cref = "T:System.Collections.ICollection" />.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.IsSynchronized">
            <summary>
              Gets a value indicating whether access to the <see cref = "T:System.Collections.ICollection" /> is synchronized (thread safe).
            </summary>
            <returns>true if access to the <see cref = "T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.SyncRoot">
            <summary>
              Gets an object that can be used to synchronize access to the <see cref = "T:System.Collections.ICollection" />.
            </summary>
            <returns>An object that can be used to synchronize access to the <see cref = "T:System.Collections.ICollection" />.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.IsReadOnly">
            <summary>
              Gets a value indicating whether the <see cref = "T:System.Collections.IDictionary" /> object is read-only.
            </summary>
            <returns>true if the <see cref = "T:System.Collections.IDictionary" /> object is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Item(System.Object)">
            <summary>
              Gets or sets the <see cref = "T:System.Object" /> with the specified key.
            </summary>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Keys">
            <summary>
              Gets an <see cref = "T:System.Collections.ICollection" /> object containing the keys of the <see
               cref = "T:System.Collections.IDictionary" /> object.
            </summary>
            <returns>An <see cref = "T:System.Collections.ICollection" /> object containing the keys of the <see
               cref = "T:System.Collections.IDictionary" /> object.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Values">
            <summary>
              Gets an <see cref = "T:System.Collections.ICollection" /> object containing the values in the <see
               cref = "T:System.Collections.IDictionary" /> object.
            </summary>
            <returns>An <see cref = "T:System.Collections.ICollection" /> object containing the values in the <see
               cref = "T:System.Collections.IDictionary" /> object.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.System#Collections#IDictionary#IsFixedSize">
            <summary>
              Gets a value indicating whether the <see cref = "T:System.Collections.IDictionary" /> object has a fixed size.
            </summary>
            <returns>true if the <see cref = "T:System.Collections.IDictionary" /> object has a fixed size; otherwise, false.</returns>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Add(System.Object,System.Object)">
            <summary>
              Adds an element with the provided key and value to the <see cref = "T:System.Collections.IDictionary" /> object.
            </summary>
            <param name = "key">The <see cref = "T:System.Object" /> to use as the key of the element to add.</param>
            <param name = "value">The <see cref = "T:System.Object" /> to use as the value of the element to add.</param>
            <exception cref = "T:System.ArgumentNullException">
              <paramref name = "key" /> is null. </exception>
            <exception cref = "T:System.ArgumentException">An element with the same key already exists in the <see
               cref = "T:System.Collections.IDictionary" /> object. </exception>
            <exception cref = "T:System.NotSupportedException">The <see cref = "T:System.Collections.IDictionary" /> is read-only.-or- The <see
               cref = "T:System.Collections.IDictionary" /> has a fixed size. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Clear">
            <summary>
              Removes all elements from the <see cref = "T:System.Collections.IDictionary" /> object.
            </summary>
            <exception cref = "T:System.NotSupportedException">The <see cref = "T:System.Collections.IDictionary" /> object is read-only. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Contains(System.Object)">
            <summary>
              Determines whether the <see cref = "T:System.Collections.IDictionary" /> object contains an element with the specified key.
            </summary>
            <param name = "key">The key to locate in the <see cref = "T:System.Collections.IDictionary" /> object.</param>
            <returns>
              true if the <see cref = "T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.
            </returns>
            <exception cref = "T:System.ArgumentNullException">
              <paramref name = "key" /> is null. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Remove(System.Object)">
            <summary>
              Removes the element with the specified key from the <see cref = "T:System.Collections.IDictionary" /> object.
            </summary>
            <param name = "key">The key of the element to remove.</param>
            <exception cref = "T:System.ArgumentNullException">
              <paramref name = "key" /> is null. </exception>
            <exception cref = "T:System.NotSupportedException">The <see cref = "T:System.Collections.IDictionary" /> object is read-only.-or- The <see
               cref = "T:System.Collections.IDictionary" /> has a fixed size. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
              An <see cref = "T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
            <summary>
              Copies the elements of the <see cref = "T:System.Collections.ICollection" /> to an <see cref = "T:System.Array" />, starting at a particular <see
               cref = "T:System.Array" /> index.
            </summary>
            <param name = "array">The one-dimensional <see cref = "T:System.Array" /> that is the destination of the elements copied from <see
               cref = "T:System.Collections.ICollection" />. The <see cref = "T:System.Array" /> must have zero-based indexing.</param>
            <param name = "index">The zero-based index in <paramref name = "array" /> at which copying begins.</param>
            <exception cref = "T:System.ArgumentNullException">
              <paramref name = "array" /> is null. </exception>
            <exception cref = "T:System.ArgumentOutOfRangeException">
              <paramref name = "index" /> is less than zero. </exception>
            <exception cref = "T:System.ArgumentException">
              <paramref name = "array" /> is multidimensional.-or- <paramref name = "index" /> is equal to or greater than the length of <paramref
               name = "array" />.-or- The number of elements in the source <see cref = "T:System.Collections.ICollection" /> is greater than the available space from <paramref
               name = "index" /> to the end of the destination <paramref name = "array" />. </exception>
            <exception cref = "T:System.ArgumentException">The type of the source <see cref = "T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref
               name = "array" />. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.System#Collections#IDictionary#GetEnumerator">
            <summary>
              Returns an <see cref = "T:System.Collections.IDictionaryEnumerator" /> object for the <see
               cref = "T:System.Collections.IDictionary" /> object.
            </summary>
            <returns>
              An <see cref = "T:System.Collections.IDictionaryEnumerator" /> object for the <see
               cref = "T:System.Collections.IDictionary" /> object.
            </returns>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Read(System.Collections.IDictionary,System.Object)">
            <summary>
              Reads values of properties from <paramref name = "valuesAsAnonymousObject" /> and inserts them into <paramref
               name = "targetDictionary" /> using property names as keys.
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.AbstractStreamResource">
            <summary>
            
            </summary>
        </member>
        <member name="F:Castle.Core.Resource.AbstractStreamResource.createStream">
            <summary>
            This returns a new stream instance each time it is called.
            It is the responsibility of the caller to dispose of this stream
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.FileResource">
            <summary>
            
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.FileResourceFactory">
            <summary>
            
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.IResource">
            <summary>
            Represents a 'streamable' resource. Can
            be a file, a resource in an assembly.
            </summary>
        </member>
        <member name="P:Castle.Core.Resource.IResource.FileBasePath">
            <remarks>
            Only valid for resources that
            can be obtained through relative paths
            </remarks>
        </member>
        <member name="M:Castle.Core.Resource.IResource.GetStreamReader">
            <summary>
            Returns a reader for the stream
            </summary>
            <remarks>
            It's up to the caller to dispose the reader.
            </remarks>
        </member>
        <member name="M:Castle.Core.Resource.IResource.GetStreamReader(System.Text.Encoding)">
            <summary>
            Returns a reader for the stream
            </summary>
            <remarks>
            It's up to the caller to dispose the reader.
            </remarks>
        </member>
        <member name="M:Castle.Core.Resource.IResource.CreateRelative(System.String)">
            <summary>
            Returns an instance of <see cref="T:Castle.Core.Resource.IResource"/>
            created according to the <c>relativePath</c>
            using itself as the root.
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.IResourceFactory">
            <summary>
            Depicts the contract for resource factories.
            </summary>
        </member>
        <member name="M:Castle.Core.Resource.IResourceFactory.Accept(Castle.Core.Resource.CustomUri)">
            <summary>
            Used to check whether the resource factory
            is able to deal with the given resource
            identifier.
            </summary>
            <remarks>
            Implementors should return <c>true</c>
            only if the given identifier is supported
            by the resource factory
            </remarks>
        </member>
        <member name="M:Castle.Core.Resource.IResourceFactory.Create(Castle.Core.Resource.CustomUri)">
            <summary>
            Creates an <see cref="T:Castle.Core.Resource.IResource"/> instance
            for the given resource identifier
            </summary>
        </member>
        <member name="M:Castle.Core.Resource.IResourceFactory.Create(Castle.Core.Resource.CustomUri,System.String)">
            <summary>
            Creates an <see cref="T:Castle.Core.Resource.IResource"/> instance
            for the given resource identifier
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.StaticContentResource">
            <summary>
            Adapts a static string content as an <see cref="T:Castle.Core.Resource.IResource"/>
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.UncResource">
            <summary>
            Enable access to files on network shares
            </summary>
        </member>
        <member name="T:Castle.Core.Smtp.DefaultSmtpSender">
            <summary>
            Default <see cref="T:Castle.Core.Smtp.IEmailSender"/> implementation.
            </summary>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.Smtp.DefaultSmtpSender"/> class based on the <see cref="T:System.Net.Mail.SmtpClient"/> configuration provided in the application configuration file.
            </summary>
            <remarks>
            This constructor is based on the default <see cref="T:System.Net.Mail.SmtpClient"/> configuration in the application configuration file.
            </remarks>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.#ctor(System.String)">
            <summary>
            This service implementation
            requires a host name in order to work
            </summary>
            <param name="hostname">The smtp server name</param>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Port">
            <summary>
            Gets or sets the port used to
            access the SMTP server
            </summary>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Hostname">
            <summary>
            Gets the hostname.
            </summary>
            <value>The hostname.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.AsyncSend">
            <summary>
            Gets or sets a value which is used to
            configure if emails are going to be sent asynchronously or not.
            </summary>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Timeout">
            <summary>
            Gets or sets a value that specifies
            the amount of time after which a synchronous Send call times out.
            </summary>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.UseSsl">
            <summary>
            Gets or sets a value indicating whether the email should be sent using
            a secure communication channel.
            </summary>
            <value><c>true</c> if should use SSL; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.Send(System.String,System.String,System.String,System.String)">
            <summary>
            Sends a message.
            </summary>
            <exception cref="T:System.ArgumentNullException">If any of the parameters is null</exception>
            <param name="from">From field</param>
            <param name="to">To field</param>
            <param name="subject">e-mail's subject</param>
            <param name="messageText">message's body</param>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.Send(System.Net.Mail.MailMessage)">
            <summary>
            Sends a message.
            </summary>
            <exception cref="T:System.ArgumentNullException">If the message is null</exception>
            <param name="message">Message instance</param>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Domain">
            <summary>
            Gets or sets the domain.
            </summary>
            <value>The domain.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.UserName">
            <summary>
            Gets or sets the name of the user.
            </summary>
            <value>The name of the user.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Password">
            <summary>
            Gets or sets the password.
            </summary>
            <value>The password.</value>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.Configure(System.Net.Mail.SmtpClient)">
            <summary>
            Configures the sender
            with port information and eventual credential
            informed
            </summary>
            <param name="smtpClient">Message instance</param>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.HasCredentials">
            <summary>
            Gets a value indicating whether credentials were informed.
            </summary>
            <value>
            <see langword="true"/> if this instance has credentials; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="T:Castle.Core.Smtp.IEmailSender">
            <summary>
            Email sender abstraction.
            </summary>
        </member>
        <member name="M:Castle.Core.Smtp.IEmailSender.Send(System.String,System.String,System.String,System.String)">
            <summary>
            Sends a mail message.
            </summary>
            <param name="from">From field</param>
            <param name="to">To field</param>
            <param name="subject">E-mail's subject</param>
            <param name="messageText">message's body</param>
        </member>
        <member name="M:Castle.Core.Smtp.IEmailSender.Send(System.Net.Mail.MailMessage)">
            <summary>
            Sends a <see cref="T:System.Net.Mail.MailMessage">message</see>. 
            </summary>
            <param name="message"><see cref="T:System.Net.Mail.MailMessage">Message</see> instance</param>
        </member>
        <member name="M:Castle.Core.Smtp.IEmailSender.Send(System.Collections.Generic.IEnumerable{System.Net.Mail.MailMessage})">
            <summary>
            Sends multiple <see cref="T:System.Net.Mail.MailMessage">messages</see>. 
            </summary>
            <param name="messages">List of <see cref="T:System.Net.Mail.MailMessage">messages</see></param>
        </member>
        <member name="T:Castle.DynamicProxy.Contributors.ITypeContributor">
            <summary>
              Interface describing elements composing generated type
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Contributors.MembersCollector.AcceptMethod(System.Reflection.MethodInfo,System.Boolean,Castle.DynamicProxy.IProxyGenerationHook)">
            <summary>
              Performs some basic screening and invokes the <see cref = "T:Castle.DynamicProxy.IProxyGenerationHook" />
              to select methods.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Contributors.MembersCollector.AcceptMethodPreScreen(System.Reflection.MethodInfo,System.Boolean,Castle.DynamicProxy.IProxyGenerationHook)">
            <summary>
              Performs some basic screening to filter out non-interceptable methods.
            </summary>
            <remarks>
              The <paramref name="hook"/> will get invoked for non-interceptable method notification only;
              it does not get asked whether or not to intercept the <paramref name="method"/>.
            </remarks>
        </member>
        <member name="T:Castle.DynamicProxy.Contributors.NonInheritableAttributesContributor">
            <summary>
              Reproduces the proxied type's non-inheritable custom attributes on the proxy type.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.Contributors.ProxyTargetAccessorContributor">
            <summary>
              Adds an implementation for <see cref="T:Castle.DynamicProxy.IProxyTargetAccessor"/> to the proxy type.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.CustomAttributeInfo">
            <summary>
            Encapsulates the information needed to build an attribute.
            </summary>
            <remarks>
            Arrays passed to this class as constructor arguments or property or field values become owned by this class.
            They should not be mutated after creation.
            </remarks>
        </member>
        <member name="T:Castle.DynamicProxy.DefaultProxyBuilder">
            <summary>
              Default implementation of <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> interface producing in-memory proxy assemblies.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.DefaultProxyBuilder.#ctor">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.DefaultProxyBuilder" /> class with new <see cref = "P:Castle.DynamicProxy.DefaultProxyBuilder.ModuleScope" />.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.DefaultProxyBuilder.#ctor(Castle.DynamicProxy.ModuleScope)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.DefaultProxyBuilder" /> class.
            </summary>
            <param name = "scope">The module scope for generated proxy types.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ExceptionMessageBuilder.CreateInstructionsToMakeVisible(System.Reflection.Assembly)">
            <summary>
            Provides instructions that a user could follow to make a type or method in <paramref name="targetAssembly"/>
            visible to DynamicProxy.</summary>
            <param name="targetAssembly">The assembly containing the type or method.</param>
            <returns>Instructions that a user could follow to make a type or method visible to DynamicProxy.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ExceptionMessageBuilder.CreateMessageForInaccessibleType(System.Type,System.Type)">
            <summary>
            Creates a message to inform clients that a proxy couldn't be created due to reliance on an
            inaccessible type (perhaps itself).
            </summary>
            <param name="inaccessibleType">the inaccessible type that prevents proxy creation</param>
            <param name="typeToProxy">the type that couldn't be proxied</param>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.BaseProxyGenerator">
            <summary>
              Base class that exposes the common functionalities
              to proxy generation.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.BaseProxyGenerator.AddMappingNoCheck(System.Type,Castle.DynamicProxy.Contributors.ITypeContributor,System.Collections.Generic.IDictionary{System.Type,Castle.DynamicProxy.Contributors.ITypeContributor})">
            <summary>
              It is safe to add mapping (no mapping for the interface exists)
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.BaseProxyGenerator.GenerateParameterlessConstructor(Castle.DynamicProxy.Generators.Emitters.ClassEmitter,System.Type,Castle.DynamicProxy.Generators.Emitters.SimpleAST.FieldReference)">
            <summary>
              Generates a parameters constructor that initializes the proxy
              state with <see cref = "T:Castle.DynamicProxy.StandardInterceptor" /> just to make it non-null.
              <para>
                This constructor is important to allow proxies to be XML serializable
              </para>
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.CacheKey.#ctor(System.Reflection.MemberInfo,System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.Generators.CacheKey" /> class.
            </summary>
            <param name = "target">Target element. This is either target type or target method for invocation types.</param>
            <param name = "type">The type of the proxy. This is base type for invocation types.</param>
            <param name = "interfaces">The interfaces.</param>
            <param name = "options">The options.</param>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.CacheKey.#ctor(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.Generators.CacheKey" /> class.
            </summary>
            <param name = "target">Type of the target.</param>
            <param name = "interfaces">The interfaces.</param>
            <param name = "options">The options.</param>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.Emitters.LdcOpCodesDictionary">
            <summary>
              Provides appropriate Ldc.X opcode for the type of primitive value to be loaded.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.Emitters.LdindOpCodesDictionary">
            <summary>
              Provides appropriate Ldind.X opcode for 
              the type of primitive value to be loaded indirectly.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.Emitters.OpCodeUtil.EmitLoadIndirectOpCodeForType(System.Reflection.Emit.ILGenerator,System.Type)">
            <summary>
              Emits a load indirect opcode of the appropriate type for a value or object reference.
              Pops a pointer off the evaluation stack, dereferences it and loads
              a value of the specified type.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.Emitters.OpCodeUtil.EmitLoadOpCodeForDefaultValueOfType(System.Reflection.Emit.ILGenerator,System.Type)">
            <summary>
              Emits a load opcode of the appropriate kind for the constant default value of a
              type, such as 0 for value types and null for reference types.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.Emitters.OpCodeUtil.EmitStoreIndirectOpCodeForType(System.Reflection.Emit.ILGenerator,System.Type)">
            <summary>
              Emits a store indirectopcode of the appropriate type for a value or object reference.
              Pops a value of the specified type and a pointer off the evaluation stack, and
              stores the value.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.Emitters.SimpleAST.IndirectReference">
            <summary>
              Wraps a reference that is passed 
              ByRef and provides indirect load/store support.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.Emitters.StindOpCodesDictionary">
            <summary>
              Provides appropriate Stind.X opcode 
              for the type of primitive value to be stored indirectly.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.INamingScope">
            <summary>
              Represents the scope of uniqueness of names for types and their members
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.INamingScope.GetUniqueName(System.String)">
            <summary>
              Gets a unique name based on <paramref name = "suggestedName" />
            </summary>
            <param name = "suggestedName">Name suggested by the caller</param>
            <returns>Unique name based on <paramref name = "suggestedName" />.</returns>
            <remarks>
              Implementers should provide name as closely resembling <paramref name = "suggestedName" /> as possible.
              Generally if no collision occurs it is suggested to return suggested name, otherwise append sequential suffix.
              Implementers must return deterministic names, that is when <see cref = "M:Castle.DynamicProxy.Generators.INamingScope.GetUniqueName(System.String)" /> is called twice 
              with the same suggested name, the same returned name should be provided each time. Non-deterministic return
              values, like appending random suffices will break serialization of proxies.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.INamingScope.SafeSubScope">
            <summary>
              Returns new, disposable naming scope. It is responsibility of the caller to make sure that no naming collision
              with enclosing scope, or other subscopes is possible.
            </summary>
            <returns>New naming scope.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.InvocationTypeGenerator.GetBaseCtorArguments(System.Type,System.Reflection.ConstructorInfo@)">
            <summary>
              Generates the constructor for the class that extends
              <see cref = "T:Castle.DynamicProxy.AbstractInvocation" />
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Generators.MetaEvent.#ctor(System.Reflection.EventInfo,Castle.DynamicProxy.Generators.MetaMethod,Castle.DynamicProxy.Generators.MetaMethod,System.Reflection.EventAttributes)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.Generators.MetaEvent" /> class.
            </summary>
            <param name = "event">The event.</param>
            <param name = "adder">The add method.</param>
            <param name = "remover">The remove method.</param>
            <param name = "attributes">The attributes.</param>
        </member>
        <member name="T:Castle.DynamicProxy.Generators.MethodFinder">
            <summary>
              Returns the methods implemented by a type. Use this instead of Type.GetMethods() to work around a CLR issue
              where duplicate MethodInfos are returned by Type.GetMethods() after a token of a generic type's method was loaded.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.IChangeProxyTarget">
            <summary>
              Exposes means to change target objects of proxies and invocations.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IChangeProxyTarget.ChangeInvocationTarget(System.Object)">
            <summary>
              Changes the target object (<see cref = "P:Castle.DynamicProxy.IInvocation.InvocationTarget" />) of current <see cref = "T:Castle.DynamicProxy.IInvocation" />.
            </summary>
            <param name = "target">The new value of target of invocation.</param>
            <remarks>
              Although the method takes <see cref = "T:System.Object" /> the actual instance must be of type assignable to <see
               cref = "P:Castle.DynamicProxy.IInvocation.TargetType" />, otherwise an <see cref = "T:System.InvalidCastException" /> will be thrown.
              Also while it's technically legal to pass null reference (Nothing in Visual Basic) as <paramref name = "target" />, for obvious reasons Dynamic Proxy will not be able to call the intercepted method on such target.
              In this case last interceptor in the pipeline mustn't call <see cref = "M:Castle.DynamicProxy.IInvocation.Proceed" /> or a <see
               cref = "T:System.NotImplementedException" /> will be throws.
              Also while it's technically legal to pass proxy itself as <paramref name = "target" />, this would create stack overflow.
              In this case last interceptor in the pipeline mustn't call <see cref = "M:Castle.DynamicProxy.IInvocation.Proceed" /> or a <see
               cref = "T:System.InvalidOperationException" /> will be throws.
            </remarks>
            <exception cref = "T:System.InvalidCastException">Thrown when <paramref name = "target" /> is not assignable to the proxied type.</exception>
        </member>
        <member name="M:Castle.DynamicProxy.IChangeProxyTarget.ChangeProxyTarget(System.Object)">
            <summary>
              Permanently changes the target object of the proxy. This does not affect target of the current invocation.
            </summary>
            <param name = "target">The new value of target of the proxy.</param>
            <remarks>
              Although the method takes <see cref = "T:System.Object" /> the actual instance must be of type assignable to proxy's target type, otherwise an <see
               cref = "T:System.InvalidCastException" /> will be thrown.
              Also while it's technically legal to pass null reference (Nothing in Visual Basic) as <paramref name = "target" />, for obvious reasons Dynamic Proxy will not be able to call the intercepted method on such target.
              In this case last interceptor in the pipeline mustn't call <see cref = "M:Castle.DynamicProxy.IInvocation.Proceed" /> or a <see
               cref = "T:System.NotImplementedException" /> will be throws.
              Also while it's technically legal to pass proxy itself as <paramref name = "target" />, this would create stack overflow.
              In this case last interceptor in the pipeline mustn't call <see cref = "M:Castle.DynamicProxy.IInvocation.Proceed" /> or a <see
               cref = "T:System.InvalidOperationException" /> will be throws.
            </remarks>
            <exception cref = "T:System.InvalidCastException">Thrown when <paramref name = "target" /> is not assignable to the proxied type.</exception>
        </member>
        <member name="T:Castle.DynamicProxy.IInterceptor">
            <summary>
              Provides the main DynamicProxy extension point that allows member interception.
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.IInterceptorSelector">
            <summary>
              Provides an extension point that allows proxies to choose specific interceptors on
              a per method basis.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IInterceptorSelector.SelectInterceptors(System.Type,System.Reflection.MethodInfo,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Selects the interceptors that should intercept calls to the given <paramref name = "method" />.
            </summary>
            <param name = "type">The type of the target object.</param>
            <param name = "method">The method that will be intercepted.</param>
            <param name = "interceptors">All interceptors registered with the proxy.</param>
            <returns>An array of interceptors to invoke upon calling the <paramref name = "method" />.</returns>
            <remarks>
              This method is called only once per proxy instance, upon the first call to the
              <paramref name = "method" />. Either an empty array or null are valid return values to indicate
              that no interceptor should intercept calls to the method. Although it is not advised, it is
              legal to return other <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations than these provided in
              <paramref name = "interceptors" />.
            </remarks>
        </member>
        <member name="T:Castle.DynamicProxy.IInvocation">
            <summary>
              Encapsulates an invocation of a proxied method.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.Arguments">
            <summary>
              Gets the arguments that the <see cref = "P:Castle.DynamicProxy.IInvocation.Method" /> has been invoked with.
            </summary>
            <value>The arguments the method was invoked with.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.GenericArguments">
            <summary>
              Gets the generic arguments of the method.
            </summary>
            <value>The generic arguments, or null if not a generic method.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.InvocationTarget">
            <summary>
              Gets the object on which the invocation is performed. This is different from proxy object
              because most of the time this will be the proxy target object.
            </summary>
            <seealso cref = "T:Castle.DynamicProxy.IChangeProxyTarget" />
            <value>The invocation target.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.Method">
            <summary>
              Gets the <see cref = "T:System.Reflection.MethodInfo" /> representing the method being invoked on the proxy.
            </summary>
            <value>The <see cref = "T:System.Reflection.MethodInfo" /> representing the method being invoked.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.MethodInvocationTarget">
            <summary>
              For interface proxies, this will point to the <see cref = "T:System.Reflection.MethodInfo" /> on the target class.
            </summary>
            <value>The method invocation target.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.Proxy">
            <summary>
              Gets the proxy object on which the intercepted method is invoked.
            </summary>
            <value>Proxy object on which the intercepted method is invoked.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.ReturnValue">
            <summary>
              Gets or sets the return value of the method.
            </summary>
            <value>The return value of the method.</value>
        </member>
        <member name="P:Castle.DynamicProxy.IInvocation.TargetType">
            <summary>
              Gets the type of the target object for the intercepted method.
            </summary>
            <value>The type of the target object.</value>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocation.GetArgumentValue(System.Int32)">
            <summary>
              Gets the value of the argument at the specified <paramref name = "index" />.
            </summary>
            <param name = "index">The index.</param>
            <returns>The value of the argument at the specified <paramref name = "index" />.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocation.GetConcreteMethod">
            <summary>
              Returns the concrete instantiation of the <see cref = "P:Castle.DynamicProxy.IInvocation.Method" /> on the proxy, with any generic
              parameters bound to real types.
            </summary>
            <returns>
              The concrete instantiation of the <see cref = "P:Castle.DynamicProxy.IInvocation.Method" /> on the proxy, or the <see cref = "P:Castle.DynamicProxy.IInvocation.Method" /> if
              not a generic method.
            </returns>
            <remarks>
              Can be slower than calling <see cref = "P:Castle.DynamicProxy.IInvocation.Method" />.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocation.GetConcreteMethodInvocationTarget">
            <summary>
              Returns the concrete instantiation of <see cref = "P:Castle.DynamicProxy.IInvocation.MethodInvocationTarget" />, with any
              generic parameters bound to real types.
              For interface proxies, this will point to the <see cref = "T:System.Reflection.MethodInfo" /> on the target class.
            </summary>
            <returns>The concrete instantiation of <see cref = "P:Castle.DynamicProxy.IInvocation.MethodInvocationTarget" />, or
              <see cref = "P:Castle.DynamicProxy.IInvocation.MethodInvocationTarget" /> if not a generic method.</returns>
            <remarks>
              In debug builds this can be slower than calling <see cref = "P:Castle.DynamicProxy.IInvocation.MethodInvocationTarget" />.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocation.Proceed">
            <summary>
              Proceeds the call to the next interceptor in line, and ultimately to the target method.
            </summary>
            <remarks>
              Since interface proxies without a target don't have the target implementation to proceed to,
              it is important, that the last interceptor does not call this method, otherwise a
              <see cref = "T:System.NotImplementedException" /> will be thrown.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocation.CaptureProceedInfo">
            <summary>
              Returns an object describing the <see cref="M:Castle.DynamicProxy.IInvocation.Proceed"/> operation for this <see cref="T:Castle.DynamicProxy.IInvocation"/>
              at this specific point during interception.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocation.SetArgumentValue(System.Int32,System.Object)">
            <summary>
              Overrides the value of an argument at the given <paramref name = "index" /> with the
              new <paramref name = "value" /> provided.
            </summary>
            <remarks>
              This method accepts an <see cref = "T:System.Object" />, however the value provided must be compatible
              with the type of the argument defined on the method, otherwise an exception will be thrown.
            </remarks>
            <param name = "index">The index of the argument to override.</param>
            <param name = "value">The new value for the argument.</param>
        </member>
        <member name="T:Castle.DynamicProxy.IInvocationProceedInfo">
            <summary>
              Describes the <see cref="M:Castle.DynamicProxy.IInvocation.Proceed"/> operation for an <see cref="T:Castle.DynamicProxy.IInvocation"/>
              at a specific point during interception.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IInvocationProceedInfo.Invoke">
            <summary>
              Executes the <see cref="M:Castle.DynamicProxy.IInvocation.Proceed"/> operation described by this instance.
            </summary>
            <exception cref="T:System.NotImplementedException">There is no interceptor, nor a proxy target object, to proceed to.</exception>
        </member>
        <member name="M:Castle.DynamicProxy.Internal.AttributeUtil.ShouldSkipAttributeReplication(System.Type,System.Boolean)">
            <summary>
              Attributes should be replicated if they are non-inheritable,
              but there are some special cases where the attributes means
              something to the CLR, where they should be skipped.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Internal.TypeUtil.GetAllInterfaces(System.Type[])">
            <summary>
              Returns list of all unique interfaces implemented given types, including their base interfaces.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.Internal.TypeUtil.IsDelegateType(System.Type)">
            <summary>
              Checks whether the specified <paramref name="type"/> is a delegate type (i.e. a direct subclass of <see cref="T:System.MulticastDelegate"/>).
            </summary>
        </member>
        <member name="T:Castle.DynamicProxy.IProxyBuilder">
            <summary>
              Abstracts the implementation of proxy type construction.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.IProxyBuilder.Logger">
            <summary>
              Gets or sets the <see cref = "T:Castle.Core.Logging.ILogger" /> that this <see cref = "T:Castle.DynamicProxy.ProxyGenerator" /> logs to.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.IProxyBuilder.ModuleScope">
            <summary>
              Gets the <see cref = "P:Castle.DynamicProxy.IProxyBuilder.ModuleScope" /> associated with this builder.
            </summary>
            <value>The module scope associated with this builder.</value>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyBuilder.CreateClassProxyType(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates a proxy type for given <paramref name = "classToProxy" />, implementing <paramref
               name = "additionalInterfacesToProxy" />, using <paramref name = "options" /> provided.
            </summary>
            <param name = "classToProxy">The class type to proxy.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types to proxy.</param>
            <param name = "options">The proxy generation options.</param>
            <returns>The generated proxy type.</returns>
            <remarks>
              Implementers should return a proxy type for the specified class and interfaces.
              Additional interfaces should be only 'mark' interfaces, that is, they should work like interface proxy without target. (See <see
               cref = "M:Castle.DynamicProxy.IProxyBuilder.CreateInterfaceProxyTypeWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)" /> method.)
            </remarks>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is not public.
              Note that to avoid this exception, you can mark offending type internal, and define <see
               cref = "T:System.Runtime.CompilerServices.InternalsVisibleToAttribute" /> 
              pointing to Castle Dynamic Proxy assembly, in assembly containing that type, if this is appropriate.</exception>
            <seealso cref = "T:Castle.DynamicProxy.Generators.ClassProxyGenerator" />
        </member>
        <member name="M:Castle.DynamicProxy.IProxyBuilder.CreateInterfaceProxyTypeWithTarget(System.Type,System.Type[],System.Type,Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates a proxy type that proxies calls to <paramref name = "interfaceToProxy" /> members on <paramref
               name = "targetType" />, implementing <paramref name = "additionalInterfacesToProxy" />, using <paramref
               name = "options" /> provided.
            </summary>
            <param name = "interfaceToProxy">The interface type to proxy.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types to proxy.</param>
            <param name = "targetType">Type implementing <paramref name = "interfaceToProxy" /> on which calls to the interface members should be intercepted.</param>
            <param name = "options">The proxy generation options.</param>
            <returns>The generated proxy type.</returns>
            <remarks>
              Implementers should return a proxy type for the specified interface that 'proceeds' executions to the specified target.
              Additional interfaces should be only 'mark' interfaces, that is, they should work like interface proxy without target. (See <see
               cref = "M:Castle.DynamicProxy.IProxyBuilder.CreateInterfaceProxyTypeWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)" /> method.)
            </remarks>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is not public.
              Note that to avoid this exception, you can mark offending type internal, and define <see
               cref = "T:System.Runtime.CompilerServices.InternalsVisibleToAttribute" /> 
              pointing to Castle Dynamic Proxy assembly, in assembly containing that type, if this is appropriate.</exception>
            <seealso cref = "T:Castle.DynamicProxy.Generators.InterfaceProxyWithTargetGenerator" />
        </member>
        <member name="M:Castle.DynamicProxy.IProxyBuilder.CreateInterfaceProxyTypeWithTargetInterface(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates a proxy type for given <paramref name = "interfaceToProxy" /> and <parmaref
               name = "additionalInterfacesToProxy" /> that delegates all calls to the provided interceptors and allows interceptors to switch the actual target of invocation.
            </summary>
            <param name = "interfaceToProxy">The interface type to proxy.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types to proxy.</param>
            <param name = "options">The proxy generation options.</param>
            <returns>The generated proxy type.</returns>
            <remarks>
              Implementers should return a proxy type for the specified interface(s) that delegate all executions to the specified interceptors
              and uses an instance of the interface as their targets (i.e. <see cref = "P:Castle.DynamicProxy.IInvocation.InvocationTarget" />), rather than a class. All <see
               cref = "T:Castle.DynamicProxy.IInvocation" /> classes should then implement <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface,
              to allow interceptors to switch invocation target with instance of another type implementing called interface.
            </remarks>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is not public.
              Note that to avoid this exception, you can mark offending type internal, and define <see
               cref = "T:System.Runtime.CompilerServices.InternalsVisibleToAttribute" /> 
              pointing to Castle Dynamic Proxy assembly, in assembly containing that type, if this is appropriate.</exception>
            <seealso cref = "T:Castle.DynamicProxy.Generators.InterfaceProxyWithTargetInterfaceGenerator" />
        </member>
        <member name="M:Castle.DynamicProxy.IProxyBuilder.CreateInterfaceProxyTypeWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates a proxy type for given <paramref name = "interfaceToProxy" /> that delegates all calls to the provided interceptors.
            </summary>
            <param name = "interfaceToProxy">The interface type to proxy.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types to proxy.</param>
            <param name = "options">The proxy generation options.</param>
            <returns>The generated proxy type.</returns>
            <remarks>
              Implementers should return a proxy type for the specified interface and additional interfaces that delegate all executions to the specified interceptors.
            </remarks>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is not public.
              Note that to avoid this exception, you can mark offending type internal, and define <see
               cref = "T:System.Runtime.CompilerServices.InternalsVisibleToAttribute" /> 
              pointing to Castle Dynamic Proxy assembly, in assembly containing that type, if this is appropriate.</exception>
            <seealso cref = "T:Castle.DynamicProxy.Generators.InterfaceProxyWithoutTargetGenerator" />
        </member>
        <member name="T:Castle.DynamicProxy.IProxyGenerationHook">
            <summary>
              Used during the target type inspection process. Implementors have a chance to customize the
              proxy generation process.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerationHook.MethodsInspected">
            <summary>
              Invoked by the generation process to notify that the whole process has completed.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerationHook.NonProxyableMemberNotification(System.Type,System.Reflection.MemberInfo)">
            <summary>
              Invoked by the generation process to notify that a member was not marked as virtual.
            </summary>
            <param name = "type">The type which declares the non-virtual member.</param>
            <param name = "memberInfo">The non-virtual member.</param>
            <remarks>
              This method gives an opportunity to inspect any non-proxyable member of a type that has 
              been requested to be proxied, and if appropriate - throw an exception to notify the caller.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerationHook.ShouldInterceptMethod(System.Type,System.Reflection.MethodInfo)">
            <summary>
              Invoked by the generation process to determine if the specified method should be proxied.
            </summary>
            <param name = "type">The type which declares the given method.</param>
            <param name = "methodInfo">The method to inspect.</param>
            <returns>True if the given method should be proxied; false otherwise.</returns>
        </member>
        <member name="T:Castle.DynamicProxy.IProxyGenerator">
            <summary>
              Provides proxy objects for classes and interfaces.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.IProxyGenerator.Logger">
            <summary>
              Gets or sets the <see cref = "T:Castle.Core.Logging.ILogger" /> that this <see cref = "T:Castle.DynamicProxy.ProxyGenerator" /> log to.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.IProxyGenerator.ProxyBuilder">
            <summary>
              Gets the proxy builder instance used to generate proxy types.
            </summary>
            <value>The proxy builder.</value>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTarget``1(``0,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>Object proxying calls to members of <typeparamref name = "TInterface" /> on <paramref name = "target" /> object.</returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" />is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTarget``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" />is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see
               cref = "M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types  on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget``1(Castle.DynamicProxy.IInterceptor)">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on target object generated at runtime with given <paramref
               name = "interceptor" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface which will be proxied.</typeparam>
            <param name = "interceptor">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptor" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              As a result of that also at least one <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementation must be provided.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget``1(Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface which will be proxied.</typeparam>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              As a result of that also at least one <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementation must be provided.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget``1(Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface which will be proxied.</typeparam>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              As a result of that also at least one <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementation must be provided.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,Castle.DynamicProxy.IInterceptor)">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptor" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "interceptor">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptor" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" />  is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of <paramref name = "additionalInterfacesToProxy" /> to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget``1(``0,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no parameterless constructor exists on type <paramref
               name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy``1(Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy``1(Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,System.Type[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no parameterless constructor exists on type <paramref
               name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyGenerator.CreateClassProxy(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="T:Castle.DynamicProxy.IProxyTargetAccessor">
            <summary>
              Exposes access to the target object and interceptors of proxy objects.
              This is a DynamicProxy infrastructure interface and should not be implemented yourself.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyTargetAccessor.DynProxyGetTarget">
            <summary>
              Get the proxy target (note that null is a valid target!)
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyTargetAccessor.DynProxySetTarget(System.Object)">
            <summary>
              Set the proxy target.
            </summary>
            <param name="target">New proxy target.</param>
        </member>
        <member name="M:Castle.DynamicProxy.IProxyTargetAccessor.GetInterceptors">
            <summary>
              Gets the interceptors for the proxy
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.MixinData.#ctor(System.Collections.Generic.IEnumerable{System.Object})">
            <summary>
              Because we need to cache the types based on the mixed in mixins, we do the following here:
              - Get all the mixin interfaces
              - Sort them by full name
              - Return them by position
            
            The idea is to have reproducible behavior for the case that mixins are registered in different orders.
            This method is here because it is required 
            </summary>
        </member>
        <member name="F:Castle.DynamicProxy.ModuleScope.DEFAULT_FILE_NAME">
            <summary>
              The default file name used when the assembly is saved using <see cref = "F:Castle.DynamicProxy.ModuleScope.DEFAULT_FILE_NAME" />.
            </summary>
        </member>
        <member name="F:Castle.DynamicProxy.ModuleScope.DEFAULT_ASSEMBLY_NAME">
            <summary>
              The default assembly (simple) name used for the assemblies generated by a <see cref = "T:Castle.DynamicProxy.ModuleScope" /> instance.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.#ctor">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ModuleScope" /> class; assemblies created by this instance will not be saved.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.#ctor(System.Boolean)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ModuleScope" /> class, allowing to specify whether the assemblies generated by this instance
              should be saved.
            </summary>
            <param name = "savePhysicalAssembly">If set to <c>true</c> saves the generated module.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.#ctor(System.Boolean,System.Boolean)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ModuleScope" /> class, allowing to specify whether the assemblies generated by this instance
              should be saved.
            </summary>
            <param name = "savePhysicalAssembly">If set to <c>true</c> saves the generated module.</param>
            <param name = "disableSignedModule">If set to <c>true</c> disables ability to generate signed module. This should be used in cases where ran under constrained permissions.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.#ctor(System.Boolean,System.Boolean,System.String,System.String,System.String,System.String)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ModuleScope" /> class, allowing to specify whether the assemblies generated by this instance
              should be saved and what simple names are to be assigned to them.
            </summary>
            <param name = "savePhysicalAssembly">If set to <c>true</c> saves the generated module.</param>
            <param name = "disableSignedModule">If set to <c>true</c> disables ability to generate signed module. This should be used in cases where ran under constrained permissions.</param>
            <param name = "strongAssemblyName">The simple name of the strong-named assembly generated by this <see
               cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
            <param name = "strongModulePath">The path and file name of the manifest module of the strong-named assembly generated by this <see
               cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
            <param name = "weakAssemblyName">The simple name of the weak-named assembly generated by this <see cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
            <param name = "weakModulePath">The path and file name of the manifest module of the weak-named assembly generated by this <see
               cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.#ctor(System.Boolean,System.Boolean,Castle.DynamicProxy.Generators.INamingScope,System.String,System.String,System.String,System.String)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ModuleScope" /> class, allowing to specify whether the assemblies generated by this instance
              should be saved and what simple names are to be assigned to them.
            </summary>
            <param name = "savePhysicalAssembly">If set to <c>true</c> saves the generated module.</param>
            <param name = "disableSignedModule">If set to <c>true</c> disables ability to generate signed module. This should be used in cases where ran under constrained permissions.</param>
            <param name = "namingScope">Naming scope used to provide unique names to generated types and their members (usually via sub-scopes).</param>
            <param name = "strongAssemblyName">The simple name of the strong-named assembly generated by this <see
               cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
            <param name = "strongModulePath">The path and file name of the manifest module of the strong-named assembly generated by this <see
               cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
            <param name = "weakAssemblyName">The simple name of the weak-named assembly generated by this <see cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
            <param name = "weakModulePath">The path and file name of the manifest module of the weak-named assembly generated by this <see
               cref = "T:Castle.DynamicProxy.ModuleScope" />.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.GetKeyPair">
            <summary>
              Gets the key pair used to sign the strong-named assembly generated by this <see cref = "T:Castle.DynamicProxy.ModuleScope" />.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.ModuleScope.StrongNamedModule">
            <summary>
              Gets the strong-named module generated by this scope, or <see langword = "null" /> if none has yet been generated.
            </summary>
            <value>The strong-named module generated by this scope, or <see langword = "null" /> if none has yet been generated.</value>
        </member>
        <member name="P:Castle.DynamicProxy.ModuleScope.StrongNamedModuleName">
            <summary>
              Gets the file name of the strongly named module generated by this scope.
            </summary>
            <value>The file name of the strongly named module generated by this scope.</value>
        </member>
        <member name="P:Castle.DynamicProxy.ModuleScope.WeakNamedModule">
            <summary>
              Gets the weak-named module generated by this scope, or <see langword = "null" /> if none has yet been generated.
            </summary>
            <value>The weak-named module generated by this scope, or <see langword = "null" /> if none has yet been generated.</value>
        </member>
        <member name="P:Castle.DynamicProxy.ModuleScope.WeakNamedModuleName">
            <summary>
              Gets the file name of the weakly named module generated by this scope.
            </summary>
            <value>The file name of the weakly named module generated by this scope.</value>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.ObtainDynamicModule(System.Boolean)">
            <summary>
              Gets the specified module generated by this scope, creating a new one if none has yet been generated.
            </summary>
            <param name = "isStrongNamed">If set to true, a strong-named module is returned; otherwise, a weak-named module is returned.</param>
            <returns>A strong-named or weak-named module generated by this scope, as specified by the <paramref
               name = "isStrongNamed" /> parameter.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.ObtainDynamicModuleWithStrongName">
            <summary>
              Gets the strong-named module generated by this scope, creating a new one if none has yet been generated.
            </summary>
            <returns>A strong-named module generated by this scope.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ModuleScope.ObtainDynamicModuleWithWeakName">
            <summary>
              Gets the weak-named module generated by this scope, creating a new one if none has yet been generated.
            </summary>
            <returns>A weak-named module generated by this scope.</returns>
        </member>
        <member name="T:Castle.DynamicProxy.ProxyGenerationOptions">
            <summary>
              <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> allows customization of the behavior of proxies created by
              an <see cref="T:Castle.DynamicProxy.IProxyGenerator"/> (or proxy types generated by an <see cref="T:Castle.DynamicProxy.IProxyBuilder"/>).
              <para>
                You should not modify an instance of <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> once it has been
                used to create a proxy (or proxy type).
              </para>
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerationOptions.#ctor(Castle.DynamicProxy.IProxyGenerationHook)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ProxyGenerationOptions" /> class.
            </summary>
            <param name = "hook">The hook.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerationOptions.#ctor">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ProxyGenerationOptions" /> class.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.ProxyGenerationOptions.Hook">
            <summary>
              Gets or sets the <see cref="T:Castle.DynamicProxy.IProxyGenerationHook"/> that should be used during proxy type
              generation. Defaults to an instance of <see cref="T:Castle.DynamicProxy.AllMethodsHook"/>.
              <para>
                You should not modify this property once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.ProxyGenerationOptions.Selector">
            <summary>
              Gets or sets the <see cref="T:Castle.DynamicProxy.IInterceptorSelector"/> that should be used by created proxies
              to determine which interceptors to use for an interception. If set to <see langword="null"/>
              (which is the default), created proxies will not use any selector.
              <para>
                You should not modify this property once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.ProxyGenerationOptions.BaseTypeForInterfaceProxy">
            <summary>
              Gets or sets the class type from which generated interface proxy types will be derived.
              Defaults to <c><see langword="typeof"/>(<see langword="object"/>)</c>.
              <para>
                You should not modify this property once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.ProxyGenerationOptions.AdditionalAttributes">
            <summary>
              Gets the collection of additional custom attributes that will be put on generated proxy types.
              This collection is initially empty.
              <para>
                You should not modify this collection once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerationOptions.AddDelegateTypeMixin(System.Type)">
            <summary>
              Adds a delegate type to the list of mixins that will be added to generated proxies.
              That is, generated proxies will have a `Invoke` method with a signature matching that
              of the specified <paramref name="delegateType"/>.
              <para>
                You should not call this method once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
            <param name="delegateType">The delegate type whose `Invoke` method should be reproduced in generated proxies.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="delegateType"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="delegateType"/> is not a delegate type.</exception>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerationOptions.AddDelegateMixin(System.Delegate)">
            <summary>
              Adds a delegate to be mixed into generated proxies. The <paramref name="delegate"/>
              will act as the target for calls to a `Invoke` method with a signature matching that
              of the delegate.
              <para>
                You should not call this method once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
            <param name="delegate">The delegate that should act as the target for calls to `Invoke` methods with a matching signature.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="delegate"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerationOptions.AddMixinInstance(System.Object)">
            <summary>
              Mixes the interfaces implemented by the specified <paramref name="instance"/> object into
              created proxies, and uses <paramref name="instance"/> as the target for these mixed-in interfaces.
              <para>
                You should not call this method once this <see cref="T:Castle.DynamicProxy.ProxyGenerationOptions"/> instance
                has been used to create a proxy.
              </para>
            </summary>
            <param name="instance">The object that should act as the target for all of its implemented interfaces' methods.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="instance"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="instance"/> is an instance of <see cref="T:System.Type"/>.</exception>
        </member>
        <member name="T:Castle.DynamicProxy.ProxyGenerator">
            <summary>
              Provides proxy objects for classes and interfaces.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.#ctor(Castle.DynamicProxy.IProxyBuilder)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ProxyGenerator" /> class.
            </summary>
            <param name = "builder">Proxy types builder.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.#ctor">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ProxyGenerator" /> class.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.#ctor(System.Boolean)">
            <summary>
              Initializes a new instance of the <see cref = "T:Castle.DynamicProxy.ProxyGenerator" /> class.
            </summary>
            <param name="disableSignedModule">If <c>true</c> forces all types to be generated into an unsigned module.</param>
        </member>
        <member name="P:Castle.DynamicProxy.ProxyGenerator.Logger">
            <summary>
              Gets or sets the <see cref = "T:Castle.Core.Logging.ILogger" /> that this <see cref = "T:Castle.DynamicProxy.ProxyGenerator" /> log to.
            </summary>
        </member>
        <member name="P:Castle.DynamicProxy.ProxyGenerator.ProxyBuilder">
            <summary>
              Gets the proxy builder instance used to generate proxy types.
            </summary>
            <value>The proxy builder.</value>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTarget``1(``0,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>Object proxying calls to members of <typeparamref name = "TInterface" /> on <paramref name = "target" /> object.</returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" />is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTarget``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" />is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see
               cref = "M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types  on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on <paramref name = "target" /> object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method generates new proxy type for each type of <paramref name = "target" />, which affects performance. If you don't want to proxy types differently depending on the type of the target
              use <see cref = "M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])" /> method.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref
               name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref
               name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref
               name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref
               name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref
               name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithTargetInterface(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on <paramref name = "target" /> object with given <paramref name = "interceptors" />.
              Interceptors can use <see cref = "T:Castle.DynamicProxy.IChangeProxyTarget" /> interface to provide other target for method invocation than default <paramref name = "target" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface implemented by <paramref name = "target" /> which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types on <paramref name = "target" /> object or alternative implementation swapped at runtime by an interceptor.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "target" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "target" /> does not implement <paramref name = "interfaceToProxy" /> interface.</exception>
            <exception cref = "T:System.MissingMethodException">Thrown when no default constructor exists on actual type of <paramref name = "target" /> object.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of actual type of <paramref name = "target" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget``1(Castle.DynamicProxy.IInterceptor)">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on target object generated at runtime with given <paramref
               name = "interceptor" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface which will be proxied.</typeparam>
            <param name = "interceptor">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptor" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              As a result of that also at least one <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementation must be provided.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget``1(Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface which will be proxied.</typeparam>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              As a result of that also at least one <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementation must be provided.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget``1(Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <typeparamref name = "TInterface" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TInterface">Type of the interface which will be proxied.</typeparam>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <typeparamref name = "TInterface" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TInterface" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              As a result of that also at least one <see cref = "T:Castle.DynamicProxy.IInterceptor" /> implementation must be provided.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,Castle.DynamicProxy.IInterceptor)">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptor" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "interceptor">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptor" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> type on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of interfaces to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" />  is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to members of interface <paramref name = "interfaceToProxy" /> on target object generated at runtime with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "interfaceToProxy">Type of the interface which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              Object proxying calls to members of <paramref name = "interfaceToProxy" /> and <paramref
               name = "additionalInterfacesToProxy" /> types on generated target object.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interfaceToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "interceptors" /> array is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "interfaceToProxy" /> is not an interface type.</exception>
            <remarks>
              Since this method uses an empty-shell implementation of <paramref name = "additionalInterfacesToProxy" /> to proxy generated at runtime, the actual implementation of proxied methods must be provided by given <see
               cref = "T:Castle.DynamicProxy.IInterceptor" /> implementations.
              They are responsible for setting return value (and out parameters) on proxied methods. It is also illegal for an interceptor to call <see
               cref = "M:Castle.DynamicProxy.IInvocation.Proceed" />, since there's no actual implementation to proceed with.
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget``1(``0,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget``1(``0,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no parameterless constructor exists on type <paramref
               name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyWithTarget(System.Type,System.Type[],System.Object,Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "target">The target object, calls to which will be intercepted.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy``1(Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy``1(Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <typeparamref name = "TClass" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <typeparam name = "TClass">Type of class which will be proxied.</typeparam>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <typeparamref name = "TClass" /> proxying calls to virtual members of <typeparamref
               name = "TClass" /> type.
            </returns>
            <exception cref = "T:System.ArgumentException">Thrown when given <typeparamref name = "TClass" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <typeparamref name = "TClass" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <typeparamref name = "TClass" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,System.Type[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no parameterless constructor exists on type <paramref
               name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> type.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions,Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no default constructor exists on type <paramref name = "classToProxy" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when default constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions,System.Object[],Castle.DynamicProxy.IInterceptor[])">
            <summary>
              Creates proxy object intercepting calls to virtual members of type <paramref name = "classToProxy" /> on newly created instance of that type with given <paramref
               name = "interceptors" />.
            </summary>
            <param name = "classToProxy">Type of class which will be proxied.</param>
            <param name = "additionalInterfacesToProxy">Additional interface types. Calls to their members will be proxied as well.</param>
            <param name = "options">The proxy generation options used to influence generated proxy type and object.</param>
            <param name = "constructorArguments">Arguments of constructor of type <paramref name = "classToProxy" /> which should be used to create a new instance of that type.</param>
            <param name = "interceptors">The interceptors called during the invocation of proxied methods.</param>
            <returns>
              New object of type <paramref name = "classToProxy" /> proxying calls to virtual members of <paramref
               name = "classToProxy" /> and <paramref name = "additionalInterfacesToProxy" /> types.
            </returns>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "classToProxy" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentNullException">Thrown when given <paramref name = "options" /> object is a null reference (Nothing in Visual Basic).</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> or any of <paramref
               name = "additionalInterfacesToProxy" /> is a generic type definition.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when given <paramref name = "classToProxy" /> is not a class type.</exception>
            <exception cref = "T:System.ArgumentException">Thrown when no constructor exists on type <paramref name = "classToProxy" /> with parameters matching <paramref
               name = "constructorArguments" />.</exception>
            <exception cref = "T:System.Reflection.TargetInvocationException">Thrown when constructor of type <paramref name = "classToProxy" /> throws an exception.</exception>
            <remarks>
              This method uses <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation to generate a proxy type.
              As such caller should expect any type of exception that given <see cref = "T:Castle.DynamicProxy.IProxyBuilder" /> implementation may throw.
            </remarks>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateClassProxyType(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates the proxy type for class proxy with given <paramref name = "classToProxy" /> class, implementing given <paramref
               name = "additionalInterfacesToProxy" /> and using provided <paramref name = "options" />.
            </summary>
            <param name = "classToProxy">The base class for proxy type.</param>
            <param name = "additionalInterfacesToProxy">The interfaces that proxy type should implement.</param>
            <param name = "options">The options for proxy generation process.</param>
            <returns><see cref = "T:System.Type" /> of proxy.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyTypeWithTarget(System.Type,System.Type[],System.Type,Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates the proxy type for interface proxy with target for given <paramref name = "interfaceToProxy" /> interface, implementing given <paramref
               name = "additionalInterfacesToProxy" /> on given <paramref name = "targetType" /> and using provided <paramref
               name = "options" />.
            </summary>
            <param name = "interfaceToProxy">The interface proxy type should implement.</param>
            <param name = "additionalInterfacesToProxy">The additional interfaces proxy type should implement.</param>
            <param name = "targetType">Actual type that the proxy type will encompass.</param>
            <param name = "options">The options for proxy generation process.</param>
            <returns><see cref = "T:System.Type" /> of proxy.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyTypeWithTargetInterface(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates the proxy type for interface proxy with target interface for given <paramref name = "interfaceToProxy" /> interface, implementing given <paramref
               name = "additionalInterfacesToProxy" /> on given <paramref name = "interfaceToProxy" /> and using provided <paramref
               name = "options" />.
            </summary>
            <param name = "interfaceToProxy">The interface proxy type should implement.</param>
            <param name = "additionalInterfacesToProxy">The additional interfaces proxy type should implement.</param>
            <param name = "options">The options for proxy generation process.</param>
            <returns><see cref = "T:System.Type" /> of proxy.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyGenerator.CreateInterfaceProxyTypeWithoutTarget(System.Type,System.Type[],Castle.DynamicProxy.ProxyGenerationOptions)">
            <summary>
              Creates the proxy type for interface proxy without target for given <paramref name = "interfaceToProxy" /> interface, implementing given <paramref
               name = "additionalInterfacesToProxy" /> and using provided <paramref name = "options" />.
            </summary>
            <param name = "interfaceToProxy">The interface proxy type should implement.</param>
            <param name = "additionalInterfacesToProxy">The additional interfaces proxy type should implement.</param>
            <param name = "options">The options for proxy generation process.</param>
            <returns><see cref = "T:System.Type" /> of proxy.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.CreateDelegateToMixin``1(System.Object)">
            <summary>
              Creates a delegate of the specified type to a suitable `Invoke` method
              on the given <paramref name="proxy"/> instance.
            </summary>
            <param name="proxy">The proxy instance to which the delegate should be bound.</param>
            <typeparam name="TDelegate">The type of delegate that should be created.</typeparam>
            <exception cref="T:System.MissingMethodException">
              The <paramref name="proxy"/> does not have an `Invoke` method that is compatible with
              the requested <typeparamref name="TDelegate"/> type.
            </exception>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.CreateDelegateToMixin(System.Object,System.Type)">
            <summary>
              Creates a delegate of the specified type to a suitable `Invoke` method
              on the given <paramref name="proxy"/> instance.
            </summary>
            <param name="proxy">The proxy instance to which the delegate should be bound.</param>
            <param name="delegateType">The type of delegate that should be created.</param>
            <exception cref="T:System.MissingMethodException">
              The <paramref name="proxy"/> does not have an `Invoke` method that is compatible with
              the requested <paramref name="delegateType"/>.
            </exception>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.IsAccessible(System.Reflection.MethodBase)">
            <summary>
            Checks whether the specified method is accessible to DynamicProxy.</summary>
            <param name="method">The method to check.</param>
            <returns><c>true</c> if the method is accessible to DynamicProxy, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.IsAccessible(System.Reflection.MethodBase,System.String@)">
            <summary>
            Checks whether the specified method is accessible to DynamicProxy.</summary>
            <param name="method">The method to check.</param>
            <param name="message">If the method is accessible to DynamicProxy, <c>null</c>; otherwise, an explanation of why the method is not accessible.</param>
            <returns><c>true</c> if the method is accessible to DynamicProxy, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.IsAccessible(System.Type)">
            <summary>
            Checks whether the specified type is accessible to DynamicProxy.</summary>
            <param name="type">The type to check.</param>
            <returns><c>true</c> if the type is accessible to DynamicProxy, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.AreInternalsVisibleToDynamicProxy(System.Reflection.Assembly)">
            <summary>
              Determines whether this assembly has internals visible to DynamicProxy.
            </summary>
            <param name="asm">The assembly to inspect.</param>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.IsAccessibleMethod(System.Reflection.MethodBase)">
            <summary>
              Checks whether the specified method is accessible to DynamicProxy.
              Unlike with <see cref="M:Castle.DynamicProxy.ProxyUtil.IsAccessible(System.Reflection.MethodBase)"/>, the declaring type's accessibility is ignored.
            </summary>
            <param name = "method">The method to check.</param>
            <returns><c>true</c> if the method is accessible to DynamicProxy, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Castle.DynamicProxy.ProxyUtil.IsInternal(System.Reflection.MethodBase)">
            <summary>
              Determines whether the specified method is internal.
            </summary>
            <param name = "method">The method.</param>
            <returns>
              <c>true</c> if the specified method is internal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Castle.DynamicProxy.Tokens.InvocationMethods">
            <summary>
              Holds <see cref = "T:System.Reflection.MethodInfo" /> objects representing methods of <see cref = "T:Castle.DynamicProxy.AbstractInvocation" /> class.
            </summary>
        </member>
    </members>
</doc>
