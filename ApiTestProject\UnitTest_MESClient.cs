﻿using DataAccessor;
using Entities.Models;
using Infrastructure.Utilities;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using NewtonsoftJsonConverter;
using SecurityEncDecryptExtensions;
using Services.AstroenergyService;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [Microsoft.VisualStudio.TestTools.UnitTesting.TestClass]
    public class UnitTest_MESClient
    {
        [TestMethod]
        public void Hashtable_JsonConvert()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("ID", typeof(decimal));
           var r= dt.NewRow();
            r["ID"] = 1;
            dt.Rows.Add(r);
            r = dt.NewRow();
            r["ID"] = 2.0;
            dt.Rows.Add(r);
            r = dt.NewRow();
            r["ID"] = 3.1;
            dt.Rows.Add(r);

            var s=  JsonConvert.SerializeObject(dt, new JsonSerializerSettings
          {
              Converters = { new CustomDecimalConverter() }
          });
          var h=  JsonConvert.DeserializeObject<Dictionary<string,object>>(s);
            foreach (var item in h)
            {
                //JsonConvert.DeserializeObject<Dictionary<string, object>>(item.Value);
            }
        }

        [TestMethod]
        public void test_GetData_success()
        {
           var s= new MESClientService().GetData(
                    @"select distinct smb.sampletestname,
                                        j.jaisedited as isedited,
                                        j.jaisserialport as isserialport,
                                        sm.lowerspecificationlimit as lowerValue,
                                        sm.upperspecificationlimit as upperValue,
                                        uom.uomname as uom,
                                        j.japath as path,
                                        sam.jaspcachievemethodname as achievemethod,
                                        sam.jacollectmuldata as CollectMulData,
                                        sam.jacollectmulfile as CollectMulFile,
                                        sam.jafiletype as FileType,
                                        sam.jaflag as  Flag,
                                        sb.SpecName,fa.FactoryName
                                      from jaspcplaningdetail j,
                                           spec            sp,
                                           specbase        sb,
                                           factory         fa,
                                           sampletest      sm,
                                           sampletestbase  smb,
                                           uom             uom,
                                           jaspcachievemethod sam
                                     where j.factoryid = fa.factoryid
                                            and j.assemblywipspecid = sp.specid
                                            and sp.specbaseid = sb.specbaseid
                                            and j.jasampletestid = sm.sampletestid
                                            and sm.sampletestbaseid = smb.sampletestbaseid  
                                            and j.jaspcachievemethodid = sam.jaspcachievemethodid(+)
                                            and sm.uomid = uom.uomid(+)
                                            and SM.STATUS = 1 ".AesEncrypt("21E28EB6405E8177E0655D87854AA01D"), new Dictionary<string, object> { { "Spec", "制绒" }, { "factoryname", "C4-1" } }, 0).GetAwaiter().GetResult();
            var ss = JsonConvert.SerializeObject(s);
            var sss = JsonConvert.SerializeObject(s, new JsonSerializerSettings
            {
                Converters = { new CustomDecimalConverter() }
            });
        }

        [TestMethod]
        public void test_GetData_mes_db()
        {
            var s = new MESClientService().GetData(Convert.ToBase64String(Encoding.Default.GetBytes(@" SELECT P.PRODUCTID               AS PRODUCTID,
                            PB.PRODUCTNAME            AS PRODUCTNAME,
                            P.DESCRIPTION             AS PRODUCTDESCRIPTION,
                            LG.LABELGROUPID           AS LABELGROUPID,
                            LG.LABELGROUPNAME         AS LABELGROUPNAME,
                            LG.DESCRIPTION            AS LABELGROUPDESCRIPTION,
                            LT.LABELTEMPLATEID        AS LABELTEMPLATEID,
                            LT.LABELTEMPLATENAME      AS LABELTEMPLATENAME,
                            LT.DESCRIPTION            AS LABELTEMPLATEDESCRIPTION,
                            LTT.LABELTEMPLATETYPENAME AS LABELTEMPLATETYPENAME,
                            LT.TEMPLATEFULLNAME       AS TEMPLATEFULLNAME,
                            LT.TEMPLATEABSOLUTEPATH   AS TEMPLATEABSOLUTEPATH,
                            LT.TEMPLATERELATIVEPATH   AS TEMPLATERELATIVEPATH
                     FROM LABELTEMPLATE LT
                              LEFT JOIN LABELTEMPLATETYPE LTT ON LTT.LABELTEMPLATETYPEID = LT.LABELTEMPLATETYPEID
                              LEFT JOIN LABELGROUP LG ON LG.LABELGROUPID = LT.LABELGROUPID
                              LEFT JOIN PRODUCT P ON P.PRODUCTID = LG.PRODUCTID
                              LEFT JOIN PRODUCTBASE PB ON PB.PRODUCTBASEID = P.PRODUCTBASEID
                     WHERE  LTT.LABELTEMPLATETYPENAME = :LABELTEMPLATETYPENAME   ")), new Dictionary<string, object> { { "LABELTEMPLATETYPENAME", "xzczc" } },0).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void test_GetData_without_column()
        {
            var s = new MESClientService().GetData(Convert.ToBase64String(Encoding.Default.GetBytes("SELECT SPC FROM JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE ")), null,7).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void test_GetData_params_not_contain_need()
        {
            var s = new MESClientService().GetData(Convert.ToBase64String(Encoding.Default.GetBytes(@" SELECT P.PRODUCTID               AS PRODUCTID,
                            PB.PRODUCTNAME            AS PRODUCTNAME,
                            P.DESCRIPTION             AS PRODUCTDESCRIPTION,
                            LG.LABELGROUPID           AS LABELGROUPID,
                            LG.LABELGROUPNAME         AS LABELGROUPNAME,
                            LG.DESCRIPTION            AS LABELGROUPDESCRIPTION,
                            LT.LABELTEMPLATEID        AS LABELTEMPLATEID,
                            LT.LABELTEMPLATENAME      AS LABELTEMPLATENAME,
                            LT.DESCRIPTION            AS LABELTEMPLATEDESCRIPTION,
                            LTT.LABELTEMPLATETYPENAME AS LABELTEMPLATETYPENAME,
                            LT.TEMPLATEFULLNAME       AS TEMPLATEFULLNAME,
                            LT.TEMPLATEABSOLUTEPATH   AS TEMPLATEABSOLUTEPATH,
                            LT.TEMPLATERELATIVEPATH   AS TEMPLATERELATIVEPATH
                     FROM LABELTEMPLATE LT
                              LEFT JOIN LABELTEMPLATETYPE LTT ON LTT.LABELTEMPLATETYPEID = LT.LABELTEMPLATETYPEID
                              LEFT JOIN LABELGROUP LG ON LG.LABELGROUPID = LT.LABELGROUPID
                              LEFT JOIN PRODUCT P ON P.PRODUCTID = LG.PRODUCTID
                              LEFT JOIN PRODUCTBASE PB ON PB.PRODUCTBASEID = P.PRODUCTBASEID
                     WHERE  LTT.LABELTEMPLATETYPENAME = :LABELTEMPLATETYPENAME   ")), null, 0).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void test_GetData_query_single()
        {
            var s = new MESClientService().GetData(
                    @"select distinct smb.sampletestname,
                                        j.jaisedited as isedited,
                                        j.jaisserialport as isserialport,
                                        sm.lowerspecificationlimit as lowerValue,
                                        sm.upperspecificationlimit as upperValue,
                                        uom.uomname as uom,
                                        j.japath as path,
                                        sam.jaspcachievemethodname as achievemethod,
                                        sam.jacollectmuldata as CollectMulData,
                                        sam.jacollectmulfile as CollectMulFile,
                                        sam.jafiletype as FileType,
                                        sam.jaflag as  Flag,
                                        sb.SpecName,fa.FactoryName
                                      from jaspcplaningdetail j,
                                           spec            sp,
                                           specbase        sb,
                                           factory         fa,
                                           sampletest      sm,
                                           sampletestbase  smb,
                                           uom             uom,
                                           jaspcachievemethod sam
                                     where j.factoryid = fa.factoryid
                                            and j.assemblywipspecid = sp.specid
                                            and sp.specbaseid = sb.specbaseid
                                            and j.jasampletestid = sm.sampletestid
                                            and sm.sampletestbaseid = smb.sampletestbaseid  
                                            and j.jaspcachievemethodid = sam.jaspcachievemethodid(+)
                                            and sm.uomid = uom.uomid(+)
                                            and SM.STATUS = 1 ".AesEncrypt("21E28EB6405E8177E0655D87854AA01D"), new Dictionary<string, object> { {"Spec","制绒" },{ "factoryname","C4-1"} }, 0).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void test_SaveData()
        {
             new MESClientService().SaveData(new Dictionary<string, string> { { "1", "UPDATE ZTDCMESDBMID.JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE x SET x.NAME=:name WHERE x.ID=:id".AesEncrypt("21E28EB6405E8177E0655D87854AA01D") } }, new Dictionary<string, object> { { "1",new {name= "电池-电池1",id=5001 } } },7).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void test_BatchInsert()
        {
           
        }

        [TestMethod]
        public void test_DeleteSpcData()
        {
            
        }

        [TestMethod]
        [DataRow("2025-01-03 11:03:11", "2025-01-03 10:30:00", "2025-01-03 10:40:00")]
        [DataRow("2025-01-03 11:13:11", "2025-01-03 10:40:00", "2025-01-03 10:50:00")]
        [DataRow("2025-01-03 11:23:11", "2025-01-03 10:50:00", "2025-01-03 11:00:00")]
        [DataRow("2025-01-03 11:33:11", "2025-01-03 11:00:00", "2025-01-03 11:10:00")]
        [DataRow("2025-01-03 11:43:11", "2025-01-03 11:10:00", "2025-01-03 11:20:00")]
        [DataRow("2025-01-03 11:53:11", "2025-01-03 11:20:00", "2025-01-03 11:30:00")]
        [DataRow("2025-01-03 11:00:00", "2025-01-03 10:30:00", "2025-01-03 10:40:00")]
        [DataRow("2025-01-03 11:10:00", "2025-01-03 10:40:00", "2025-01-03 10:50:00")]
        [DataRow("2025-01-03 11:50:00", "2025-01-03 11:20:00", "2025-01-03 11:30:00")]
        public void test_time(string now, string actStartTime, string actEndTime)
        {
            // 将一个小时分成6等分（0min-10min作为第一个时间段），判断当前时间处在哪个时间，并将这个时间段最小值作为startTime,最大值作为endTime
            // 1. 得到上一个小时的数据
            DateTime lastHour = DateTime.Parse(now).AddMinutes(-30);
            //1. 获取当前时间的分钟数
            int minute = lastHour.Minute;
            //2. 判断分钟数的个位
            int one = minute % 10;
            //3. 使用lastHour减去个位数，得到startTime
            string startTime = lastHour.AddMinutes(-one).ToString($"yyyy-MM-dd HH:mm:00");
            string endTime = lastHour.AddMinutes(10 - one).ToString($"yyyy-MM-dd HH:mm:00");

            Assert.AreEqual(actStartTime, startTime);
            Assert.AreEqual(actEndTime, endTime);
        }


        [TestMethod]
        public void test_BatchUpdate()
        {
            using (var db = new BaseRepository(DBProvider.MesDbProvider).GetSqlSugarClient())
            {
                var query = db.Queryable<MfgOrder>()
     .Select(m => new MfgOrder
     {
         ProductId = m.ProductId == "0000000000000000"
             ? SqlFunc.Subqueryable<ProductBase>().Where(pb => pb.ProductBaseId == m.ProductBaseId).Select(pb => pb.RevOfRcdId)
             : m.ProductId,
         ProductBaseId = m.ProductBaseId
     })
     .ToList();
                Assert.IsTrue(query.Count>0);
            }


           
        }

        class MfgOrder
        {
            public string ProductId { get; set; }
            public string ProductBaseId { get; set; }
        }

        class ProductBase
        {
            public string ProductBaseId { get; set; }
            public string RevOfRcdId { get; set; }
        }

    }
}
