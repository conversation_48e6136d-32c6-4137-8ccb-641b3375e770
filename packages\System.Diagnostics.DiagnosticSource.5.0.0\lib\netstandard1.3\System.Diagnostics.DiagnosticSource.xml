﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.DiagnosticSource</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Activity">
      <summary>Represents an operation with context to be used for logging.</summary>
    </member>
    <member name="M:System.Diagnostics.Activity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Activity" /> class.</summary>
      <param name="operationName">The name of the operation.</param>
    </member>
    <member name="M:System.Diagnostics.Activity.AddBaggage(System.String,System.String)">
      <summary>Updates the <see cref="T:System.Diagnostics.Activity" /> to have a new baggage item with the specified key and value.</summary>
      <param name="key">The baggage key.</param>
      <param name="value">The baggage value.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.AddEvent(System.Diagnostics.ActivityEvent)">
      <summary>Adds the specified activity event to the events list.</summary>
      <param name="e">The activity event to add.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.AddTag(System.String,System.Object)">
      <summary>Updates the activity to have a tag with an additional <paramref name="key" /> and <paramref name="value" />.</summary>
      <param name="key">The tag key name.</param>
      <param name="value">The tag value mapped to the input key.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.AddTag(System.String,System.String)">
      <summary>Updates the <see cref="T:System.Diagnostics.Activity" /> to have a new tag with the provided <paramref name="key" /> and <paramref name="value" />.</summary>
      <param name="key">The tag key.</param>
      <param name="value">The tag value.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.Dispose">
      <summary>Stops the activity if it is already started and notifies any event listeners. Nothing will happen otherwise.</summary>
    </member>
    <member name="M:System.Diagnostics.Activity.Dispose(System.Boolean)">
      <summary>When overriden by a derived type, this method releases any allocated resources.</summary>
      <param name="disposing">
        <see langword="false" /> if the method is being called from the finalizer; <see langword="true" /> if calling from user code.</param>
    </member>
    <member name="M:System.Diagnostics.Activity.GetBaggageItem(System.String)">
      <summary>Returns the value of a key-value pair added to the activity with <see cref="M:System.Diagnostics.Activity.AddBaggage(System.String,System.String)" />.</summary>
      <param name="key">The baggage key.</param>
      <returns>The value of the key-value-pair item if it exists, or <see langword="null" /> if it does not exist.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.GetCustomProperty(System.String)">
      <summary>Returns the object mapped to the specified property name.</summary>
      <param name="propertyName">The name associated to the object.</param>
      <returns>The object mapped to the property name, if one is found; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetCustomProperty(System.String,System.Object)">
      <summary>Attaches any custom object to this activity. If the specified <paramref name="propertyName" /> was previously associated with another object, the property will be updated to be associated with the new <paramref name="propertyValue" /> instead. It is recommended to use a unique property name to avoid conflicts with anyone using the same value.</summary>
      <param name="propertyName">The name to associate the value with.</param>
      <param name="propertyValue">The object to attach and map to the property name.</param>
    </member>
    <member name="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)">
      <summary>Updates the <see cref="T:System.Diagnostics.Activity" /> to set its <see cref="P:System.Diagnostics.Activity.Duration" /> as the difference between <see cref="P:System.Diagnostics.Activity.StartTimeUtc" /> and the specified stop time.</summary>
      <param name="endTimeUtc">The UTC stop time.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetIdFormat(System.Diagnostics.ActivityIdFormat)">
      <summary>Sets the ID format on this <see cref="T:System.Diagnostics.Activity" /> before it is started.</summary>
      <param name="format">One of the enumeration values that specifies the format of the <see cref="P:System.Diagnostics.Activity.Id" /> property.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetParentId(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivityTraceFlags)">
      <summary>Sets the parent ID using the W3C convention of a TraceId and a SpanId.</summary>
      <param name="traceId">The parent activity's TraceId.</param>
      <param name="spanId">The parent activity's SpanId.</param>
      <param name="activityTraceFlags">One of the enumeration values that specifies flags defined by the W3C standard that are associated with an activity.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetParentId(System.String)">
      <summary>Updates this <see cref="T:System.Diagnostics.Activity" /> to indicate that the <see cref="T:System.Diagnostics.Activity" /> with an ID of <paramref name="parentId" /> caused this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <param name="parentId">The ID of the parent operation.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetStartTime(System.DateTime)">
      <summary>Sets the start time of this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <param name="startTimeUtc">The <see cref="T:System.Diagnostics.Activity" /> start time in UTC.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetTag(System.String,System.Object)">
      <summary>Adds or update the activity tag with the input key and value.</summary>
      <param name="key">The tag key name.</param>
      <param name="value">The tag value mapped to the input key.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.Start">
      <summary>Starts the activity.</summary>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.Stop">
      <summary>Stops the activity.</summary>
    </member>
    <member name="P:System.Diagnostics.Activity.ActivityTraceFlags">
      <summary>Gets the flags (defined by the W3C ID specification) associated with the activity.</summary>
      <returns>the flags associated with the activity.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Baggage">
      <summary>Gets a collection of key/value pairs that represents information that is passed to children of this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <returns>An enumeration of string-string key-value pairs.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Context">
      <summary>Gets the context of the activity. Context becomes valid only if the activity has been started.</summary>
      <returns>The context of the activity, if the activity has been started; otherwise, returns the default context.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Current">
      <summary>Gets or sets the current operation (<see cref="T:System.Diagnostics.Activity" />) for the current thread.  This flows across async calls.</summary>
      <returns>The current operation for the current thread.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.DefaultIdFormat">
      <summary>Gets or sets the default ID format for the <see cref="T:System.Diagnostics.Activity" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Activity.DisplayName">
      <summary>Gets or sets the display name of the activity.</summary>
      <returns>A string that represents the activity display name.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Duration">
      <summary>Gets the duration of the operation.</summary>
      <returns>The delta between <see cref="P:System.Diagnostics.Activity.StartTimeUtc" /> and the end time if the <see cref="T:System.Diagnostics.Activity" /> has ended (<see cref="M:System.Diagnostics.Activity.Stop" /> or <see cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)" /> was called), or <see cref="F:System.TimeSpan.Zero" /> if the <see cref="T:System.Diagnostics.Activity" /> has not ended and <see cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)" /> was not called.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Events">
      <summary>Gets the list of all the activity events attached to this activity.</summary>
      <returns>An enumeration of activity events attached to this activity. If the activity has no events, returns an empty enumeration.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.ForceDefaultIdFormat">
      <summary>Gets or sets a valud that detrmines if the <see cref="P:System.Diagnostics.Activity.DefaultIdFormat" /> is always used to define the default ID format.</summary>
      <returns>
        <see langword="true" /> to always use the <see cref="P:System.Diagnostics.Activity.DefaultIdFormat" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Id">
      <summary>Gets an identifier that is specific to a particular request.</summary>
      <returns>The activity ID.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.IdFormat">
      <summary>Gets the format for the <see cref="P:System.Diagnostics.Activity.Id" />.</summary>
      <returns>The format for the <see cref="P:System.Diagnostics.Activity.Id" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.IsAllDataRequested">
      <summary>Indicates if the this activity should be populated with all the propagation information, as well as all the other properties such as links, tags and events.</summary>
      <returns>
        <see langword="true" /> if the activity should be populated; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Kind">
      <summary>Gets the relationship between the activity, its parents, and its children in a trace.</summary>
      <returns>One of the enumeration values that indicate relationship between the activity, its parents, and its children in a trace.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Links">
      <summary>Gets the list of all the activity links attached to this activity.</summary>
      <returns>An enumeration of activity links attached to this activity. If the activity has no links, returns an empty enumeration.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.OperationName">
      <summary>Gets the operation name.</summary>
      <returns>The name of the operation.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Parent">
      <summary>Gets the parent <see cref="T:System.Diagnostics.Activity" /> that created this activity.</summary>
      <returns>The parent of this <see cref="T:System.Diagnostics.Activity" />, if it is from the same process, or <see langword="null" /> if this instance has no parent (it is a root activity) or if the parent is from outside the process.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.ParentId">
      <summary>Gets the ID of this activity's parent.</summary>
      <returns>The parent ID, if one exists, or <see langword="null" /> if it does not.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.ParentSpanId">
      <summary>Gets the parent's <see cref="P:System.Diagnostics.Activity.SpanId" />.</summary>
      <returns>The parent's <see cref="P:System.Diagnostics.Activity.SpanId" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Recorded">
      <summary>Gets a value that indicates whether the W3CIdFlags.Recorded flag is set.</summary>
      <returns>
        <see langword="true" /> if the W3CIdFlags.Recorded flag is set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.RootId">
      <summary>Gets the root ID of this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <returns>The root ID, or <see langword="null" /> if the current instance has either a <see cref="P:System.Diagnostics.Activity.ParentId" /> or an <see cref="P:System.Diagnostics.Activity.Id" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Source">
      <summary>Get the activity source associated with this activity.</summary>
    </member>
    <member name="P:System.Diagnostics.Activity.SpanId">
      <summary>Gets the SPAN part of the <see cref="P:System.Diagnostics.Activity.Id" />.</summary>
      <returns>The ID for the SPAN part of <see cref="P:System.Diagnostics.Activity.Id" />, if the <see cref="T:System.Diagnostics.Activity" /> has the W3C format; otherwise, a zero <see langword="SpanId" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.StartTimeUtc">
      <summary>Gets the time when the operation started.</summary>
      <returns>The UTC time that the operation started.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.TagObjects">
      <summary>Returns the list of tags representing information to log along with the activity. This information is not passed on to the children of this activity.</summary>
      <returns>A key-value pair enumeration of tags and objects.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Tags">
      <summary>Gets a collection of key/value pairs that represent information that will be logged along with the <see cref="T:System.Diagnostics.Activity" /> to the logging system.</summary>
      <returns>An enumeration of string-string key-value-pairs.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.TraceId">
      <summary>Gets the TraceId part of the <see cref="P:System.Diagnostics.Activity.Id" />.</summary>
      <returns>The ID for the TraceId part of the <see cref="P:System.Diagnostics.Activity.Id" />, if the ID has the W3C format; otherwise, a zero TraceId.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.TraceStateString">
      <summary>Gets the W3C <see langword="tracestate" /> header.</summary>
      <returns>The W3C <see langword="tracestate" /> header.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityContext">
      <summary>A representation that conforms to the W3C TraceContext specification. It contains two identifiers: a TraceId and a SpanId,  along with a set of common TraceFlags and system-specific TraceState values.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.#ctor(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivityTraceFlags,System.String,System.Boolean)">
      <summary>Construct a new activity context instance using the specified arguments.</summary>
      <param name="traceId">A trace identifier.</param>
      <param name="spanId">A span identifier.</param>
      <param name="traceFlags">Contain details about the trace.</param>
      <param name="traceState">Carries system-specific configuration data.</param>
      <param name="isRemote">Indicates if the context is propagated from a remote parent.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.Equals(System.Diagnostics.ActivityContext)">
      <summary>Indicates whether the current object is equal to another object of the same type.</summary>
      <param name="value">The object to compare to this instance.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.Equals(System.Object)">
      <summary>Determines whether this instance and a specified <see langword="ActivityContext" /> object have the same value.</summary>
      <param name="obj">The object to compare to this instance.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="obj" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.GetHashCode">
      <summary>Provides a hash function for the current <see langword="ActivityContext" /> that's suitable for hashing algorithms and data structures, such as hash tables.</summary>
      <returns>A hash code for the current <see langword="ActivityContext" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.op_Equality(System.Diagnostics.ActivityContext,System.Diagnostics.ActivityContext)">
      <summary>Determines whether two specified <see langword="ActivityContext" /> values are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.op_Inequality(System.Diagnostics.ActivityContext,System.Diagnostics.ActivityContext)">
      <summary>Determines whether two specified <see langword="ActivityContext" /> values are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.Parse(System.String,System.String)">
      <summary>Parses a W3C trace context headers to an <see cref="T:System.Diagnostics.ActivityContext" /> object.</summary>
      <param name="traceParent">The W3C trace parent header.</param>
      <param name="traceState">The trace state.</param>
      <exception cref="T:System.ArgumentException">The trace parent is invalid.</exception>
      <returns>The <see cref="T:System.Diagnostics.ActivityContext" /> object created from the parsing operation.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityContext.TryParse(System.String,System.String,System.Diagnostics.ActivityContext@)">
      <summary>Tries to parse the W3C trace context headers to an <see cref="T:System.Diagnostics.ActivityContext" /> object.</summary>
      <param name="traceParent">The W3C trace parent header.</param>
      <param name="traceState">The W3C trace state.</param>
      <param name="context">When this method returns <see langword="true" />, the <see cref="T:System.Diagnostics.ActivityContext" /> object created from the parsing operation.</param>
      <returns>
        <see langword="true" /> if the parsing was successful; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityContext.IsRemote">
      <summary>Indicates if the activity context was propagated from a remote parent.</summary>
      <returns>
        <see langword="true" /> if it was propagated from a remote parent; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityContext.SpanId">
      <summary>The Id of the request as known by the caller.</summary>
      <returns>The Span Id in the context.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityContext.TraceFlags">
      <summary>The flags defined by the W3C standard along with the ID for the activity.</summary>
      <returns>The context tracing flags.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityContext.TraceId">
      <summary>The trace identifier.</summary>
      <returns>The tracing identifier in the context.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityContext.TraceState">
      <summary>Holds the W3C 'tracestate' header.</summary>
      <returns>A string representing the W3C 'tracestate' header.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityCreationOptions`1">
      <summary>Encapsulates all the information that is sent to the activity listener, to make decisions about the creation of the activity instance, as well as its state.

The possible generic type parameters are <see cref="T:System.Diagnostics.ActivityContext" /> or <see cref="T:System.String" />.</summary>
      <typeparam name="T">The type of the <see langword="Parent" /> property. Should be either <see cref="T:System.String" /> or <see langword="ActivityContext" />.</typeparam>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.Kind">
      <summary>Gets the activity kind which the activity will be created with.</summary>
      <returns>One of the enumeration values that represent an activity kind.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.Links">
      <summary>Gets the enumeration of activity links that the activity will be created with.</summary>
      <returns>An enumeration of activity links.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.Name">
      <summary>Gets the name to use as OperationName of the activity that will get created.</summary>
      <returns>A string representing the activity name.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.Parent">
      <summary>Gets the parent context or parent Id that the activity will get created with.</summary>
      <returns>The parent of the activity, represented either as a <see cref="T:System.String" /> or as an <see cref="T:System.Diagnostics.ActivityContext" />.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.SamplingTags">
      <summary>Gets the collection that is used to add more tags during the sampling process. The added tags are also added to the created Activity if it is decided that it should be created by the <see cref="T:System.Diagnostics.ActivityListener" /> callbacks.</summary>
      <returns>The Activity tags collection.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.Source">
      <summary>Gets the activity source that creates the activity.</summary>
      <returns>An activity source object.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.Tags">
      <summary>Gets the tags that the activity will be created with.</summary>
      <returns>A key-value pair enumeration of tags associated with the activity.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityCreationOptions`1.TraceId">
      <summary>Gets the trace Id to use in the Activity object if it is decided that it should be created by <see cref="T:System.Diagnostics.ActivityListener" /> callbacks.</summary>
      <returns>The trace Id.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityEvent">
      <summary>Represents an event containing a name and a timestamp, as well as an optional list of tags.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityEvent.#ctor(System.String)">
      <summary>Initializes a new activity event instance using the specified name and the current time as the event timestamp.</summary>
      <param name="name">The event name.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityEvent.#ctor(System.String,System.DateTimeOffset,System.Diagnostics.ActivityTagsCollection)">
      <summary>Initializes a new activity event instance using the specified name, timestamp and tags.</summary>
      <param name="name">The event name.</param>
      <param name="timestamp">The event timestamp. Timestamp must only be used for the events that happened in the past, not at the moment of this call.</param>
      <param name="tags">The event tags.</param>
    </member>
    <member name="P:System.Diagnostics.ActivityEvent.Name">
      <summary>Gets the activity event name.</summary>
      <returns>A string representing the activity event name.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityEvent.Tags">
      <summary>Gets the collection of tags associated with the event.</summary>
      <returns>A key-value pair enumeration containing the tags associated with the event.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityEvent.Timestamp">
      <summary>Gets the activity event timestamp.</summary>
      <returns>A datetime offset representing the activity event timestamp.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityIdFormat">
      <summary>Specifies the format of the <see cref="P:System.Diagnostics.Activity.Id" /> property.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityIdFormat.Hierarchical">
      <summary>The hierarchical format.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityIdFormat.Unknown">
      <summary>An unknown format.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityIdFormat.W3C">
      <summary>The W3C format.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivityKind">
      <summary>Describes the relationship between the activity, its parents and its children in a trace.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityKind.Client">
      <summary>Outgoing request to the external component.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityKind.Consumer">
      <summary>Output received from an external component.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityKind.Internal">
      <summary>Internal operation within an application, as opposed to operations with remote parents or children. This is the default value.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityKind.Producer">
      <summary>Output provided to external components.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityKind.Server">
      <summary>Requests incoming from external component.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivityLink">
      <summary>Activities may be linked to zero or more activity context instances that are causally related.

Activity links can point to activity contexts inside a single trace or across different traces.

Activity links can be used to represent batched operations where an activity was initiated by multiple initiating activities, each representing a single incoming item being processed in the batch.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityLink.#ctor(System.Diagnostics.ActivityContext,System.Diagnostics.ActivityTagsCollection)">
      <summary>Constructs a new activity link, which can be linked to an activity.</summary>
      <param name="context">The trace activity context.</param>
      <param name="tags">The key-value pair list of tags associated to the activity context.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityLink.Equals(System.Diagnostics.ActivityLink)">
      <summary>Indicates whether the current activity link is equal to another activity link.</summary>
      <param name="value">The activity link to compare.</param>
      <returns>
        <see langword="true" /> if the current activity link is equal to <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityLink.Equals(System.Object)">
      <summary>Indicates whether the current activity link is equal to another object.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if the current activity link is equal to <paramref name="obj" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityLink.GetHashCode">
      <summary>Provides a hash function for the current <see langword="ActivityLink" /> that's suitable for hashing algorithms and data structures, such as hash tables.</summary>
      <returns>A hash code for the current <see langword="ActivityLink" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityLink.op_Equality(System.Diagnostics.ActivityLink,System.Diagnostics.ActivityLink)">
      <summary>Determines whether two specified <see langword="ActivityLink" /> values are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityLink.op_Inequality(System.Diagnostics.ActivityLink,System.Diagnostics.ActivityLink)">
      <summary>Determines whether two specified <see langword="ActivityLink" /> values are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityLink.Context">
      <summary>Retrieves the activity context inside this activity link.</summary>
    </member>
    <member name="P:System.Diagnostics.ActivityLink.Tags">
      <summary>Retrieves the key-value pair enumeration of tags attached to the activity context.</summary>
      <returns>An enumeration of tags attached to the activity context.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityListener">
      <summary>Allows listening to the start and stop activity events and gives the opportunity to decide creating an activity for sampling scenarios.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityListener.#ctor">
      <summary>Construct a new activity listener object to start listeneing to the activity events.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityListener.Dispose">
      <summary>Unregisters this activity listener object from listening to activity events.</summary>
    </member>
    <member name="P:System.Diagnostics.ActivityListener.ActivityStarted">
      <summary>Gets or sets the callback used to listen to the activity start event.</summary>
      <returns>An activity callback instance used to listen to the activity start event.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityListener.ActivityStopped">
      <summary>Gets or sets the callback used to listen to the activity stop event.</summary>
      <returns>An activity callback instance used to listen to the activity stop event.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityListener.Sample">
      <summary>Gets or sets the callback that is used to decide if creating <see cref="T:System.Diagnostics.Activity" /> objects with a specific data state is allowed.</summary>
      <returns>A sample activity instance.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityListener.SampleUsingParentId">
      <summary>Gets or sets the callback that is used to decide if creating <see cref="T:System.Diagnostics.Activity" /> objects with a specific data state is allowed.</summary>
      <returns>A sample activity instance.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityListener.ShouldListenTo">
      <summary>Gets or sets the callback that allows deciding if activity object events that were created using the activity source object should be listened or not.</summary>
      <returns>
        <see langword="true" /> to listen events; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivitySamplingResult">
      <summary>Enumeration values used by <see cref="T:System.Diagnostics.ActivityListener" /> to indicate the amount of data to collect for the related <see cref="T:System.Diagnostics.Activity" />. Requesting more data causes a greater performance overhead.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivitySamplingResult.AllData">
      <summary>The activity object should be populated with all the propagation information and also all other properties such as Links, Tags, and Events. Using this value causes <see cref="P:System.Diagnostics.Activity.IsAllDataRequested" /> to return <see langword="true" />.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivitySamplingResult.AllDataAndRecorded">
      <summary>The activity object should be populated the same as the <see cref="F:System.Diagnostics.ActivitySamplingResult.AllData" /> case. Additionally, Activity.Recorded is set to <see langword="true" />. For activities using the W3C trace ids, this sets a flag bit in the ID that will be propagated downstream requesting that the trace is recorded everywhere.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivitySamplingResult.None">
      <summary>The activity object does not need to be created.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivitySamplingResult.PropagationData">
      <summary>The activity object needs to be created. It will have a Name, a Source, an Id and Baggage. Other properties are unnecessary and will be ignored by this listener.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivitySource">
      <summary>Provides APIs to create and start <see cref="T:System.Diagnostics.Activity" /> objects and to register <see cref="T:System.Diagnostics.ActivityListener" /> objects to listen to the <see cref="T:System.Diagnostics.Activity" /> events.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.#ctor(System.String,System.String)">
      <summary>Constructs an activity source object with the specified <paramref name="name" />.</summary>
      <param name="name">The name of the activity source object.</param>
      <param name="version">The version of the component publishing the tracing info.</param>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.AddActivityListener(System.Diagnostics.ActivityListener)">
      <summary>Adds a listener to the activity starting and stopping events.</summary>
      <param name="listener">The activity listener object to use for listening to the activity events.</param>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.Dispose">
      <summary>Disposes the activity source object, removes the current instance from the global list, and empties the listeners list.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.HasListeners">
      <summary>Checks if there are any listeners for this activity source.</summary>
      <returns>
        <see langword="true" /> if there is a listener registered for this activity source; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.String,System.Diagnostics.ActivityKind)">
      <summary>Creates a new activity if there are active listeners for it, using the specified name and activity kind.</summary>
      <param name="name">The operation name of the activity.</param>
      <param name="kind">The activity kind.</param>
      <returns>The created activity object, if it had active listeners, or <see langword="null" /> if it has no event listeners.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.String,System.Diagnostics.ActivityKind,System.Diagnostics.ActivityContext,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.DateTimeOffset)">
      <summary>Creates a new activity if there are active listeners for it, using the specified name, activity kind, parent activity context, tags, optional activity link and optional start time.</summary>
      <param name="name">The operation name of the activity.</param>
      <param name="kind">The activity kind.</param>
      <param name="parentContext">The parent <see cref="T:System.Diagnostics.ActivityContext" /> object to initialize the created activity object with.</param>
      <param name="tags">The optional tags list to initialize the created activity object with.</param>
      <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink" /> list to initialize the created activity object with.</param>
      <param name="startTime">The optional start timestamp to set on the created activity object.</param>
      <returns>The created activity object, if it had active listeners, or <see langword="null" /> if it has no event listeners.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.String,System.Diagnostics.ActivityKind,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.DateTimeOffset)">
      <summary>Creates a new activity if there are active listeners for it, using the specified name, activity kind, parent Id, tags, optional activity links and optional start time.</summary>
      <param name="name">The operation name of the activity.</param>
      <param name="kind">The activity kind.</param>
      <param name="parentId">The parent Id to initialize the created activity object with.</param>
      <param name="tags">The optional tags list to initialize the created activity object with.</param>
      <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink" /> list to initialize the created activity object with.</param>
      <param name="startTime">The optional start timestamp to set on the created activity object.</param>
      <returns>The created activity object, if it had active listeners, or <see langword="null" /> if it has no event listeners.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivitySource.Name">
      <summary>Returns the activity source name.</summary>
      <returns>A string that represents the activity source name.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivitySource.Version">
      <summary>Returns the activity source version.</summary>
      <returns>A string that represents the activity source version.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivitySpanId">
      <summary>Represents a <see cref="P:System.Diagnostics.Activity.SpanId" /> formatted based on a W3C standard.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CopyTo(System.Span{System.Byte})">
      <summary>Copies the 8 bytes of the current <see cref="T:System.Diagnostics.ActivitySpanId" /> to a specified span.</summary>
      <param name="destination">The span to which the 8 bytes of the SpanID are to be copied.</param>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateFromBytes(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> value from a read-only span of eight bytes.</summary>
      <param name="idData">A read-only span of eight bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain eight bytes.</exception>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateFromString(System.ReadOnlySpan{System.Char})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> value from a read-only span of 16 hexadecimal characters.</summary>
      <param name="idData">A span that contains 16 hexadecimal characters.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain 16 hexadecimal characters.

-or-
          
The characters in <paramref name="idData" /> are not all lower-case hexadecimal characters or all zeros.</exception>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateFromUtf8String(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> value from a read-only span of UTF8-encoded bytes.</summary>
      <param name="idData">A read-only span of UTF8-encoded bytes.</param>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateRandom">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> based on a random number (that is very likely to be unique).</summary>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.Equals(System.Diagnostics.ActivitySpanId)">
      <summary>Determines whether this instance and the specified <see cref="T:System.Diagnostics.ActivitySpanId" /> instance have the same value.</summary>
      <param name="spanId">The instance to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="spanId" /> has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.Equals(System.Object)">
      <summary>the current instance and a specified object, which also must be an <see cref="T:System.Diagnostics.ActivitySpanId" /> instance, have the same value.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see cref="T:System.Diagnostics.ActivitySpanId" /> and has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.GetHashCode">
      <summary>Returns the hash code of the SpanId.</summary>
      <returns>The hash code of the SpanId.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.op_Equality(System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivitySpanId)">
      <summary>Determines whether two specified <see cref="T:System.Diagnostics.ActivitySpanId" /> instances have the same value.</summary>
      <param name="spanId1">The first instance to compare.</param>
      <param name="spandId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the SpanId of <paramref name="spanId1" /> is the same as the SpanId of <paramref name="spandId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.op_Inequality(System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivitySpanId)">
      <summary>Determine whether two specified <see cref="T:System.Diagnostics.ActivitySpanId" /> instances have unequal values.</summary>
      <param name="spanId1">The first instance to compare.</param>
      <param name="spandId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the SpanId of <paramref name="spanId1" /> is different from the SpanId of <paramref name="spandId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.ToHexString">
      <summary>Returns a 16-character hexadecimal string that represents this span ID.</summary>
      <returns>The 16-character hexadecimal string representation of this span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.ToString">
      <summary>Returns a 16-character hexadecimal string that represents this span ID.</summary>
      <returns>The 16-character hexadecimal string representation of this span ID.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityTagsCollection">
      <summary>ActivityTagsCollection is a collection class used to store tracing tags.

This collection will be used with classes like <see cref="T:System.Diagnostics.ActivityEvent" /> and <see cref="T:System.Diagnostics.ActivityLink" />.

This collection behaves as follows:
- The collection items will be ordered according to how they are added.
- Don't allow duplication of items with the same key.
- When using the indexer to store an item in the collection:
    - If the item has a key that previously existed in the collection and the value is <see langword="null" />, the collection item matching the key will be removed from the collection.
    - If the item has a key that previously existed in the collection and the value is not <see langword="null" />, the new item value will replace the old value stored in the collection.
    - Otherwise, the item will be added to the collection.
- Add method will add a new item to the collection if an item doesn't already exist with the same key. Otherwise, it will throw an exception.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.#ctor">
      <summary>Create a new instance of the collection.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
      <summary>Create a new instance of the collection and store the input list items in the collection.</summary>
      <param name="list">Initial list to store in the collection.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Adds an item to the collection</summary>
      <param name="item">Key and value pair of the tag to add to the collection.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="item" /> already exists in the list.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="item" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Add(System.String,System.Object)">
      <summary>Adds a tag with the provided key and value to the collection. This collection doesn't allow adding two tags with the same key.</summary>
      <param name="key">The tag key.</param>
      <param name="value">The tag value.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Clear">
      <summary>Removes all items from the collection.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.</summary>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.ContainsKey(System.String)">
      <summary>Determines whether the collection contains an element with the specified key.</summary>
      <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</param>
      <returns>
        <see langword="true" /> if the collection contains tag with that key. <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
      <summary>Copies the elements of the collection to an array, starting at a particular array index.</summary>
      <param name="array">The array that is the destination of the elements copied from collection.</param>
      <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator for the <see langword="ActivityTagsCollection" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Removes the first occurrence of a specific item from the collection.</summary>
      <param name="item">The tag key value pair to remove.</param>
      <returns>
        <see langword="true" /> if item was successfully removed from the collection; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if item is not found in the original collection.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Remove(System.String)">
      <summary>Removes the tag with the specified key from the collection.</summary>
      <param name="key">The tag key.</param>
      <returns>
        <see langword="true" /> if the item existed and removed. <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String@System#Object}}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.TryGetValue(System.String,System.Object@)">
      <summary>Gets the value associated with the specified key.</summary>
      <param name="key">The tag key.</param>
      <param name="value">The tag value.</param>
      <returns>When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.Count">
      <summary>Gets the number of elements contained in the collection.</summary>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.IsReadOnly">
      <summary>Gets a value indicating whether the collection is read-only. This always returns <see langword="false" />.</summary>
      <returns>Always returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.Item(System.String)">
      <summary>Gets or sets a specified collection item.
        
        When setting a value to this indexer property, the following behavior is observed:
- If the key previously existed in the collection and the value is <see langword="null" />, the collection item matching the key will get removed from the collection.
- If the key previously existed in the collection and the value is not <see langword="null" />, the value will replace the old value stored in the collection.
- Otherwise, a new item will get added to the collection.</summary>
      <param name="key">The key of the value to get or set.</param>
      <returns>The object mapped to the key.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.Keys">
      <summary>Get the list of the keys of all stored tags.</summary>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.Values">
      <summary>Get the list of the values of all stored tags.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivityTagsCollection.Enumerator">
      <summary>Enumerates the elements of an <see langword="ActivityTagsCollection" />.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Enumerator.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the collection.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTagsCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.Enumerator.Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="P:System.Diagnostics.ActivityTagsCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityTraceFlags">
      <summary>Specifies flags defined by the W3C standard that are associated with an activity.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityTraceFlags.None">
      <summary>The activity has not been marked.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityTraceFlags.Recorded">
      <summary>The activity (or more likely its parents) has been marked as useful to record.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivityTraceId">
      <summary>Represents a <see cref="P:System.Diagnostics.Activity.TraceId" /> whose format is based on a W3C standard.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CopyTo(System.Span{System.Byte})">
      <summary>Copies the 16 bytes of the current <see cref="T:System.Diagnostics.ActivityTraceId" /> to a specified span.</summary>
      <param name="destination">The span to which the 16 bytes of the trace ID are to be copied.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateFromBytes(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivityTraceId" /> value from a read-only span of 16 bytes.</summary>
      <param name="idData">A read-only span of 16 bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain eight bytes.</exception>
      <returns>The new trace ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateFromString(System.ReadOnlySpan{System.Char})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivityTraceId" /> value from a read-only span of 32 hexadecimal characters.</summary>
      <param name="idData">A span that contains 32 hexadecimal characters.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain 16 hexadecimal characters.

-or-

The characters in <paramref name="idData" /> are not all lower-case hexadecimal characters or all zeros.</exception>
      <returns>The new trace ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateFromUtf8String(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivityTraceId" /> value from a read-only span of UTF8-encoded bytes.</summary>
      <param name="idData">A read-only span of UTF8-encoded bytes.</param>
      <returns>The new trace ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateRandom">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> based on a random number (that is very likely to be unique).</summary>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.Equals(System.Diagnostics.ActivityTraceId)">
      <summary>Determines whether the current instance and a specified <see cref="T:System.Diagnostics.ActivityTraceId" /> are equal.</summary>
      <param name="traceId">The instance to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="traceId" /> has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.Equals(System.Object)">
      <summary>Determines whether this instance and a specified object, which must also be an <see cref="T:System.Diagnostics.ActivityTraceId" /> instance, have the same value.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see cref="T:System.Diagnostics.ActivityTraceId" /> and has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.GetHashCode">
      <summary>Returns the hash code of the TraceId.</summary>
      <returns>The hash code of the TraceId.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.op_Equality(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivityTraceId)">
      <summary>Determines whether two specified <see cref="T:System.Diagnostics.ActivityTraceId" /> instances have the same value.</summary>
      <param name="traceId1">The first instance to compare.</param>
      <param name="traceId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the TraceId of <paramref name="traceId1" /> is the same as the TraceId of <paramref name="traceId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.op_Inequality(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivityTraceId)">
      <summary>Determines whether two specified <see cref="T:System.Diagnostics.ActivityTraceId" /> instances have the same value.</summary>
      <param name="traceId1">The first instance to compare.</param>
      <param name="traceId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the TraceId of <paramref name="traceId1" /> is different from the TraceId of <paramref name="traceId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.ToHexString">
      <summary>Returns a 16-character hexadecimal string that represents this span ID.</summary>
      <returns>The 32-character hexadecimal string representation of this trace ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.ToString">
      <summary>Returns a 32-character hexadecimal string that represents this trace ID.</summary>
      <returns>The 32-character hexadecimal string representation of this trace ID.</returns>
    </member>
    <member name="T:System.Diagnostics.DiagnosticListener">
      <summary>Provides an implementation of the abstract <see cref="T:System.Diagnostics.DiagnosticSource" /> class that represents a named place to which a source sends its information (events).</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.#ctor(System.String)">
      <summary>Creates a new <see cref="T:System.Diagnostics.DiagnosticListener" />.</summary>
      <param name="name">The name of this <see cref="T:System.Diagnostics.DiagnosticListener" />.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Dispose">
      <summary>Disposes the NotificationListeners.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled">
      <summary>Determines whether there are any registered subscribers.</summary>
      <returns>
        <see langword="true" /> if there are any registered subscribers, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled(System.String)">
      <summary>Checks whether the <see cref="T:System.Diagnostics.DiagnosticListener" /> is enabled.</summary>
      <param name="name">The name of the event to check.</param>
      <returns>
        <see langword="true" /> if notifications are enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled(System.String,System.Object,System.Object)">
      <summary>Checks if any subscriber to the diagnostic events is interested in receiving events with this name. Subscribers indicate their interest using a delegate provided in <see cref="Overload:System.Diagnostics.DiagnosticListener.Subscribe" />.</summary>
      <param name="name">The name of the event to check.</param>
      <param name="arg1">The object that represents a context.</param>
      <param name="arg2">The object that represents a context.</param>
      <returns>
        <see langword="true" /> if it is enabled, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.OnActivityExport(System.Diagnostics.Activity,System.Object)">
      <summary>Invokes the OnActivityExport method of all the subscribers.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">An object that represents the outgoing request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.OnActivityImport(System.Diagnostics.Activity,System.Object)">
      <summary>Invokes the OnActivityImport method of all the subscribers.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">An object that represents the incoming request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
      <summary>Adds a subscriber.</summary>
      <param name="observer">A subscriber.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean})">
      <summary>Adds a subscriber, and optionally filters events based on their name and up to two context objects.</summary>
      <param name="observer">A subscriber.</param>
      <param name="isEnabled">A delegate that filters events based on their name and up to two context objects (which can be <see langword="null" />), or <see langword="null" /> to if an event filter is not desirable.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean},System.Action{System.Diagnostics.Activity,System.Object},System.Action{System.Diagnostics.Activity,System.Object})">
      <summary>Adds a subscriber, optionally filters events based on their name and up to two context objects, and specifies methods to call when providers import or export activites from outside the process.</summary>
      <param name="observer">A subscriber.</param>
      <param name="isEnabled">A delegate that filters events based on their name and up to two context objects (which can be <see langword="null" />), or <see langword="null" /> if an event filter is not desirable.</param>
      <param name="onActivityImport">An action delegate that receives the activity affected by an external event and an object that represents the incoming request.</param>
      <param name="onActivityExport">An action delegate that receives the activity affected by an external event and an object that represents the outgoing request.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Predicate{System.String})">
      <summary>Adds a subscriber, and optionally filters events based on their name.</summary>
      <param name="observer">A subscriber.</param>
      <param name="isEnabled">A delegate that filters events based on their name (<see cref="T:System.String" />). The delegate should return <see langword="true" /> if the event is enabled.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.ToString">
      <summary>Returns a string with the name of this DiagnosticListener.</summary>
      <returns>The name of this DiagnosticListener.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Write(System.String,System.Object)">
      <summary>Logs a notification.</summary>
      <param name="name">The name of the event to log.</param>
      <param name="value">An object that represents the payload for the event.</param>
    </member>
    <member name="P:System.Diagnostics.DiagnosticListener.AllListeners">
      <summary>Gets the collection of listeners for this <see cref="T:System.Diagnostics.DiagnosticListener" />.</summary>
    </member>
    <member name="P:System.Diagnostics.DiagnosticListener.Name">
      <summary>Gets the name of this <see cref="T:System.Diagnostics.DiagnosticListener" />.</summary>
      <returns>The name of the <see cref="T:System.Diagnostics.DiagnosticListener" />.</returns>
    </member>
    <member name="T:System.Diagnostics.DiagnosticSource">
      <summary>An abstract class that allows code to be instrumented for production-time logging of rich data payloads for consumption within the process that was instrumented.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Diagnostics.DiagnosticSource" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String)">
      <summary>Verifies if the notification event is enabled.</summary>
      <param name="name">The name of the event being written.</param>
      <returns>
        <see langword="true" /> if the notification event is enabled, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String,System.Object,System.Object)">
      <summary>Verifies it the notification event is enabled.</summary>
      <param name="name">The name of the event being written.</param>
      <param name="arg1">An object that represents the additional context for IsEnabled. Consumers should expect to receive <see langword="null" /> which may indicate that producer called pure IsEnabled(string) to check if consumer wants to get notifications for such events at all. Based on that, producer may call IsEnabled(string, object, object) again with non-<see langword="null" /> context.</param>
      <param name="arg2">Optional. An object that represents the additional context for IsEnabled. <see langword="null" /> by default. Consumers should expect to receive <see langword="null" /> which may indicate that producer called pure IsEnabled(string) or producer passed all necessary context in <paramref name="arg1" />.</param>
      <returns>
        <see langword="true" /> if the notification event is enabled, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.OnActivityExport(System.Diagnostics.Activity,System.Object)">
      <summary>Transfers state from an activity to some event or operation, such as an outgoing HTTP request, that will occur outside the process.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">An object that represents the outgoing request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.OnActivityImport(System.Diagnostics.Activity,System.Object)">
      <summary>Transfers state to an activity from some event or operation, such as an incoming request, that occurred outside the process.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">A payload that represents the incoming request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.StartActivity(System.Diagnostics.Activity,System.Object)">
      <summary>Starts an <see cref="T:System.Diagnostics.Activity" /> and writes a start event.</summary>
      <param name="activity">The <see cref="T:System.Diagnostics.Activity" /> to be started.</param>
      <param name="args">An object that represent the value being passed as a payload for the event.</param>
      <returns>The started activity for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.StopActivity(System.Diagnostics.Activity,System.Object)">
      <summary>Stops the given <see cref="T:System.Diagnostics.Activity" />, maintains the global <see cref="P:System.Diagnostics.Activity.Current" /> activity, and notifies consumers that the <see cref="T:System.Diagnostics.Activity" /> was stopped.</summary>
      <param name="activity">The activity to be stopped.</param>
      <param name="args">An object that represents the value passed as a payload for the event.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.Write(System.String,System.Object)">
      <summary>Provides a generic way of logging complex payloads.</summary>
      <param name="name">The name of the event being written.</param>
      <param name="value">An object that represents the value being passed as a payload for the event. This is often an anonymous type which contains several sub-values.</param>
    </member>
    <member name="T:System.Diagnostics.SampleActivity`1">
      <summary>A delegate that defines the signature of the <see cref="T:System.Diagnostics.ActivityListener" /> callbacks used in the sampling process.</summary>
      <param name="options">The Activity creation options used by <see cref="T:System.Diagnostics.ActivityListener" /> callbacks to decide creating the Activity object or not.</param>
      <typeparam name="T">The type of the requested parent to create the Activity object with. Should be either a string or an <see cref="T:System.Diagnostics.ActivityContext" /> instance.</typeparam>
      <returns>An object containing the sampling results, which indicate the amount of data to collect for the related <see cref="T:System.Diagnostics.Activity" />.</returns>
    </member>
  </members>
</doc>