﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Models.EMS
{
    public class EQPOUTPUTBYHOUR2
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CREATEDATE { get; set; }
        /// <summary>
        /// 工序
        /// </summary>
        public string SPEC { get; set; }
        /// <summary>
        /// 工作日期
        /// </summary>
        public string NOWDAY { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT { get; set; }
        /// <summary>
        /// 当前小时产出数量
        /// </summary>
        public string OUTPUT { get; set; }
        /// <summary>
        /// 时间格式1
        /// </summary>
        public string HOURS1 { get; set; }
        /// <summary>
        /// 时间格式2
        /// </summary>
        public string HOURS2 { get; set; }
        /// <summary>
        /// 班次
        /// </summary>
        public string SHIFT { get; set; }
    }                           
}
