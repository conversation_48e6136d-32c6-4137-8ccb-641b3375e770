﻿using DataAccessor;
using DataEntities.Astroenergy;
using Entities.Models.WMSTOMES;
using Infrastructure.Configurable;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Newtonsoft.Json;
using OutsideService.Comm;
using OutsideService.WMS;
using OutsideService.WMS.Models;
using Services;
using Services.AstroenergyRepository;
using Services.AstroenergyService;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Security.Cryptography;
using System.Text;
using System.Threading;

namespace ApiTestProject
{
    [TestClass]
    public class WMSServiceTest
    {
        /// <summary>
        /// 测试一下箱码标签同步功能的WMS接口
        /// </summary>
        [TestMethod]
        [DataRow("04059A0123053100020")]
        public void Test_WMSService_SyncPackNoToWMS(string boxCode)
        {
            //获取入库信息
            var dto = new WMSToMESRepository().GetPackageToWMSDtoByBoxCode(boxCode).Result;
            Assert.IsNotNull(dto);
            //推送数据
            var resultModel = new WMSService().SyncPackNoToWMS(new List<SyncPackageToWMSDto> { dto });
            Assert.IsNotNull(resultModel);
            Assert.AreEqual("1", resultModel.SYNC_STATUS);
            Thread.Sleep(200);
        }

        /// <summary>
        /// 测试一下箱码标签同步功能的WMS接口
        /// </summary>
        [TestMethod]
        [DataRow("04059A0123053100020")]
        public void Test_WMSService_SyncPackNoToWMS_with_error_url(string boxCode)
        {
            //获取入库信息
            var dto = new WMSToMESRepository().GetPackageToWMSDtoByBoxCode(boxCode).Result;
            Assert.IsNotNull(dto);

            //错误的url
            ConfigReader.ConfigEntity.WMSSetting.WMSAPIURL = "http://1.1.1.1";
            //推送数据
            var resultModel = new WMSService().SyncPackNoToWMS(new List<SyncPackageToWMSDto> { dto });
            Assert.IsNotNull(resultModel);
            Assert.AreEqual("0", resultModel.SYNC_STATUS);
            Thread.Sleep(200);
        }

        /// <summary>
        /// 测试一下箱码标签同步WMS功能，当箱码不存在
        /// </summary>
        [TestMethod]
        [DataRow("123")]//不存在的
        public void Test_WMSToMESService_SyncPackNoToWMS_when_order_code_is_not_exist(string boxCode)
        {
            //var wmsServMock = new Mock<WMSService>();
            //wmsServMock.Setup(moq => moq.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>())).Returns(() => null);
            //WMSToMESService s = new WMSToMESService(new WMSToMESRepository(), wmsServMock.Object);
            WMSToMESService s = new WMSToMESService();
            ApiResponse rs = s.SyncPackNoToWMSByBoxCode(boxCode).Result;
            //wmsServMock.Verify(x => x.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>()), Times.Never());
            Assert.AreEqual("未找到待同步的入库信息", rs.Msg);
        }

        /// <summary>
        /// 测试一下箱码标签同步WMS功能，当箱码已推送成功过
        /// </summary>
        [TestMethod]
        [DataRow("11980A23061800001")]//已推送成功过的
        public void Test_WMSToMESService_SyncPackNoToWMS_when_ordercode_is_already_pushed(string boxCode)
        {
            //先往数据库中增加推送成功的状态，做虚假数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var statusEntity = new JA_SYNC_PACKAGE_TO_WMS_STATUS
                {
                    BOX_CODE = boxCode,
                    ORDER_CODE = "1",
                    SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    SYNC_STATUS = "1"
                };
                db.Storageable(statusEntity).ExecuteCommand();
            }
            //var wmsServMock = new Mock<WMSService>();
            //wmsServMock.Setup(moq => moq.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>())).Returns(() => null);
            //WMSToMESService s = new WMSToMESService(new WMSToMESRepository(), wmsServMock.Object);
            WMSToMESService s = new WMSToMESService();
            ApiResponse rs = s.SyncPackNoToWMSByBoxCode(boxCode).Result;
            //wmsServMock.Verify(x => x.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>()), Times.Never());
            Assert.AreEqual("未找到待同步的入库信息", rs.Msg);
        }


        /// <summary>
        /// 测试一下箱码标签同步WMS功能，当箱码需要被推送
        /// </summary>
        [TestMethod]
        [DataRow("11980A23061800001")]//待推送的,且能推送成功的
        [DataRow("11980A23061800002")]//待推送的,但会推送失败的
        public void Test_WMSToMESService_SyncPackNoToWMS_when_order_code_is_ready_to_push(string boxCode)
        {
            //如果已经存在推送成功状态记录，则删除
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                //根据主键删除
                db.Deleteable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().In(boxCode).ExecuteCommand();
            }
            var syncTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            //var wmsServMock = new Mock<WMSService>();
            //wmsServMock.Setup(moq => moq.SyncPackNoToWMS(It.Is<SyncPackageToWMSDto>(t => t.BOX_CODE == "11980A23061800001"))).Returns(() => new JA_SYNC_PACKAGE_TO_WMS_LOG
            //{
            //    URL = "/dec/Inbound/Api/SaveBoxCode/",
            //    SYNC_TIME = syncTime,
            //    REQUEST_JSON = "requestJson",
            //    RESULT_JSON = JsonConvert.SerializeObject(new ResultPackNoModel { errorCode = "1", Message = "成功" }),
            //    SYNC_STATUS = "1"
            //});
            //wmsServMock.Setup(moq => moq.SyncPackNoToWMS(It.Is<SyncPackageToWMSDto>(t => t.BOX_CODE == "11980A23061800002"))).Returns(() => new JA_SYNC_PACKAGE_TO_WMS_LOG
            //{
            //    URL = "/dec/Inbound/Api/SaveBoxCode/",
            //    SYNC_TIME = syncTime,
            //    REQUEST_JSON = "requestJson",
            //    RESULT_JSON = JsonConvert.SerializeObject(new ResultPackNoModel { errorCode = "0", Message = "失败" }),
            //    SYNC_STATUS = "0"
            //});

            //WMSToMESService s = new WMSToMESService(new WMSToMESRepository(), wmsServMock.Object);
            WMSToMESService s = new WMSToMESService();
            ApiResponse rs = s.SyncPackNoToWMSByBoxCode(boxCode).Result;
            //wmsServMock.Verify(x => x.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>()), Times.Once());
            //查询数据库
            //1.查询推送状态
            //2.查询推送日志
            //3.删除推送状态表
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var statusList = db.Queryable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(it => string.Equals(it.BOX_CODE, boxCode) && SqlFunc.GreaterThanOrEqual(it.SYNC_TIME, syncTime)).ToList();
                Assert.AreEqual(1, statusList?.Count);
                Assert.IsTrue(statusList.First().SYNC_STATUS=="1"|| statusList.First().SYNC_STATUS=="0");
                Assert.IsTrue(!String.IsNullOrEmpty(statusList.First().RESPONSE_MSG));

                db.Deleteable<JA_SYNC_PACKAGE_TO_WMS_STATUS>(statusList).ExecuteCommand();
            }
            Thread.Sleep(200);
        }

        /// <summary>
        /// 测试一下箱码标签同步WMS功能，按入库单进行同步
        /// </summary>
        [TestMethod]
        [DataRow("C042353100019")]//待推送的入库单
        public void Test_WMSToMESService_SyncPackNoToWMSByOrderCode(string orderCode)
        {
            //如果已经存在推送成功状态记录，则删除
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                //根据入库单删除
                db.Deleteable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(it => it.ORDER_CODE == orderCode).ExecuteCommand();
            }
            //var wmsServMock = new Mock<WMSService>();
            var syncTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            //wmsServMock.Setup(moq => moq.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>())).Returns(() => new JA_SYNC_PACKAGE_TO_WMS_LOG
            //{
            //    URL = "/dec/Inbound/Api/SaveBoxCode/",
            //    SYNC_TIME = syncTime,
            //    REQUEST_JSON = "requestJson",
            //    RESULT_JSON = JsonConvert.SerializeObject(new ResultPackNoModel { errorCode = "1", Message = "成功" }),
            //    SYNC_STATUS = "1"
            //});
            //WMSToMESService s = new WMSToMESService(new WMSToMESRepository(), wmsServMock.Object);
            WMSToMESService s = new WMSToMESService();
            ApiResponse rs = s.SyncPackNoToWMSByOrderCode(orderCode).Result;
            //wmsServMock.Verify(x => x.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>()), Times.AtLeast(2));
            //查询数据库
            //1.查询推送状态
            //2.查询推送日志
            //3.删除推送状态表
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var statusList = db.Queryable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(it => string.Equals(it.ORDER_CODE, orderCode) && SqlFunc.GreaterThanOrEqual(it.SYNC_TIME, syncTime)).ToList();
                Assert.IsTrue(statusList?.Count >= 2);
                Assert.AreEqual("1", statusList.First().SYNC_STATUS);

                db.Deleteable<JA_SYNC_PACKAGE_TO_WMS_STATUS>(statusList).ExecuteCommand();
            }

            Thread.Sleep(200);
        }

        /// <summary>
        /// 测试一下箱码标签同步WMS功能，失败重推
        /// </summary>
        [TestMethod]
        public void Test_WMSToMESService_ReSyncPushPackNoToWMS()
        {
            //增加一些失败的记录
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                List<JA_SYNC_PACKAGE_TO_WMS_STATUS> list = new List<JA_SYNC_PACKAGE_TO_WMS_STATUS>()
                {
                    new JA_SYNC_PACKAGE_TO_WMS_STATUS
                    {
                        BOX_CODE = "04059A0123053100038",
                        ORDER_CODE = "C042353100019",
                        SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        SYNC_STATUS = "0"
                    },
                    new JA_SYNC_PACKAGE_TO_WMS_STATUS
                    {
                        BOX_CODE = "04059A0123053100036",
                        ORDER_CODE = "C042353100019",
                        SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        SYNC_STATUS = "0"
                    },
                    new JA_SYNC_PACKAGE_TO_WMS_STATUS
                    {
                        BOX_CODE = "04059A0123053100037",
                        ORDER_CODE = "C042353100019",
                        SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        SYNC_STATUS = "0"
                    }
                };

                db.Storageable<JA_SYNC_PACKAGE_TO_WMS_STATUS>(list).ExecuteCommand();
            }
            //var wmsServMock = new Mock<WMSService>();
            var syncTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            //wmsServMock.Setup(moq => moq.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>())).Returns(() => new JA_SYNC_PACKAGE_TO_WMS_LOG
            //{
            //    URL = "/dec/Inbound/Api/SaveBoxCode/",
            //    SYNC_TIME = syncTime,
            //    REQUEST_JSON = "requestJson",
            //    RESULT_JSON = JsonConvert.SerializeObject(new ResultPackNoModel { errorCode = "1", Message = "成功" }),
            //    SYNC_STATUS = "1"
            //});
            //WMSToMESService s = new WMSToMESService(new WMSToMESRepository(), wmsServMock.Object);
            WMSToMESService s = new WMSToMESService();
            ApiResponse rs = s.ReSyncOrForcePushPackNoToWMS(null, false).Result;
            //wmsServMock.Verify(moq => moq.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>()), Times.AtLeast(3));
            Assert.IsTrue(rs.Success);

            //删除这些手动添加的失败的记录
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var statusList = db.Queryable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(it => string.Equals(it.ORDER_CODE, "C042353100019") && SqlFunc.GreaterThanOrEqual(it.SYNC_TIME, syncTime)).ToList();
                Assert.IsTrue(statusList?.Count == 3);
                Assert.AreEqual("1", statusList.First().SYNC_STATUS);

                db.Deleteable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(t => t.ORDER_CODE == "C042353100019").ExecuteCommand();
            }
            Thread.Sleep(200);
        }


        /// <summary>
        /// 测试一下箱码标签同步WMS功能，强制重推
        /// </summary>
        [TestMethod]
        [DataRow("04174A01023090700011")]//已推送过的
        public void Test_WMSToMESService_ForcePushPackNoToWMS(string boxCode)
        {
            //var wmsServMock = new Mock<WMSService>();
            //WMSToMESService s = new WMSToMESService(new WMSToMESRepository(), wmsServMock.Object);
            WMSToMESService s = new WMSToMESService();
            ApiResponse rs = s.ReSyncOrForcePushPackNoToWMS(boxCode, false).Result;
            //wmsServMock.Verify(x => x.SyncPackNoToWMS(It.IsAny<SyncPackageToWMSDto>()), Times.Once());
            Assert.IsTrue(rs.Success);

            //删除这些手动添加的失败的记录
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var statusList = db.Queryable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(it => string.Equals(it.BOX_CODE, boxCode)).ToList();
                Assert.AreEqual(1, statusList?.Count);
                Assert.AreEqual("1", statusList.First().SYNC_STATUS);
            }
            Thread.Sleep(200);
        }


        [TestMethod]
        public void Test_WMSToMESService_SendMailWhenPushPackNoToWMSFail()
        {
            //增加一些失败的记录
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                List<JA_SYNC_PACKAGE_TO_WMS_STATUS> list = new List<JA_SYNC_PACKAGE_TO_WMS_STATUS>()
                {
                    new JA_SYNC_PACKAGE_TO_WMS_STATUS
                    {
                        BOX_CODE = "04059A0123053100038",
                        ORDER_CODE = "C042353100019",
                        SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        SYNC_STATUS = "0",
                        RETRY_TIMES = 5,
                        RESPONSE_MSG="报错了，重试一下"
                    },
                    new JA_SYNC_PACKAGE_TO_WMS_STATUS
                    {
                        BOX_CODE = "04059A0123053100036",
                        ORDER_CODE = "C042353100019",
                        SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        SYNC_STATUS = "0",
                        RETRY_TIMES = 5,
                        RESPONSE_MSG="报错了，重试一下"
                    },
                    new JA_SYNC_PACKAGE_TO_WMS_STATUS
                    {
                        BOX_CODE = "04059A0123053100037",
                        ORDER_CODE = "C042353100019",
                        SYNC_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        SYNC_STATUS = "0",
                        RETRY_TIMES = 5,
                        RESPONSE_MSG="报错了，重试一下"
                    }
                };

                db.Storageable<JA_SYNC_PACKAGE_TO_WMS_STATUS>(list).ExecuteCommand();
            }
            new WMSToMESService().SendMailWhenPushPackNoToWMSFail().GetAwaiter().GetResult();

            //删除这些手动添加的失败的记录
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                db.Deleteable<JA_SYNC_PACKAGE_TO_WMS_STATUS>().Where(t => t.ORDER_CODE == "C042353100019").ExecuteCommand();
            }
        }

        /// <summary>
        /// 测试WMS回传箱码信息
        /// </summary>
        [TestMethod]
        public void Test_WMSToMESService_ReturnBoxNo()
        {
            //删除已保存的测试数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                db.DeleteNav<JAWMSRETURNPACKAGERECORD>(it => it.OPERATOR == "dd")//删除主表 Student(id=1)
      .Include(z1 => z1.DETAILS)
      .ExecuteCommand();

                db.Deleteable<JAPACKAGEWMSSTATUS>().Where(it => new[] { "B4444", "B5555" }.Contains(it.BOX_CODE)).ExecuteCommand();
            }

            new WMSToMESService().ReturnBoxNo(new JAWMSRETURNPACKAGERECORD
            {
                COMPANY_CODE = "1",
                OPERATOR = "dd",
                OPERATORTIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                REMARK = "测试",
                DETAILS = new List<JAWMSRETURNPACKAGERECORDDETAIL>
                   {
                        new JAWMSRETURNPACKAGERECORDDETAIL
                        {
                             BOX_CODE = "B4444",
                             SAPNO="C1111",
                             STATUSCODE="IN"
                        },
                        new JAWMSRETURNPACKAGERECORDDETAIL
                        {
                             BOX_CODE = "B5555",
                             SAPNO="C1111",
                             STATUSCODE="IN"
                        }
                   }
            }).GetAwaiter();

            //查询测试数据
            using (var db = new UnitOfWork().MIDRepository.GetSqlSugarClient())
            {
                var list = db.Queryable<JAWMSRETURNPACKAGERECORD>()
                            .Includes(z1 => z1.DETAILS)
                            .Where(it => it.OPERATOR == "dd")//删除主表 Student(id=1)
                            .ToList();
                Assert.AreEqual(1, list.Count);
                Assert.AreEqual(2, list[0].DETAILS.Count);
                var list2 = db.Queryable<JAPACKAGEWMSSTATUS>().Where(it => new[] { "B4444", "B5555" }.Contains(it.BOX_CODE)).ToList();
                Assert.AreEqual(2, list2.Count);
            }
        }

        /// <summary>
        /// 测试Token头的生成
        /// </summary>
        [TestMethod]
        public void Test_GenerateWMSToken()
        {
            /// <summary>
            /// 获取要加密的string字符串字节数组
            /// </summary>
            /// <param name="strKey">待加密字符串</param>
            /// <returns>加密数组</returns>
            byte[] GetKeyByteArray(string strKey)
            {
                UTF8Encoding Asc = new UTF8Encoding();
                int tmpStrLen = strKey.Length;
                byte[] resByte = new byte[tmpStrLen - 1];
                resByte = Asc.GetBytes(strKey);
                return resByte;
            }

            //SHA256("checkcode"+checkcode+"signcode"+signcode+"timestamp"+yyyyMMddHH24miss)
            //加密前字符串：checkcodeKIL5NCWFSTXZWM83signcode7W83NV6LUVYO6A6Ytimestamp2024010112340000
            //加密后得到sign: 8094ae9d88f80cb16fb8c558fa9c369e19244aca3ac1b3899054d4c29f7d341d
            byte[] tmpByte;
            SHA256 sha256 = new SHA256Managed();
            string strIn = $"checkcodeKIL5NCWFSTXZWM83signcode7W83NV6LUVYO6A6Ytimestamp2024010112340000";
            tmpByte = sha256.ComputeHash(GetKeyByteArray(strIn));

            StringBuilder rst = new StringBuilder();
            for (int i = 0; i < tmpByte.Length; i++)
            {
                rst.Append(tmpByte[i].ToString("x2"));
            }
            sha256.Clear();

            Assert.AreEqual("8094ae9d88f80cb16fb8c558fa9c369e19244aca3ac1b3899054d4c29f7d341d", rst.ToString());
        }
    }
}
