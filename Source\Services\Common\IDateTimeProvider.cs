﻿using System;

namespace Services.Common
{
    /// <summary>
    /// 时间提供者接口
    /// </summary>
    public interface IDateTimeProvider
    {
        DateTime Now { get; }
    }

    /// <summary>
    /// 数据库时间提供者实现
    /// </summary>
    public class DatabaseDateTimeProvider : IDateTimeProvider
    {
        public DateTime Now
        {
            get
            {
                using (var db = new DataAccessor.BaseRepository(DataAccessor.DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    return db.Ado.SqlQuerySingle<DateTime>("SELECT SYSDATE FROM DUAL");
                }
            }
        }
    }

    /// <summary>
    /// 系统时间提供者实现(用于默认情况)
    /// </summary>
    public class SystemDateTimeProvider : IDateTimeProvider
    {
        public DateTime Now => DateTime.Now;
    }
}