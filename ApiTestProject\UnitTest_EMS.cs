﻿using Entities.Models.EMS;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Services.AstroenergyService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [TestClass]
    public class UnitTest_EMS
    {
        [TestMethod]
        public void get_next_30_minite()
        {
            DateTime? startDateTime = DateTime.Parse("2023-08-30 09:00");
            DateTime needPushDatetime = DateTime.Now;
            if (startDateTime.Value.Minute == 30)
            {
                needPushDatetime = startDateTime.Value.AddHours(1);
            }
            else if (startDateTime.Value.Minute > 30)
            {
                // startDateTime的下一个小时的30分钟
                needPushDatetime = startDateTime.Value.AddHours(1).AddMinutes(30 - startDateTime.Value.Minute);
            }
            else if (startDateTime.Value.Minute < 30)
            {
                needPushDatetime = startDateTime.Value.AddMinutes(30 - startDateTime.Value.Minute);
            }

            Console.WriteLine(needPushDatetime.ToString());
        }

        [TestMethod]
        public void send_output_data_to_ems_success()
        {
            var emsServiceMock = new Mock<EMSService>();
            emsServiceMock.Setup(moq => moq.HttpDataToEMS(It.IsAny<List<EQPOUTPUTBYHOUR>>(), It.IsAny<List<JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE>>())).Returns(() => new PushOutputToEMSResponse
            {
                code = 0,
                msg = "",
                data = null
            });
            var res = emsServiceMock.Object.PushOutputDataToEMS().GetAwaiter().GetResult();

        }

        [TestMethod]
        public void send_output_data_to_ems_fail()
        {
            var emsServiceMock = new Mock<EMSService>();
            emsServiceMock.Setup(moq => moq.HttpDataToEMS(It.IsAny<List<EQPOUTPUTBYHOUR>>(), It.IsAny<List<JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE>>())).Returns((List<EQPOUTPUTBYHOUR> inputs, List<JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE> types) => new PushOutputToEMSResponse
            {
                code = -1,
                msg = "插入数据库失败",
                data = inputs.Select(x => new PushOutputToEMSDTO
                {
                    date = DateTime.Parse($"{x.NOWDAY} {x.HOURS1}"),
                    nodeName = x.EQUIPMENT,
                    value = double.Parse(x.OUTPUT),
                    productType = types.Find(p => p.SPEC == x.SPEC)?.ID ?? 0
                }).ToList()
            });
            var res = emsServiceMock.Object.PushOutputDataToEMS().GetAwaiter().GetResult();
        }

        [TestMethod]
        public void send_output_data_to_ems_retry()
        {
            var emsServiceMock = new Mock<EMSService>();
            emsServiceMock.Setup(moq => moq.HttpDataToEMS(It.IsAny<List<EQPOUTPUTBYHOUR>>(), It.IsAny<List<JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE>>())).Returns((List<EQPOUTPUTBYHOUR> inputs, List<JA_PUSH_OUTPUT_TO_EMS_PRODUCTTYPE> types) => new PushOutputToEMSResponse
            {
                code = -1,
                msg = "插入数据库失败2",
                data = inputs.Select(x => new PushOutputToEMSDTO
                {
                    date = DateTime.Parse($"{x.NOWDAY} {x.HOURS1}"),
                    nodeName = x.EQUIPMENT,
                    value = double.Parse(x.OUTPUT),
                    productType = types.Find(p => p.SPEC == x.SPEC)?.ID ?? 0
                }).ToList()
            });
            var res = emsServiceMock.Object.RePushOutputDataToEMS(null,true).GetAwaiter().GetResult();
        }

        [TestMethod]
        public void send_output_data_to_ems_without_mock()
        {
            EMSService emsService = new EMSService();
            var res = emsService.PushOutputDataToEMS().GetAwaiter().GetResult();
        }

        [TestMethod]
        public void send_output_data_to_ems_without_mock_10times()
        {
            for (int i = 0; i < 50; i++)
            {
                send_output_data_to_ems_without_mock();
            }
        }
    }
}
