﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using OutsideService.WMS;
using Services.AstroenergyRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [TestClass]
    public class WMSToMesReponsitoryTest
    {
        /// <summary>
        /// 测试根据入库单获取箱码
        /// </summary>
        [TestMethod]
        public void Test_WMSToMesReponsitory_GetStoreInPackageNoByOrderCode()
        {
            //获取入库信息
            var dto = new WMSToMESRepository().GetStoreInPackageNoByOrderCode("C042353100020").Result;
            
            Assert.IsNotNull(dto);
            Assert.IsTrue(dto.Count>=1);
        }
    }
}
