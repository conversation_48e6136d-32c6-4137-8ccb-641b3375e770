### 基于需求设计的产量计算逻辑流程图及说明：
```
graph TD
    A[开始] --> B0{是否为丝网工序?}
    B0 -->|是| B1[生成测试分选数据]
    B0 -->|否| B[处理CreateDate生成时间点]
    B1 --> B2[生成丝网数据<br>OutputQty=InputQTY<br>TotalOutputQty=TotalInputQty<br>TotalOutQty=TotalInQty]
    B1 & B2 --> B

    B --> C{Redis存在<br>Equipment+TimePoint数据?}
    C -->|是| D[从Redis获取历史产量]
    C -->|否| E[从EQPOUTPUTBYHOUR和JAPRINTQTYINFO<br>获取历史数据]
    E --> F{是否找到数据?}
    F -->|是| G[合并数据库数据]
    F -->|否| H[标记为无历史数据]

    D & G & H --> I{是否存在上一个<br>时间点数据?}
    I -->|是| J[获取上一时间点OutputQty]
    I -->|否| K[判断特殊时间点规则]
    K -->|8:30/20:30| L[取TotalOutputQty<br>作为上一时间点数据]
    K -->|其他时间点| M[标记为无历史数据]

    L & M & J --> N{当前时间点有产量数据?}
    N -->|是| O[比较传入OutputQty与历史值]
    N -->|否| P[用传入值作为新基准]

    O --> Q{OutputQty ><br>历史值?}
    Q -->|是| R[正常数据]
    Q -->|否| S[异常数据]

    R --> T[新产量 = 当前值 - 上一时间点值]
    S --> U[新产量 = 当前值 + 历史值]

    P --> V{当前值 ><br>上一时间点值?}
    V -->|是| W[正常数据]
    V -->|否| X[异常数据]

    W --> Y[新产量 = 当前值 - 上一时间点值]
    X --> Z[新产量 = 当前值]

    T & U & Y & Z --> AA[更新Redis和EQPOUTPUTBYHOUR]
    AA --> AB[结束]

    style A fill:#9f9,stroke:#333
    style AB fill:#f99,stroke:#333
    style K fill:#ff9,stroke:#333
    style Q fill:#f9f,stroke:#333
    style V fill:#f9f,stroke:#333
    style B0 fill:#ff9,stroke:#333
    style B1 fill:#ccf,stroke:#333
    style B2 fill:#ccf,stroke:#333
```

### 逻辑说明：

#### 丝网工序特殊处理：

1. 当设备规格为"丝网"时，系统会同时处理两种数据：
   - 测试分选数据：复制原始模型，将规格改为"测试分选"，修改SubDescription
   - 丝网数据：复制原始模型，将OutputQty设置为InputQTY，TotalOutputQty设置为TotalInputQty，TotalOutQty设置为TotalInQty

2. 这种特殊处理确保了丝网工序的产量计算与实际生产情况相符，因为丝网工序的产出量通常等于投入量

3. 两种数据都会被保存到EQPOUTPUTBYHOUR表中，分别对应不同的设备ID

#### 时间点生成规则：

1. 将CreateDate转换为整点后30分钟格式（如7:30-8:30→8:30）

2. 特殊处理8:30和20:30时段（对应早晚班次切换）

#### 数据查询优先级：

```
graph LR
A[数据源] --> B{Redis存在?}
B -->|是| C[使用Redis数据]
B -->|否| D[查询EQPOUTPUTBYHOUR]
D --> E{是否找到?}
E -->|是| F[结合JAPRINTQTYINFO]
E -->|否| G[视为无历史数据]
```

#### 异常数据处理机制：

`. 当检测到产量值异常时（当前值小于历史值），采用保守计算策略

2. 特殊时段使用TotalOutputQty作为基准值，确保跨班次产量计算的连续性

#### 更新策略矩阵：

条件类型|	计算方式|	更新操作
-------|---------|--------
正常数据|	差值计算（当前值 - 历史值）|	更新Redis和数据库的累计值
异常数据|	全量累加（当前值 + 历史值）|	更新Redis和数据库的异常标记字段
首次记录|	直接采用当前值|	创建新记录并设置基准值
特殊时段|	使用TotalOutputQty作为基准|	同时更新班次切换标记
丝网工序|	InputQTY作为OutputQty|	同时生成测试分选和丝网两种数据

#### 边界条件处理：

1. 凌晨0:30时段需要特别处理日期变更

2. 设备首次投产时的初始化逻辑

3. 数据库与Redis数据不一致时的校验机制

### 需要补充的异常处理机制：

1. Redis连接失败时自动切换直连数据库

2. 时间点格式异常时的自动校正

3. 负产量值的过滤机制

4. 数据更新失败的自动重试策略

5. 丝网工序数据处理失败时的回退机制

### 丝网工序特殊处理的测试要点：

1. 验证丝网设备的OutputQty是否正确设置为InputQTY

2. 验证测试分选设备是否正确创建，并保持原始OutputQty值

3. 验证两种设备的小时产量计算逻辑是否正确

4. 验证异常情况（如产量清零）的处理是否正确

5. 验证不同时间点的数据上报，包括首次上报、数据增长、跨时间段等场景