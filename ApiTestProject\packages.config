﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Castle.Core" version="5.1.1" targetFramework="net472" />
  <package id="Log2RabbitMQ" version="1.1.1" targetFramework="net472" />
  <package id="log4net" version="1.2.10" targetFramework="net472" />
  <package id="Moq" version="4.20.70" targetFramework="net472" />
  <package id="MSTest.TestAdapter" version="2.2.10" targetFramework="net472" />
  <package id="MSTest.TestFramework" version="2.2.10" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net472" />
  <package id="NewtonsoftJsonConverter" version="1.0.1" targetFramework="net472" />
  <package id="RabbitMQ.Client" version="6.5.0" targetFramework="net472" />
  <package id="RabbitMQ.Extension" version="1.1.6" targetFramework="net472" />
  <package id="SecurityEncDecryptExtensions" version="1.0.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net472" />
  <package id="System.Threading.Channels" version="7.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
</packages>