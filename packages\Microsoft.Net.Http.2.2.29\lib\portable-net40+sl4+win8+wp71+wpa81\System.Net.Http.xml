﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>Provides HTTP content based on a byte array.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.ByteArrayContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> parameter is null. </exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.ByteArrayContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="offset">The offset, in bytes, in the <paramref name="content" />  parameter used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="count">The number of bytes in the <paramref name="content" /> starting from the <paramref name="offset" /> parameter used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than zero.-or-The <paramref name="offset" /> parameter is greater than the length of content specified by the <paramref name="content" /> parameter.-or-The <paramref name="count " /> parameter is less than zero.-or-The <paramref name="count" /> parameter is greater than the length of content specified by the <paramref name="content" /> parameter - minus the <paramref name="offset" /> parameter.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStream">
      <summary>Creates an HTTP content stream for reading whose backing store is memory from the <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <returns>Returns <see cref="T:System.IO.Stream" />.The HTTP content stream.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStream(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize and write the byte array provided in the constructor to an HTTP content stream.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport(channel binding token, for example). This parameter may be null.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize and write the byte array provided in the constructor to an HTTP content stream as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />. The task object representing the asynchronous operation.</returns>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport, like channel binding token. This parameter may be null.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether a byte array has a valid length in bytes.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if <paramref name="length" /> is a valid length; otherwise, false.</returns>
      <param name="length">The length in bytes of the byte array.</param>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>A base type for HTTP handlers that delegate the processing of HTTP response messages to another handler, called the inner handler.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.DelegatingHandler" /> class with a specific inner handler.</summary>
      <param name="innerHandler">The inner handler which is responsible for processing the HTTP response messages.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.DelegatingHandler" />, and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources. </param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Send(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sends an HTTP request to the inner handler to send to the server synchronously.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />. The HTTP response message from the inner handler.</returns>
      <param name="request">The HTTP request message to send to the server.</param>
      <param name="cancellationToken">A cancellation token to cancel operation.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />. The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send to the server.</param>
      <param name="cancellationToken">A cancellation token to cancel operation.</param>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>A container for name/value tuples encoded using application/x-www-form-urlencoded MIME type.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.FormUrlEncodedContent" /> class with a specific collection of name/value pairs.</summary>
      <param name="nameValueCollection">A collection of name/value pairs.</param>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.CreateContentReadStream">
      <summary>Creates an HTTP content stream for reading whose backing store is memory from the <see cref="T:System.Net.Http.FormUrlEncodedContent" />.</summary>
      <returns>Returns <see cref="T:System.IO.Stream" />. The HTTP content stream.</returns>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.SerializeToStream(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize and write the provided name/value pairs in the <see cref="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})" /> constructor to an HTTP content stream.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (the channel binding token, for example). This parameter may be a null reference.</param>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize and write the provided name/value pairs in the <see cref="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})" /> constructor to an HTTP content stream as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />. The task object representing the asynchronous operation.</returns>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (the channel binding token, for example). This parameter may be a null reference.</param>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether the encoded name/value data  has a valid length in bytes.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if <paramref name="length" /> is a valid length; otherwise, false.</returns>
      <param name="length">The length in bytes of the encoded name/value data.</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>Provides a base class for sending HTTP requests and receiving HTTP responses from a resource identified by a URI. </summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpClient" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpClient" /> class with a specific handler.</summary>
      <param name="handler">The HTTP handler stack to use for sending requests. </param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>Gets or sets the base address of Uniform Resource Identifier (URI) of the Internet resource used when sending requests.</summary>
      <returns>Returns <see cref="T:System.Uri" />.The base address of Uniform Resource Identifier (URI) of the Internet resource used when sending requests.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>Cancel all pending requests on this instance.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>Gets the headers which should be sent with each request.</summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.The headers which should be sent with each request.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.Delete(System.String)">
      <summary>Send a DELETE request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Delete(System.Uri)">
      <summary>Send a DELETE request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>Send a DELETE request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>Send a DELETE request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpClient" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpClient" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.Get(System.String)">
      <summary>Send a GET request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Get(System.Uri)">
      <summary>Send a GET request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>Send a GET request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>Send a GET request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>Gets or sets the maximum number of bytes to buffer when reading the response content.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The maximum number of bytes to buffer when reading the response content.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The size specified is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">An operation has already been started on the current instance. </exception>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Post(System.String,System.Net.Http.HttpContent)">
      <summary>Send a POST request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Post(System.Uri,System.Net.Http.HttpContent)">
      <summary>Send a POST request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Send a POST request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Send a POST request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Put(System.String,System.Net.Http.HttpContent)">
      <summary>Send a PUT request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Put(System.Uri,System.Net.Http.HttpContent)">
      <summary>Send a PUT request to the specified Uri.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Send a PUT request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Send a PUT request to the specified Uri as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Send(System.Net.Http.HttpRequestMessage)">
      <summary>Send an HTTP request synchronously.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="request">The HTTP request message to send.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Send(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Send an HTTP request synchronously.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="completionOption">When the operation should complete (as soon as a response is available or after reading the whole response content).</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Send(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Send an HTTP request synchronously.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="completionOption">When the operation should complete (as soon as a response is available or after reading the whole response content).</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Send(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP request synchronously.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Send an HTTP request as an asynchronous operation. </summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="completionOption">When the operation should complete (as soon as a response is available or after reading the whole response content).</param>
      <exception cref="T:System.InvalidOperationException">This operation will not block. The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="completionOption">When the operation should complete (as soon as a response is available or after reading the whole response content).</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>Gets or sets the number of milliseconds to wait before the request times out.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.The number of milliseconds to wait before the request times out.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The timeout specified is less than or equal to zero and is not <see cref="F:System.Threading.Timeout.Infinite" />.</exception>
      <exception cref="T:System.InvalidOperationException">An operation has already been started on the current instance. </exception>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed.</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>A base class for HTTP handler implementations.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>Creates an instance of a <see cref="T:System.Net.Http.HttpClientHandler" /> class.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>Gets or sets a value that indicates whether the handler should follow redirection responses.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the if the handler should follow redirection responses; otherwise false. The default value is true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>Gets or sets the type of decompression method used by the handler for automatic decompression of the HTTP content response.</summary>
      <returns>Returns <see cref="T:System.Net.DecompressionMethods" />.The automatic decompression method used by the handler. The default value is <see cref="F:System.Net.DecompressionMethods.None" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>Gets or sets the cookie container used to store server cookies by the handler.</summary>
      <returns>Returns <see cref="T:System.Net.CookieContainer" />.The cookie container used to store server cookies by the handler.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>Gets or sets authentication information used by this handler.</summary>
      <returns>Returns <see cref="T:System.Net.ICredentials" />.The authentication credentials associated with the handler. The default is null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpClientHandler" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>Gets or sets the maximum number of redirects that the handler follows.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The maximum number of redirection responses that the handler follows. The default value is 50.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>Gets or sets the maximum request content buffer size used by the handler.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The maximum request content buffer size in bytes. The default value is 65,536 bytes.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>Gets or sets a value that indicates whether the handler sends an Authorization header with the request.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true for the handler to send an HTTP Authorization header with requests after authentication has taken place; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>Gets or sets proxy information used by the handler.</summary>
      <returns>Returns <see cref="T:System.Net.IWebProxy" />.The proxy information used by the handler. The default value is null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Send(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Creates an instance of  <see cref="T:System.Net.Http.HttpResponseMessage" /> based on the information provided in the <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message. </returns>
      <param name="request">The HTTP request message.</param>
      <param name="cancellationToken">A cancellation token to cancel the operation. </param>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Creates an instance of  <see cref="T:System.Net.Http.HttpResponseMessage" /> based on the information provided in the <see cref="T:System.Net.Http.HttpRequestMessage" /> as an operation that will not block.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message.</param>
      <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>Gets a value that indicates whether the handler supports automatic response content decompression.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the if the handler supports automatic response content decompression; otherwise false. The default value is true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>Gets a value that indicates whether the handler supports proxy settings.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the if the handler supports proxy settings; otherwise false. The default value is true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>Gets a value that indicates whether the handler supports configuration settings for the <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> and <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> properties.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the if the handler supports configuration settings for the <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> and <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> properties; otherwise false. The default value is true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>Gets or sets a value that indicates whether the handler uses the  <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> property  to store server cookies and uses these cookies when sending requests.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the if the handler supports uses the  <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> property  to store server cookies and uses these cookies when sending requests; otherwise false. The default value is true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>Gets or sets a value that controls whether default credentials are sent with requests by the handler.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the default credentials are used; otherwise false. The default value is false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>Gets or sets a value that indicates whether the handler uses a proxy for requests. </summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the handler should use a proxy for requests; otherwise false. The default value is true.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>Indicates if <see cref="T:System.Net.Http.HttpClient" /> operations should be considered completed either as soon as a response is available, or after reading the entire response message including the content. </summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>The operation should complete after reading the entire response including the content.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>The operation should complete as soon as a response is available and headers are read. The content is not read yet. </summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>A base class representing an HTTP entity body and content headers.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpContent" /> class.</summary>
    </member>
    <member name="P:System.Net.Http.HttpContent.ContentReadStream">
      <summary>Gets a stream representing the serialized HTTP content.  </summary>
      <returns>Returns <see cref="T:System.IO.Stream" />.A stream representing the serialized HTTP content.  </returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyTo(System.IO.Stream)">
      <summary>Write the HTTP content to a stream.</summary>
      <param name="stream">The target stream.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyTo(System.IO.Stream,System.Net.TransportContext)">
      <summary>Write the HTTP content to a stream.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>Write the HTTP content to a stream as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.The task object representing the asynchronous operation.</returns>
      <param name="stream">The target stream.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Write the HTTP content to a stream as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.The task object representing the asynchronous operation.</returns>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStream">
      <summary>Buffer the te HTTP content to a memory stream.</summary>
      <returns>Returns <see cref="T:System.IO.Stream" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpContent" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>Gets the HTTP content headers as defined in RFC 2616.</summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpContentHeaders" />.The content headers as defined in RFC 2616.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBuffer">
      <summary>Serialize the HTTP content to a memory buffer.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBuffer(System.Int32)">
      <summary>Serialize the HTTP content to a memory buffer.</summary>
      <param name="maxBufferSize">The maximum size, in bytes, of the buffer to use.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>Serialize the HTTP content to a memory buffer as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int32)">
      <summary>Serialize the HTTP content to a memory buffer as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.The task object representing the asynchronous operation.</returns>
      <param name="maxBufferSize">The maximum size, in bytes, of the buffer to use.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArray">
      <summary>Return the HTTP content as byte array.</summary>
      <returns>Returns <see cref="T:System.Byte" />.The HTTP content as byte array.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsString">
      <summary>Return the HTTP content as string.</summary>
      <returns>Returns <see cref="T:System.String" />.The HTTP content as a string.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStream(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize the HTTP content to a stream.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize the HTTP content to a stream as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.The task object representing the asynchronous operation.</returns>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether the HTTP content has a valid length in bytes.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if <paramref name="length" /> is a valid length; otherwise, false.</returns>
      <param name="length">The length in bytes of the HHTP content.</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>A base type for HTTP message handlers.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpMessageHandler" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpMessageHandler" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Send(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP message synchronously.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message.</returns>
      <param name="request">The HTTP message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>A helper class for retrieving and comparing standard HTTP methods.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpMethod" /> class with a specific HTTP method.</summary>
      <param name="method">The HTTP method.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>Represents an HTTP DELETE protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>Represents an HTTP GET protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>Represents an HTTP HEAD protocol method. The HEAD method is identical to GET except that the server only returns message-headers in the response, without a message-body.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>An HTTP method. </summary>
      <returns>Returns <see cref="T:System.String" />.An HTTP method represented as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>Represents an HTTP OPTIONS protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>Represents an HTTP POST protocol method that is used to post a new entity as an addition to a URI.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>Represents an HTTP PUT protocol method that is used to replace an entity identified by a URI.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>Returns <see cref="T:System.String" />.A string representing the current object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>Represents an HTTP TRACE protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>A base class for exceptions thrown by the <see cref="T:System.Net.Http.HttpClient" /> and <see cref="T:System.Net.Http.HttpMessageHandler" /> classes.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestException" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestException" /> class with a specific message that describes the current exception.</summary>
      <param name="message">A message that describes the current exception.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestException" /> class with a specific message that describes the current exception and an inner exception.</summary>
      <param name="message">A message that describes the current exception.</param>
      <param name="inner">The inner exception.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>Represents a HTTP request message.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestMessage" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestMessage" /> class with an HTTP method and a request <see cref="T:System.Uri" />.</summary>
      <param name="method">The HTTP method.</param>
      <param name="requestUri">A string that represents the request  <see cref="T:System.Uri" />.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestMessage" /> class with an HTTP method and a request <see cref="T:System.Uri" />.</summary>
      <param name="method">The HTTP method.</param>
      <param name="requestUri">The <see cref="T:System.Uri" /> to request.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>Gets or sets the contents of the HTTP message. </summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpContent" />.The content of a message</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpRequestMessage" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>Gets the collection of HTTP request headers.</summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.The collection of HTTP request headers.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>Gets or sets the HTTP method used by the HTTP request message.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.The HTTP method used by the request message. The default is the GET method.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>Gets a set of properties for the HTTP request.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>Gets or sets the <see cref="T:System.Uri" /> used for the HTTP request.</summary>
      <returns>Returns <see cref="T:System.Uri" />.The <see cref="T:System.Uri" /> used for the HTTP request.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>Returns <see cref="T:System.String" />.A string representation of the current object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>Gets or sets the HTTP message version.</summary>
      <returns>Returns <see cref="T:System.Version" />.The HTTP message version. The default is 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>Represents a HTTP response message.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpResponseMessage" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpResponseMessage" /> class with a specific <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />.</summary>
      <param name="statusCode">The status code of the HTTP response.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>Gets or sets the content of a HTTP response message. </summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpContent" />.The content of the HTTP response message.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>Releases the unmanaged resources and disposes of unmanaged resources used by the <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpResponseMessage" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>Throws an exception if the <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> property for the HTTP response is false.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.The HTTP response message if the call is successful.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>Gets the collection of HTTP response headers. </summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" />.The collection of HTTP response headers.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>Gets a value that indicates if the HTTP response was successful.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.A value that indicates if the HTTP response was successful. true if <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> was in the range 200-299; otherwise false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>Gets or sets the reason phrase which typically is sent by servers together with the status code. </summary>
      <returns>Returns <see cref="T:System.String" />.The reason phrase sent by the server.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>Gets or sets the request message which led to this response message.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpRequestMessage" />.The request message which led to this response message.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>Gets or sets the status code of the HTTP response.</summary>
      <returns>Returns <see cref="T:System.Net.HttpStatusCode" />.The status code of the HTTP response.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>Returns <see cref="T:System.String" />.A string representation of the current object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>Gets or sets the HTTP message version. </summary>
      <returns>Returns <see cref="T:System.Version" />.The HTTP message version. The default is 1.1. </returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>A base type for handlers which only do some small processing of request and/or response messages.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)"></member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <returns>Returns <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.</returns>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.Send(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <returns>Returns <see cref="T:System.Net.Http.HttpResponseMessage" />.</returns>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>Provides a collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized using the multipart/* content type specification.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor"></member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)"></member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)"></member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerator`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStream(System.IO.Stream,System.Net.TransportContext)"></member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <returns>Returns <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>Provides a container for content encoded using multipart/form-data MIME type.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor"></member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)"></member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)"></member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)"></member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>Provides HTTP content based on a stream.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)"></member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)"></member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStream">
      <returns>Returns <see cref="T:System.IO.Stream" />.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)"></member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStream(System.IO.Stream,System.Net.TransportContext)"></member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>Provides HTTP content based on a string.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)"></member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)"></member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Represents authentication information in Authorization, ProxyAuthorization, WWW-Authneticate, and Proxy-Authenticate header values.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Represents the value of the Cache-Control header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor"></member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Represents the value of the Content-Range header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)"></member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)"></member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)"></member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>Represents an entity-tag header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)"></member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <returns>Returns <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>Represents the collection of Content Headers as defined in RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <returns>Returns <see cref="T:System.Uri" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <returns>Returns <see cref="T:System.Byte" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <returns>Returns <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <returns>Returns <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>A collection of headers and their values as defined in RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.AddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.AddWithoutValidation(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerator`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <returns>Returns <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>Represents a collection of header values.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)"></member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerator`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)"></member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <returns>Returns <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>Represents the collection of Request Headers as defined in RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <returns>Returns <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <returns>Returns <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <returns>Returns <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <returns>Returns <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <returns>Returns <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <returns>Returns <see cref="T:System.Uri" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>Represents the collection of Response Headers as defined in RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <returns>Returns <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <returns>Returns <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <returns>Returns <see cref="T:System.Uri" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <returns>Returns <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>Represents a media-type as defined in the RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)"></member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)"></member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Represents a content-type header value with an additional quality.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)"></member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <returns>Returns <see cref="T:System.Double" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>Represents a name/value pair.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)"></member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>Represents a name/value pair with parameters.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)"></member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>Represents a product header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>Represents a value which can either be a product or a comment.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)"></member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)"></member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <returns>Returns <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>Represents a header value which can either be a date/time or an entity-tag value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)"></member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)"></member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)"></member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <returns>Returns <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Represents the value of the Range header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor"></member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})"></member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>Represents a byte-range header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})"></member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>Represents a header value which can either be a date/time or a timespan value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)"></member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)"></member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>Represents a string header value with an optional quality.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)"></member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <returns>Returns <see cref="T:System.Double" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Represents a transfer-coding header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)"></member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <returns>Returns <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>Represents a transfer-coding header value with optional quality.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)"></member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)"></member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <returns>Returns <see cref="T:System.Double" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Represents the value of a Via header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)"></member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>Represents a warning value used by the Warning header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)"></member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)"></member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <returns>Returns <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <returns>Returns <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.System#ICloneable#Clone">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
  </members>
</doc>