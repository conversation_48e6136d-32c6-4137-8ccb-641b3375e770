<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EntityFramework.SqlServer</name>
    </assembly>
    <members>
        <member name="T:System.Data.Entity.SqlServer.IDbSpatialValue">
            <summary>
            Adapter interface to make working with instances of <see cref="T:System.Data.Entity.Spatial.DbGeometry"/> or <see cref="T:System.Data.Entity.Spatial.DbGeography"/> easier.
            Implementing types wrap instances of DbGeography/DbGeometry and allow them to be consumed in a common way.
            This interface is implemented by wrapping types for two reasons:
            1. The DbGeography/DbGeometry classes cannot directly implement internal interfaces because their members are virtual (behavior is not guaranteed).
            2. The wrapping types ensure that instances of IDbSpatialValue handle the <see cref="T:System.NotImplementedException"/>s thrown
            by any unimplemented members of derived DbGeography/DbGeometry types that correspond to the properties and methods declared in the interface.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.DefaultSqlExecutionStrategy">
            <summary>
            An <see cref="T:System.Data.Entity.Infrastructure.IDbExecutionStrategy"/> that doesn't affect the execution but will throw a more helpful exception if a transient failure is detected.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.IDbSpatialValueExtensionMethods.AsSpatialValue(System.Data.Entity.Spatial.DbGeography)">
            <summary>
            Returns an instance of <see cref="T:System.Data.Entity.SqlServer.IDbSpatialValue"/> that wraps the specified <see cref="T:System.Data.Entity.Spatial.DbGeography"/> value.
            IDbSpatialValue members are guaranteed not to throw the <see cref="T:System.NotImplementedException"/>s caused by unimplemented members of their wrapped values.
            </summary>
            <param name="geographyValue"> The geography instance to wrap </param>
            <returns>
            An instance of <see cref="T:System.Data.Entity.SqlServer.IDbSpatialValue"/> that wraps the specified geography value
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.IDbSpatialValueExtensionMethods.AsSpatialValue(System.Data.Entity.Spatial.DbGeometry)">
            <summary>
            Returns an instance of <see cref="T:System.Data.Entity.SqlServer.IDbSpatialValue"/> that wraps the specified <see cref="T:System.Data.Entity.Spatial.DbGeometry"/> value.
            IDbSpatialValue members are guaranteed not to throw the <see cref="T:System.NotImplementedException"/>s caused by unimplemented members of their wrapped values.
            </summary>
            <param name="geometryValue"> The geometry instance to wrap </param>
            <returns>
            An instance of <see cref="T:System.Data.Entity.SqlServer.IDbSpatialValue"/> that wraps the specified geometry value
            </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.Resources.Strings">
            <summary>
            Strongly-typed and parameterized string resources.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.ArgumentIsNullOrWhitespace(System.Object)">
            <summary>
            A string like "The argument '{0}' cannot be null, empty or contain only white space."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.ProviderReturnedNullForGetDbInformation(System.Object)">
            <summary>
            A string like "The provider returned null for the informationType '{0}'."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.ProviderDoesNotSupportType(System.Object)">
            <summary>
            A string like "The underlying provider does not support the type '{0}'."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.NoStoreTypeForEdmType(System.Object,System.Object)">
            <summary>
            A string like "There is no store type corresponding to the conceptual side type '{0}' of primitive type '{1}'."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.Mapping_Provider_WrongManifestType(System.Object)">
            <summary>
            A string like "The provider manifest given is not of type '{0}'."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.ADP_InternalProviderError(System.Object)">
            <summary>
            A string like "Internal .NET Framework Data Provider error {0}."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_NeedSqlDataReader(System.Object)">
            <summary>
            A string like "Spatial readers can only be produced from readers of type SqlDataReader. A reader of type {0} was provided."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_InvalidGeographyColumn(System.Object)">
            <summary>
            A string like "Expected a geography value, found a value of type {0}."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_InvalidGeometryColumn(System.Object)">
            <summary>
            A string like "Expected a geometry value, found a value of type {0}."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.Mapping_Provider_WrongConnectionType(System.Object)">
            <summary>
            A string like "The connection is not of type '{0}'."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.Update_NotSupportedServerGenKey(System.Object)">
            <summary>
            A string like "Store-generated keys are only supported for identity columns. More than one key column is marked as server generated in table '{0}'."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.Update_NotSupportedIdentityType(System.Object,System.Object)">
            <summary>
            A string like "Store-generated keys are only supported for identity columns. Key column '{0}' has type '{1}', which is not a valid type for an identity column."
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:System.Data.Entity.SqlServer.Resources.Strings.Update_SqlEntitySetWithoutDmlFunctions(System.Object,System.Object,System.Object)" -->
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.Cqt_General_UnsupportedExpression(System.Object)">
            <summary>
            A string like "The expression '{0}' is of an unsupported type. "
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_InvalidDatePartArgumentExpression(System.Object,System.Object)">
            <summary>
            A string like "The DATEPART argument to the '{0}.{1}' function must be a literal string."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_InvalidDatePartArgumentValue(System.Object,System.Object,System.Object)">
            <summary>
            A string like "'{0}' is not a valid value for the DATEPART argument in the '{1}.{2}' function."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_TypedNaNNotSupported(System.Object)">
            <summary>
            A string like "Constant expressions of type {0} with a value of NaN are not supported by SQL Server."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_TypedPositiveInfinityNotSupported(System.Object,System.Object)">
            <summary>
            A string like "Constant expressions of type {0} with a value of {1}.PositiveInfinity are not supported by SQL Server."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_TypedNegativeInfinityNotSupported(System.Object,System.Object)">
            <summary>
            A string like "Constant expressions of type {0} with a value of {1}.NegativeInfinity are not supported by SQL Server."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_PrimitiveTypeNotSupportedPriorSql10(System.Object)">
            <summary>
            A string like "There is no store type that maps to the EDM type '{0}' on versions of SQL Server earlier than SQL Server 2008."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_CanonicalFunctionNotSupportedPriorSql10(System.Object)">
            <summary>
            A string like "The EDM function '{0}' is not supported on versions of SQL Server earlier than SQL Server 2008."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlSpatialServices_ProviderValueNotSqlType(System.Object)">
            <summary>
            A string like "The specified provider value is not compatible with this spatial services implementation. A value is required of type '{0}'."
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:System.Data.Entity.SqlServer.Resources.Strings.InvalidDatabaseName(System.Object)" -->
        <member name="M:System.Data.Entity.SqlServer.Resources.Strings.SqlServerMigrationSqlGenerator_UnknownOperation(System.Object,System.Object)">
            <summary>
            A string like "The current migration SQL generator ({0}) is unable to generate SQL for operations of type '{1}'. Call SetSqlGenerator on your migrations configuration class to use a different SQL generator. To create a custom SQL generator that can handle this type of operation, add a new class that derives from {0} and override Generate(MigrationOperation)."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_GeographyValueNotSqlCompatible">
            <summary>
            A string like "The specified DbGeography value could not be converted to a SQL Server compatible value."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_GeometryValueNotSqlCompatible">
            <summary>
            A string like "The specified DbGeometry value could not be converted to a SQL Server compatible value."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.UnableToDetermineStoreVersion">
            <summary>
            A string like "Could not determine storage version; a valid storage connection or a version hint is required."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_Sql2008RequiredForSpatial">
            <summary>
            A string like "Spatial types and functions are only supported by SQL Server 2008 or later."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_SqlTypesAssemblyNotFound">
            <summary>
            A string like "Spatial types and functions are not available for this provider because the assembly 'Microsoft.SqlServer.Types' version 10 or higher could not be found. "
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_IncompleteCreateDatabase">
            <summary>
            A string like "The database creation succeeded, but the creation of the database objects failed. The consequent attempt to drop the database also failed. See InnerException for details."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_IncompleteCreateDatabaseAggregate">
            <summary>
            A string like "See InnerExceptions for details."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_DdlGeneration_MissingInitialCatalog">
            <summary>
            A string like "Unable to complete operation. The supplied SqlConnection does not specify an initial catalog or AttachDBFileName."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_DdlGeneration_CannotDeleteDatabaseNoInitialCatalog">
            <summary>
            A string like "Unable to delete the database. There is no database that corresponds to the given AttachDBFileName."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_DdlGeneration_CannotTellIfDatabaseExists">
            <summary>
            A string like "A connection to the specified database could not be opened. See InnerException for details. However, there is a database registered with the server that corresponds to the given AttachDbFileName."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlProvider_CredentialsMissingForMasterConnection">
            <summary>
            A string like "This operation requires a connection to the 'master' database. Unable to create a connection to the 'master' database because the original database connection has been opened and credentials have been removed from the connection string. Supply an unopened connection."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_ApplyNotSupportedOnSql8">
            <summary>
            A string like "The execution of this query requires the APPLY operator, which is not supported in versions of SQL Server earlier than SQL Server 2005."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_NiladicFunctionsCannotHaveParameters">
            <summary>
            A string like "Functions listed in the provider manifest that are attributed as NiladicFunction='true' cannot have parameter declarations."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_ParameterForLimitNotSupportedOnSql8">
            <summary>
            A string like "Parameters as arguments to a TOP sub-clause and a LIMIT sub-clause in a query, or a LimitExpression in a command tree, are not supported in versions of SQL Server earlier than SQL Server 2005."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlGen_ParameterForSkipNotSupportedOnSql8">
            <summary>
            A string like "Parameters as arguments to a SKIP sub-clause in a query, or a SkipExpression in a command tree, are not supported in versions of SQL Server earlier than SQL Server 2005."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.Spatial_WellKnownGeographyValueNotValid">
            <summary>
            A string like "The specified DbWellKnownGeographyValue does not contain either Well-Known Text or Well-Known Binary."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.Spatial_WellKnownGeometryValueNotValid">
            <summary>
            A string like "The specified DbWellKnownGeometryValue does not contain either Well-Known Text or Well-Known Binary."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlSpatialservices_CouldNotCreateWellKnownGeographyValueNoSrid">
            <summary>
            A string like "A Spatial Reference System Identifier (SRID) value could not be retrieved from the specified DbGeography value."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlSpatialservices_CouldNotCreateWellKnownGeographyValueNoWkbOrWkt">
            <summary>
            A string like "The specified DbGeography value did not provide either Well-Known Binary or Well-Known Text."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlSpatialservices_CouldNotCreateWellKnownGeometryValueNoSrid">
            <summary>
            A string like "A Spatial Reference System Identifier (SRID) value could not be retrieved from the specified DbGeometry value."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.SqlSpatialservices_CouldNotCreateWellKnownGeometryValueNoWkbOrWkt">
            <summary>
            A string like "The specified DbGeometry value did not provide either Well-Known Binary or Well-Known Text."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.TransientExceptionDetected">
            <summary>
            A string like "An exception has been raised that is likely due to a transient failure. If you are connecting to a SQL Azure database consider using SqlAzureExecutionStrategy."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.ELinq_DbFunctionDirectCall">
            <summary>
            A string like "This function can only be invoked from LINQ to Entities."
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.Resources.Strings.AutomaticMigration">
            <summary>
            A string like "AutomaticMigration"
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.Resources.Error">
            <summary>
            Strongly-typed and parameterized exception factory.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:System.Data.Entity.SqlServer.Resources.Error.InvalidDatabaseName(System.Object)" -->
        <member name="M:System.Data.Entity.SqlServer.Resources.Error.SqlServerMigrationSqlGenerator_UnknownOperation(System.Object,System.Object)">
            <summary>
            InvalidOperationException with message like "The current migration SQL generator ({0}) is unable to generate SQL for operations of type '{1}'. Call SetSqlGenerator on your migrations configuration class to use a different SQL generator. To create a custom SQL generator that can handle this type of operation, add a new class that derives from {0} and override Generate(MigrationOperation)."
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Error.ArgumentOutOfRange(System.String)">
            <summary>
            The exception that is thrown when the value of an argument is outside the allowable range of values as defined by the invoked method.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Error.NotImplemented">
            <summary>
            The exception that is thrown when the author has yet to implement the logic at this point in the program. This can act as an exception based TODO tag.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Resources.Error.NotSupported">
            <summary>
            The exception that is thrown when an invoked method is not supported, or when there is an attempt to
            read, seek, or write to a stream that does not support the invoked functionality.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.Resources.EntityRes">
            <summary>
            AutoGenerated resource class. Usage:
            string s = EntityRes.GetString(EntityRes.MyIdenfitier);
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlAzureExecutionStrategy">
            <summary>
            An <see cref="T:System.Data.Entity.Infrastructure.IDbExecutionStrategy"/> that retries actions that throw exceptions caused by SQL Azure transient failures.
            </summary>
            <remarks>
            This execution strategy will retry the operation on <see cref="T:System.TimeoutException"/> and <see cref="T:System.Data.SqlClient.SqlException"/>
            if the <see cref="P:System.Data.SqlClient.SqlException.Errors"/> contains any of the following error numbers:
            40613, 40501, 40197, 10929, 10928, 10060, 10054, 10053, 233, 64 and 20
            </remarks>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlAzureExecutionStrategy.#ctor">
            <summary>
            Creates a new instance of <see cref="T:System.Data.Entity.SqlServer.SqlAzureExecutionStrategy"/>.
            </summary>
            <remarks>
            The default retry limit is 5, which means that the total amount of time spent between retries is 26 seconds plus the random factor.
            </remarks>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlAzureExecutionStrategy.#ctor(System.Int32,System.TimeSpan)">
            <summary>
            Creates a new instance of <see cref="T:System.Data.Entity.SqlServer.SqlAzureExecutionStrategy"/> with the specified limits for
            number of retries and the delay between retries.
            </summary>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
            <param name="maxDelay"> The maximum delay in milliseconds between retries. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlAzureExecutionStrategy.ShouldRetryOn(System.Exception)">
            <inheritdoc/>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlAzureRetriableExceptionDetector">
            <summary>
            Detects the exceptions caused by SQL Azure transient failures.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlDdlBuilder.AppendSql(System.String)">
            <summary>
            Appends raw SQL into the string builder.
            </summary>
            <param name="text"> Raw SQL string to append into the string builder. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlDdlBuilder.AppendNewLine">
            <summary>
            Appends new line for visual formatting or for ending a comment.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlDdlBuilder.AppendSqlInvariantFormat(System.String,System.Object[])">
            <summary>
            Append raw SQL into the string builder with formatting options and invariant culture formatting.
            </summary>
            <param name="format"> A composite format string. </param>
            <param name="args"> An array of objects to format. </param>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlFunctions">
            <summary>
            Contains function stubs that expose SqlServer methods in Linq to Entities.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.ChecksumAggregate(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>Returns the checksum of the values in a collection. Null values are ignored.</summary>
            <returns>The checksum computed from the input collection.</returns>
            <param name="arg">The collection of values over which the checksum is computed.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.ChecksumAggregate(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
            <summary>Returns the checksum of the values in a collection. Null values are ignored.</summary>
            <returns>The checksum computed from the input collection.</returns>
            <param name="arg">The collection of values over which the checksum is computed.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Ascii(System.String)">
            <summary>Returns the ASCII code value of the left-most character of a character expression.</summary>
            <returns>The ASCII code of the first character in the input string.</returns>
            <param name="arg">A valid string.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Char(System.Nullable{System.Int32})">
            <summary>Returns the character that corresponds to the specified integer ASCII value.</summary>
            <returns>The character that corresponds to the specified ASCII value.</returns>
            <param name="arg">An ASCII code.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CharIndex(System.String,System.String)">
            <summary>Returns the starting position of one expression found within another expression.</summary>
            <returns>The starting position of  target  if it is found in  toSearch .</returns>
            <param name="toSearch">The string expression to be searched.</param>
            <param name="target">The string expression to be found.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CharIndex(System.Byte[],System.Byte[])">
            <summary>Returns the starting position of one expression found within another expression.</summary>
            <returns>The starting position of  target  if it is found in  toSearch .</returns>
            <param name="toSearch">The string expression to be searched.</param>
            <param name="target">The string expression to be found.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CharIndex(System.String,System.String,System.Nullable{System.Int32})">
            <summary>Returns the starting position of one expression found within another expression.</summary>
            <returns>The starting position of  target  if it is found in  toSearch .</returns>
            <param name="toSearch">The string expression to be searched.</param>
            <param name="target">The string expression to be found.</param>
            <param name="startLocation">The character position in  toSearch  where searching begins.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CharIndex(System.Byte[],System.Byte[],System.Nullable{System.Int32})">
            <summary>Returns the starting position of one expression found within another expression.</summary>
            <returns>The starting position of  target  if it is found in  toSearch .</returns>
            <param name="toSearch">The string expression to be searched.</param>
            <param name="target">The string expression to be found.</param>
            <param name="startLocation">The character position in  toSearch  where searching begins.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CharIndex(System.String,System.String,System.Nullable{System.Int64})">
            <summary>Returns the starting position of one expression found within another expression.</summary>
            <returns>
            A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" /> value that is the starting position of  target  if it is found in  toSearch .
            </returns>
            <param name="toSearch">The string expression to be searched.</param>
            <param name="target">The string expression to be found.</param>
            <param name="startLocation">The character position in  toSearch  where searching begins.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CharIndex(System.Byte[],System.Byte[],System.Nullable{System.Int64})">
            <summary>Returns the starting position of one expression found within another expression.</summary>
            <returns>The starting position of  target  if it is found in  toSearch .</returns>
            <param name="toSearch">The string expression to be searched.</param>
            <param name="target">The string expression to be found.</param>
            <param name="startLocation">The character position in  toSearch  at which searching begins.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Difference(System.String,System.String)">
            <summary>Returns an integer value that indicates the difference between the SOUNDEX values of two character expressions.</summary>
            <returns>The SOUNDEX difference between the two strings.</returns>
            <param name="string1">The first string.</param>
            <param name="string2">The second string.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.NChar(System.Nullable{System.Int32})">
            <summary>Returns the Unicode character with the specified integer code, as defined by the Unicode standard.</summary>
            <returns>The character that corresponds to the input character code.</returns>
            <param name="arg">A character code.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.PatIndex(System.String,System.String)">
            <summary>Returns the starting position of the first occurrence of a pattern in a specified expression, or zeros if the pattern is not found, on all valid text and character data types.</summary>
            <returns>The starting character position where the string pattern was found.</returns>
            <param name="stringPattern">A string pattern to search for.</param>
            <param name="target">The string to search.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.QuoteName(System.String)">
            <summary>Returns a Unicode string with the delimiters added to make the input string a valid Microsoft SQL Server delimited identifier.</summary>
            <returns>The original string with brackets added.</returns>
            <param name="stringArg">The expression that quote characters will be added to.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.QuoteName(System.String,System.String)">
            <summary>Returns a Unicode string with the delimiters added to make the input string a valid Microsoft SQL Server delimited identifier.</summary>
            <returns>The original string with the specified quote characters added.</returns>
            <param name="stringArg">The expression that quote characters will be added to.</param>
            <param name="quoteCharacter">The one-character string to use as the delimiter. It can be a single quotation mark ( ' ), a left or right bracket ( [ ] ), or a double quotation mark ( " ). If quote_character is not specified, brackets are used.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Replicate(System.String,System.Nullable{System.Int32})">
            <summary>Repeats a string value a specified number of times.</summary>
            <returns>The target string, repeated the number of times specified by  count .</returns>
            <param name="target">A valid string.</param>
            <param name="count">The value that specifies how many time to repeat  target .</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.SoundCode(System.String)">
            <summary>Converts an alphanumeric string to a four-character (SOUNDEX) code to find similar-sounding words or names.</summary>
            <returns>The SOUNDEX code of the input string.</returns>
            <param name="arg">A valid string.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Space(System.Nullable{System.Int32})">
            <summary>Returns a string of repeated spaces.</summary>
            <returns>A string that consists of the specified number of spaces.</returns>
            <param name="arg1">The number of spaces. If negative, a null string is returned.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.StringConvert(System.Nullable{System.Double})">
            <summary>Returns character data converted from numeric data.</summary>
            <returns>The numeric input expression converted to a string.</returns>
            <param name="number">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.StringConvert(System.Nullable{System.Decimal})">
            <summary>Returns character data converted from numeric data.</summary>
            <returns>The input expression converted to a string.</returns>
            <param name="number">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.StringConvert(System.Nullable{System.Double},System.Nullable{System.Int32})">
            <summary>Returns character data converted from numeric data.</summary>
            <returns>The numeric input expression converted to a string.</returns>
            <param name="number">A numeric expression.</param>
            <param name="length">The total length of the string. This includes decimal point, sign, digits, and spaces. The default is 10.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.StringConvert(System.Nullable{System.Decimal},System.Nullable{System.Int32})">
            <summary>Returns character data converted from numeric data.</summary>
            <returns>The input expression converted to a string.</returns>
            <param name="number">A numeric expression.</param>
            <param name="length">The total length of the string. This includes decimal point, sign, digits, and spaces. The default is 10.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.StringConvert(System.Nullable{System.Double},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>Returns character data converted from numeric data.</summary>
            <returns>The numeric input expression converted to a string.</returns>
            <param name="number">A numeric expression.</param>
            <param name="length">The total length of the string. This includes decimal point, sign, digits, and spaces. The default is 10.</param>
            <param name="decimalArg">The number of places to the right of the decimal point.  decimal  must be less than or equal to 16. If  decimal  is more than 16 then the result is truncated to sixteen places to the right of the decimal point.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.StringConvert(System.Nullable{System.Decimal},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>Returns character data converted from numeric data.</summary>
            <returns>The input expression converted to a string.</returns>
            <param name="number">A numeric expression.</param>
            <param name="length">The total length of the string. This includes decimal point, sign, digits, and spaces. The default is 10.</param>
            <param name="decimalArg">The number of places to the right of the decimal point.  decimal  must be less than or equal to 16. If  decimal  is more than 16 then the result is truncated to sixteen places to the right of the decimal point.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Stuff(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>Inserts a string into another string. It deletes a specified length of characters in the target string at the start position and then inserts the second string into the target string at the start position.</summary>
            <returns>A string consisting of the two strings.</returns>
            <param name="stringInput">The target string.</param>
            <param name="start">The character position in  stringinput  where the replacement string is to be inserted.</param>
            <param name="length">The number of characters to delete from  stringInput . If  length  is longer than  stringInput , deletion occurs up to the last character in  stringReplacement .</param>
            <param name="stringReplacement">The substring to be inserted into  stringInput .</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Unicode(System.String)">
            <summary>Returns the integer value, as defined by the Unicode standard, for the first character of the input expression.</summary>
            <returns>The character code for the first character in the input string.</returns>
            <param name="arg">A valid string.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Acos(System.Nullable{System.Double})">
            <summary>A mathematical function that returns the angle, in radians, whose cosine is the specified numerical value. This angle is called the arccosine.</summary>
            <returns>The angle, in radians, defined by the input cosine value.</returns>
            <param name="arg1">The cosine of an angle.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Acos(System.Nullable{System.Decimal})">
            <summary>A mathematical function that returns the angle, in radians, whose cosine is the specified numerical value. This angle is called the arccosine.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg1">The cosine of an angle.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Asin(System.Nullable{System.Double})">
            <summary>A mathematical function that returns the angle, in radians, whose sine is the specified numerical value. This angle is called the arcsine.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg">The sine of an angle.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Asin(System.Nullable{System.Decimal})">
            <summary>A mathematical function that returns the angle, in radians, whose sine is the specified numerical value. This angle is called the arcsine.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg">The sine of an angle.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Atan(System.Nullable{System.Double})">
            <summary>A mathematical function that returns the angle, in radians, whose tangent is the specified numerical value. This angle is called the arctangent.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg">The tangent of an angle.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Atan(System.Nullable{System.Decimal})">
            <summary>A mathematical function that returns the angle, in radians, whose tangent is the specified numerical value. This angle is called the arctangent.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg">The tangent of an angle.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Atan2(System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Returns the positive angle, in radians, between the positive x-axis and the ray from the origin through the point (x, y), where x and y are the two specified numerical values. The first parameter passed to the function is the y-value and the second parameter is the x-value.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg1">The y-coordinate of a point.</param>
            <param name="arg2">The x-coordinate of a point.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Atan2(System.Nullable{System.Decimal},System.Nullable{System.Decimal})">
            <summary>Returns the positive angle, in radians, between the positive x-axis and the ray from the origin through the point (x, y), where x and y are the two specified numerical values. The first parameter passed to the function is the y-value and the second parameter is the x-value.</summary>
            <returns>An angle, measured in radians.</returns>
            <param name="arg1">The y-coordinate of a point.</param>
            <param name="arg2">The x-coordinate of a point.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Cos(System.Nullable{System.Double})">
            <summary>Returns the trigonometric cosine of the specified angle, in radians, in the specified expression.</summary>
            <returns>The trigonometric cosine of the specified angle.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Cos(System.Nullable{System.Decimal})">
            <summary>Returns the trigonometric cosine of the specified angle, in radians, in the specified expression.</summary>
            <returns>The trigonometric cosine of the specified angle.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Cot(System.Nullable{System.Double})">
            <summary>A mathematical function that returns the trigonometric cotangent of the specified angle, in radians.</summary>
            <returns>The trigonometric cotangent of the specified angle.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Cot(System.Nullable{System.Decimal})">
            <summary>A mathematical function that returns the trigonometric cotangent of the specified angle, in radians.</summary>
            <returns>The trigonometric cotangent of the specified angle.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Degrees(System.Nullable{System.Int32})">
            <summary>Returns the corresponding angle in degrees for an angle specified in radians.</summary>
            <returns>The specified angle converted to degrees.</returns>
            <param name="arg1">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Degrees(System.Nullable{System.Int64})">
            <summary>Returns the corresponding angle in degrees for an angle specified in radians.</summary>
            <returns>The specified angle converted to degrees.</returns>
            <param name="arg1">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Degrees(System.Nullable{System.Decimal})">
            <summary>Returns the corresponding angle in degrees for an angle specified in radians.</summary>
            <returns>The specified angle converted to degrees.</returns>
            <param name="arg1">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Degrees(System.Nullable{System.Double})">
            <summary>Returns the corresponding angle in degrees for an angle specified in radians.</summary>
            <returns>The specified angle converted to degrees.</returns>
            <param name="arg1">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Exp(System.Nullable{System.Double})">
            <summary>Returns the exponential value of the specified float expression.</summary>
            <returns>The constant e raised to the power of the input value.</returns>
            <param name="arg">The input value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Exp(System.Nullable{System.Decimal})">
            <summary>Returns the exponential value of the specified float expression.</summary>
            <returns>The constant e raised to the power of the input value.</returns>
            <param name="arg">The input value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Log(System.Nullable{System.Double})">
            <summary>Returns the natural logarithm of the specified input value.</summary>
            <returns>The natural logarithm of the input value.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Log(System.Nullable{System.Decimal})">
            <summary>Returns the natural logarithm of the specified input value.</summary>
            <returns>The natural logarithm of the input value.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Log10(System.Nullable{System.Double})">
            <summary>Returns the base-10 logarithm of the specified input value.</summary>
            <returns>The base-10 logarithm of the input value.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Log10(System.Nullable{System.Decimal})">
            <summary>Returns the base-10 logarithm of the specified input value.</summary>
            <returns>The base-10 logarithm of the input value.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Pi">
            <summary>Returns the constant value of pi.</summary>
            <returns>The numeric value of pi.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Radians(System.Nullable{System.Int32})">
            <summary>Returns the radian measure corresponding to the specified angle in degrees.</summary>
            <returns>The radian measure of the specified angle.</returns>
            <param name="arg">The angle, measured in degrees</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Radians(System.Nullable{System.Int64})">
            <summary>Returns the radian measure corresponding to the specified angle in degrees.</summary>
            <returns>The radian measure of the specified angle.</returns>
            <param name="arg">The angle, measured in degrees</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Radians(System.Nullable{System.Decimal})">
            <summary>Returns the radian measure corresponding to the specified angle in degrees.</summary>
            <returns>The radian measure of the specified angle.</returns>
            <param name="arg">The angle, measured in degrees.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Radians(System.Nullable{System.Double})">
            <summary>Returns the radian measure corresponding to the specified angle in degrees.</summary>
            <returns>The radian measure of the specified angle.</returns>
            <param name="arg">The angle, measured in degrees.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Rand">
            <summary>Returns a pseudo-random float value from 0 through 1, exclusive.</summary>
            <returns>The pseudo-random value.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Rand(System.Nullable{System.Int32})">
            <summary>Returns a pseudo-random float value from 0 through 1, exclusive.</summary>
            <returns>The pseudo-random value.</returns>
            <param name="seed">The seed value. If  seed  is not specified, the SQL Server Database Engine assigns a seed value at random. For a specified seed value, the result returned is always the same.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Sign(System.Nullable{System.Int32})">
            <summary>Returns the positive (+1), zero (0), or negative (-1) sign of the specified expression.</summary>
            <returns>The sign of the input expression.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Sign(System.Nullable{System.Int64})">
            <summary>Returns the positive (+1), zero (0), or negative (-1) sign of the specified expression.</summary>
            <returns>The sign of the input expression.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Sign(System.Nullable{System.Decimal})">
            <summary>Returns the positive (+1), zero (0), or negative (-1) sign of the specified expression.</summary>
            <returns>The sign of the input expression.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Sign(System.Nullable{System.Double})">
            <summary>Returns the positive (+1), zero (0), or negative (-1) sign of the specified expression.</summary>
            <returns>The sign of the input expression.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Sin(System.Nullable{System.Decimal})">
            <summary>Returns the trigonometric sine of the specified angle.</summary>
            <returns>The trigonometric sine of the input expression.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Sin(System.Nullable{System.Double})">
            <summary>Returns the trigonometric sine of the specified angle.</summary>
            <returns>The trigonometric sine of the input expression.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.SquareRoot(System.Nullable{System.Double})">
            <summary>Returns the square root of the specified number.</summary>
            <returns>The square root of the input value.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.SquareRoot(System.Nullable{System.Decimal})">
            <summary>Returns the square root of the specified number.</summary>
            <returns>The square root of the input value.</returns>
            <param name="arg">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Square(System.Nullable{System.Double})">
            <summary>Returns the square of the specified number.</summary>
            <returns>The square of the input value.</returns>
            <param name="arg1">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Square(System.Nullable{System.Decimal})">
            <summary>Returns the square of the specified number.</summary>
            <returns>The square of the input value.</returns>
            <param name="arg1">A numeric expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Tan(System.Nullable{System.Double})">
            <summary>Returns the trigonometric tangent of the input expression.</summary>
            <returns>The tangent of the input angle.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Tan(System.Nullable{System.Decimal})">
            <summary>Returns the trigonometric tangent of the input expression.</summary>
            <returns>The tangent of the input angle.</returns>
            <param name="arg">An angle, measured in radians.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateAdd(System.String,System.Nullable{System.Double},System.Nullable{System.DateTime})">
            <summary>Returns a new datetime value based on adding an interval to the specified date.</summary>
            <returns>The new date.</returns>
            <param name="datePartArg">The part of the date to increment. </param>
            <param name="number">The value used to increment a date by a specified amount.</param>
            <param name="date">The date to increment.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateAdd(System.String,System.Nullable{System.Double},System.Nullable{System.TimeSpan})">
            <summary>Returns a new time span value based on adding an interval to the specified time span.</summary>
            <returns>The new time span.</returns>
            <param name="datePartArg">The part of the date to increment.</param>
            <param name="number">The value used to increment a date by a specified amount.</param>
            <param name="time">The time span to increment.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateAdd(System.String,System.Nullable{System.Double},System.Nullable{System.DateTimeOffset})">
            <summary>Returns a new date value based on adding an interval to the specified date.</summary>
            <returns>The new point in time, expressed as a date and time of day, relative to Coordinated Universal Time (UTC).</returns>
            <param name="datePartArg">The part of the date to increment.</param>
            <param name="number">The value used to increment a date by a specified amount.</param>
            <param name="dateTimeOffsetArg">The date to increment.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateAdd(System.String,System.Nullable{System.Double},System.String)">
            <summary>Returns a new datetime value based on adding an interval to the specified date.</summary>
            <returns>
            A <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" /> value that is the new date.
            </returns>
            <param name="datePartArg">The part of the date to increment.</param>
            <param name="number">The value used to increment a date by a specified amount.</param>
            <param name="date">The date to increment.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.String,System.Nullable{System.DateTime})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.String,System.Nullable{System.DateTimeOffset})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.String,System.Nullable{System.TimeSpan})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The value specifying the number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.TimeSpan},System.String)">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTime},System.String)">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTimeOffset},System.String)">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.String,System.String)">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.TimeSpan},System.Nullable{System.DateTime})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.TimeSpan},System.Nullable{System.DateTimeOffset})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTime},System.Nullable{System.TimeSpan})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTimeOffset},System.Nullable{System.TimeSpan})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two Dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTimeOffset})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateDiff(System.String,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTime})">
            <summary>Returns the count of the specified datepart boundaries crossed between the specified start date and end date.</summary>
            <returns>The number of time intervals between the two dates.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="startDate">The first date.</param>
            <param name="endDate">The second date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateName(System.String,System.Nullable{System.DateTime})">
            <summary>Returns a character string that represents the specified datepart of the specified date.</summary>
            <returns>The specified part of the specified date.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateName(System.String,System.String)">
            <summary>Returns a character string that represents the specified datepart of the specified date.</summary>
            <returns>The specified part of the specified date.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateName(System.String,System.Nullable{System.TimeSpan})">
            <summary>Returns a character string that represents the specified datepart of the specified date.</summary>
            <returns>The specified part of the specified date.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DateName(System.String,System.Nullable{System.DateTimeOffset})">
            <summary>Returns a character string that represents the specified datepart of the specified date.</summary>
            <returns>The specified part of the specified date.</returns>
            <param name="datePartArg">The part of the date to calculate the differing number of time intervals.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DatePart(System.String,System.Nullable{System.DateTime})">
            <summary>Returns an integer that represents the specified datepart of the specified date.</summary>
            <returns>The the specified datepart of the specified date.</returns>
            <param name="datePartArg">The part of the date to return the value.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DatePart(System.String,System.Nullable{System.DateTimeOffset})">
            <summary>Returns an integer that represents the specified datepart of the specified date.</summary>
            <returns>The specified datepart of the specified date.</returns>
            <param name="datePartArg">The part of the date to return the value.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DatePart(System.String,System.String)">
            <summary>Returns an integer that represents the specified datepart of the specified date.</summary>
            <returns>The specified datepart of the specified date.</returns>
            <param name="datePartArg">The part of the date to return the value.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DatePart(System.String,System.Nullable{System.TimeSpan})">
            <summary>Returns an integer that represents the specified datepart of the specified date.</summary>
            <returns>The specified datepart of the specified date.</returns>
            <param name="datePartArg">The part of the date to return the value.</param>
            <param name="date">The date.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.GetDate">
            <summary>Returns the current database system timestamp as a datetime value without the database time zone offset. This value is derived from the operating system of the computer on which the instance of SQL Server is running.</summary>
            <returns>The current database timestamp.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.GetUtcDate">
            <summary>Returns the current database system timestamp as a datetime value. The database time zone offset is not included. This value represents the current UTC time (Coordinated Universal Time). This value is derived from the operating system of the computer on which the instance of SQL Server is running.</summary>
            <returns>The current database UTC timestamp.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.Boolean})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.Double})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.Decimal})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.DateTime})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.TimeSpan})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.DateTimeOffset})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.String)">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Byte[])">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.DataLength(System.Nullable{System.Guid})">
            <summary>Returns the number of bytes used to represent any expression.</summary>
            <returns>The number of bytes in the input value.</returns>
            <param name="arg">The value to be examined for data length.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Boolean})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Double})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Decimal})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.String)">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.DateTime})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.TimeSpan})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.DateTimeOffset})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Byte[])">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The character array for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Guid})">
            <summary>Returns the checksum value computed over the input argument.</summary>
            <returns>The checksum computed over the input value.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Decimal},System.Nullable{System.Decimal})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.String,System.String)">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Byte[],System.Byte[])">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The character array for which the checksum is calculated.</param>
            <param name="arg2">The character array for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Double},System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Decimal},System.Nullable{System.Decimal},System.Nullable{System.Decimal})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.String,System.String,System.String)">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Byte[],System.Byte[],System.Byte[])">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The character array for which the checksum is calculated.</param>
            <param name="arg2">The character array for which the checksum is calculated.</param>
            <param name="arg3">The character array for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.Checksum(System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>Returns the checksum value computed over the input arguments.</summary>
            <returns>The checksum computed over the input values.</returns>
            <param name="arg1">The value for which the checksum is calculated.</param>
            <param name="arg2">The value for which the checksum is calculated.</param>
            <param name="arg3">The value for which the checksum is calculated.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CurrentTimestamp">
            <summary>Returns the current date and time. </summary>
            <returns>The current date and time.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.CurrentUser">
            <summary>Returns the name of the current user.</summary>
            <returns>The name of the current user.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.HostName">
            <summary>Returns the workstation name.</summary>
            <returns>The name of the workstation.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.UserName(System.Nullable{System.Int32})">
            <summary>Returns a database user name corresponding to a specified identification number.</summary>
            <returns>The user name.</returns>
            <param name="arg">A user ID.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.UserName">
            <summary>Returns a database user name corresponding to a specified identification number.</summary>
            <returns>The user name.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.IsNumeric(System.String)">
            <summary>Indicates whether the input value is a valid numeric type.</summary>
            <returns>1 if the input expression is a valid numeric data type; otherwise, 0.</returns>
            <param name="arg">A string expression.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlFunctions.IsDate(System.String)">
            <summary>Indicates whether the input value is a valid date or time.</summary>
            <returns>1 if the input expression is a valid date or time value of datetime or smalldatetime data types; otherwise, 0.</returns>
            <param name="arg">The tested value.</param>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.BoolWrapper">
            <summary>
            Used for wrapping a boolean value as an object.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator">
            <summary>
            Class generating SQL for a DML command tree.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator.UseGeneratedValuesVariable(System.Data.Entity.Core.Common.CommandTrees.DbInsertCommandTree,System.Data.Entity.SqlServer.SqlVersion)">
            <summary>
            Determine whether we should use a generated values variable to return server generated values.
            This is true when we're attempting to insert a row where the primary key is server generated
            but is not an integer type (and therefore can't be used with scope_identity()). It is also true
            where there is a compound server generated key.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator.GenerateReturningSql(System.Data.Entity.SqlServer.SqlGen.SqlStringBuilder,System.Data.Entity.Core.Common.CommandTrees.DbModificationCommandTree,System.Data.Entity.Core.Metadata.Edm.EntityType,System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator.ExpressionTranslator,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Boolean)">
            <summary>
            Generates SQL fragment returning server-generated values.
            Requires: translator knows about member values so that we can figure out
            how to construct the key predicate.
            <code>Sample SQL:
            
                select IdentityValue
                from dbo.MyTable
                where @@ROWCOUNT > 0 and IdentityValue = scope_identity()
            
                or
            
                select TimestampValue
                from dbo.MyTable
                where @@ROWCOUNT > 0 and Id = 1
            
                Note that we filter on rowcount to ensure no rows are returned if no rows were modified.
            
                On SQL Server 2005 and up, we have an additional syntax used for non integer return types:
            
                declare @generatedValues table(ID uniqueidentifier)
                insert dbo.MyTable
                output ID into @generated_values
                values (...);
                select ID
                from @generatedValues as g join dbo.MyTable as t on g.ID = t.ID
                where @@ROWCOUNT > 0;</code>
            </summary>
            <param name="commandText"> Builder containing command text </param>
            <param name="tree"> Modification command tree </param>
            <param name="tableType"> Type of table. </param>
            <param name="translator"> Translator used to produce DML SQL statement for the tree </param>
            <param name="returning"> Returning expression. If null, the method returns immediately without producing a SELECT statement. </param>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator.ExpressionTranslator">
            <summary>
            Lightweight expression translator for DML expression trees, which have constrained
            scope and support.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator.ExpressionTranslator.#ctor(System.Data.Entity.SqlServer.SqlGen.SqlStringBuilder,System.Data.Entity.Core.Common.CommandTrees.DbModificationCommandTree,System.Boolean,System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Collections.Generic.ICollection{System.Data.Entity.Core.Metadata.Edm.EdmProperty},System.Boolean)">
            <summary>
            Initialize a new expression translator populating the given string builder
            with command text. Command text builder and command tree must not be null.
            </summary>
            <param name="commandText"> Command text with which to populate commands </param>
            <param name="commandTree"> Command tree generating SQL </param>
            <param name="preserveMemberValues"> Indicates whether the translator should preserve member values while compiling t-SQL (only needed for server generation) </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.DmlSqlGenerator.ExpressionTranslator.RegisterMemberValue(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Call this method to register a property value pair so the translator "remembers"
            the values for members of the row being modified. These values can then be used
            to form a predicate for server-generation (based on the key of the row)
            </summary>
            <param name="propertyExpression"> DbExpression containing the column reference (property expression). </param>
            <param name="value"> DbExpression containing the value of the column. </param>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator">
            <summary>
            Provider to convert provider agnostic migration operations into SQL commands
            that can be run against a Microsoft SQL Server database.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Collections.Generic.IEnumerable{System.Data.Entity.Migrations.Model.MigrationOperation},System.String)">
            <summary>
            Converts a set of migration operations into Microsoft SQL Server specific SQL.
            </summary>
            <param name="migrationOperations"> The operations to be converted. </param>
            <param name="providerManifestToken"> Token representing the version of SQL Server being targeted (i.e. "2005", "2008"). </param>
            <returns> A list of SQL statements to be executed to perform the migration operations. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.GenerateProcedureBody(System.Collections.Generic.ICollection{System.Data.Entity.Core.Common.CommandTrees.DbModificationCommandTree},System.String,System.String)">
            <summary>
            Generates the SQL body for a stored procedure.
            </summary>
            <param name="commandTrees">The command trees representing the commands for an insert, update or delete operation.</param>
            <param name="rowsAffectedParameter">The rows affected parameter name.</param>
            <param name="providerManifestToken">The provider manifest token.</param>
            <returns>The SQL body for the stored procedure.</returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.UpdateDatabaseOperation)">
            <summary>
            Generates the specified update database operation which represents applying a series of migrations.
            The generated script is idempotent, meaning it contains conditional logic to check if individual migrations 
            have already been applied and only apply the pending ones.
            </summary>
            <param name="updateDatabaseOperation">The update database operation.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.MigrationOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.MigrationOperation"/>.
            Allows derived providers to handle additional operation types.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="migrationOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.CreateConnection">
            <summary>
            Creates an empty connection for the current provider.
            Allows derived providers to use connection other than <see cref="T:System.Data.SqlClient.SqlConnection"/>.
            </summary>
            <returns> An empty connection for the current provider. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateProcedureOperation)">
            <summary>
            Generates the specified create procedure operation.
            </summary>
            <param name="createProcedureOperation">The create procedure operation.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AlterProcedureOperation)">
            <summary>
            Generates the specified alter procedure operation.
            </summary>
            <param name="alterProcedureOperation">The alter procedure operation.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropProcedureOperation)">
            <summary>
            Generates the specified drop procedure operation.
            </summary>
            <param name="dropProcedureOperation">The drop procedure operation.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateTableOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.CreateTableOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="createTableOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.GenerateMakeSystemTable(System.Data.Entity.Migrations.Model.CreateTableOperation,System.Data.Entity.Migrations.Utilities.IndentedTextWriter)">
            <summary>
            Generates SQL to mark a table as a system table.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="createTableOperation"> The table to mark as a system table. </param>
            <param name="writer"> The <see cref="T:System.Data.Entity.Migrations.Utilities.IndentedTextWriter"/> to write the generated SQL to. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.GenerateCreateSchema(System.String)">
            <summary>
            Generates SQL to create a database schema.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="schema"> The name of the schema to create. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddForeignKeyOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.AddForeignKeyOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="addForeignKeyOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropForeignKeyOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.DropForeignKeyOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="dropForeignKeyOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateIndexOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.CreateIndexOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="createIndexOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropIndexOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.DropIndexOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="dropIndexOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="addPrimaryKeyOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="dropPrimaryKeyOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddColumnOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.AddColumnOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="addColumnOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropColumnOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.DropColumnOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="dropColumnOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AlterColumnOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.AlterColumnOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="alterColumnOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropTableOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.DropTableOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="dropTableOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.SqlOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.SqlOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="sqlOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameColumnOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.RenameColumnOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="renameColumnOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameTableOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.RenameTableOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="renameTableOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameProcedureOperation)">
            <summary>
            Generates the specified rename procedure operation.
            </summary>
            <param name="renameProcedureOperation">The rename procedure operation.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.MoveProcedureOperation)">
            <summary>
            Generates the specified move procedure operation.
            </summary>
            <param name="moveProcedureOperation">The move procedure operation.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.MoveTableOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.MoveTableOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="moveTableOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.HistoryOperation)">
            <summary>
            Generates SQL for a <see cref="T:System.Data.Entity.Migrations.Model.HistoryOperation"/>.
            Generated SQL should be added using the Statement method.
            </summary>
            <param name="historyOperation"> The operation to produce SQL for. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Byte[])">
            <summary>
            Generates SQL to specify a constant byte[] default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Boolean)">
            <summary>
            Generates SQL to specify a constant bool default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.DateTime)">
            <summary>
            Generates SQL to specify a constant DateTime default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.DateTimeOffset)">
            <summary>
            Generates SQL to specify a constant DateTimeOffset default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Guid)">
            <summary>
            Generates SQL to specify a constant Guid default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.String)">
            <summary>
            Generates SQL to specify a constant string default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.TimeSpan)">
            <summary>
            Generates SQL to specify a constant TimeSpan default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Spatial.DbGeography)">
            <summary>
            Generates SQL to specify a constant geogrpahy default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Data.Entity.Spatial.DbGeometry)">
            <summary>
            Generates SQL to specify a constant geometry default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Generate(System.Object)">
            <summary>
            Generates SQL to specify a constant default value being set on a column.
            This method just generates the actual value, not the SQL to set the default value.
            </summary>
            <param name="defaultValue"> The value to be set. </param>
            <returns> SQL representing the default value. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.BuildColumnType(System.Data.Entity.Migrations.Model.ColumnModel)">
            <summary>
            Generates SQL to specify the data type of a column.
            This method just generates the actual type, not the SQL to create the column.
            </summary>
            <param name="columnModel"> The definition of the column. </param>
            <returns> SQL representing the data type. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Name(System.String)">
            <summary>
            Generates a quoted name. The supplied name may or may not contain the schema.
            </summary>
            <param name="name"> The name to be quoted. </param>
            <returns> The quoted name. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Quote(System.String)">
            <summary>
            Quotes an identifier for SQL Server.
            </summary>
            <param name="identifier"> The identifier to be quoted. </param>
            <returns> The quoted identifier. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Statement(System.String,System.Boolean,System.String)">
            <summary>
            Adds a new Statement to be executed against the database.
            </summary>
            <param name="sql"> The statement to be executed. </param>
            <param name="suppressTransaction"> Gets or sets a value indicating whether this statement should be performed outside of the transaction scope that is used to make the migration process transactional. If set to true, this operation will not be rolled back if the migration process fails. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Writer">
            <summary>
            Gets a new <see cref="T:System.Data.Entity.Migrations.Utilities.IndentedTextWriter"/> that can be used to build SQL.
            This is just a helper method to create a writer. Writing to the writer will
            not cause SQL to be registered for execution. You must pass the generated
            SQL to the Statement method.
            </summary>
            <returns> An empty text writer to use for SQL generation. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.Statement(System.Data.Entity.Migrations.Utilities.IndentedTextWriter,System.String)">
            <summary>
            Adds a new Statement to be executed against the database.
            </summary>
            <param name="writer"> The writer containing the SQL to be executed. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.ResolveNameConflicts(System.Data.Entity.Migrations.Model.CreateTableOperation)">
            <summary>
            Creates a shallow copy of the source CreateTableOperation and the associated
            AddPrimaryKeyOperation but renames the table and the primary key in order
            to avoid name conflicts with existing objects.
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator.GuidColumnDefault">
            <summary>
            Returns the column default value to use for store-generated GUID columns when
            no default value is explicitly specified in the migration.
            Returns newsequentialid() for on-premises SQL Server 2005 and later.
            Returns newid() for SQL Azure.
            </summary>
            <value>Either newsequentialid() or newid() as described above.</value>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlSpatialFunctions">
            <summary>
            Contains function stubs that expose SqlServer methods in Linq to Entities.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.PointGeography(System.Nullable{System.Double},System.Nullable{System.Double},System.Nullable{System.Int32})">
            <summary>Constructs a geography instance representing a Point instance from its x and y values and a spatial reference ID (SRID). </summary>
            <returns>The constructed geography instance.</returns>
            <param name="latitude">The x-coordinate of the Point being generated.</param>
            <param name="longitude">The y-coordinate of the Point being generated</param>
            <param name="spatialReferenceId">The SRID of the geography instance.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.AsTextZM(System.Data.Entity.Spatial.DbGeography)">
            <summary>Returns the Open Geospatial Consortium (OGC) Well-Known Text (WKT) representation of a geography instance augmented with any Z (elevation) and M (measure) values carried by the instance.</summary>
            <returns>The Open Geospatial Consortium (OGC) Well-Known Text (WKT) representation of a geography instance.</returns>
            <param name="geographyValue">The geography value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.BufferWithTolerance(System.Data.Entity.Spatial.DbGeography,System.Nullable{System.Double},System.Nullable{System.Double},System.Nullable{System.Boolean})">
            <summary>Returns a geometric object representing the union of all point values whose distance from a geography instance is less than or equal to a specified value, allowing for a specified tolerance.</summary>
            <returns>The union of all point values whose distance from a geography instance is less than or equal to a specified value</returns>
            <param name="geographyValue">The geography value.</param>
            <param name="distance">The distance.</param>
            <param name="tolerance">The specified tolerance.</param>
            <param name="relative">Specifying whether the tolerance value is relative or absolute.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.EnvelopeAngle(System.Data.Entity.Spatial.DbGeography)">
            <summary>Returns the maximum angle between the point returned by EnvelopeCenter() and a point in the geography instance in degrees.</summary>
            <returns>the maximum angle between the point returned by EnvelopeCenter().</returns>
            <param name="geographyValue">The geography value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.EnvelopeCenter(System.Data.Entity.Spatial.DbGeography)">
            <summary>Returns a point that can be used as the center of a bounding circle for the geography instance.</summary>
            <returns>A SqlGeography value that specifies the location of the center of a bounding circle.</returns>
            <param name="geographyValue">The geography value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.Filter(System.Data.Entity.Spatial.DbGeography,System.Data.Entity.Spatial.DbGeography)">
            <summary>Offers a fast, index-only intersection method to determine if a geography instance intersects another SqlGeography instance, assuming an index is available.</summary>
            <returns>True if a geography instance potentially intersects another SqlGeography instance; otherwise, false.</returns>
            <param name="geographyValue">The geography value.</param>
            <param name="geographyOther">Another geography instance to compare against the instance on which Filter is invoked.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.InstanceOf(System.Data.Entity.Spatial.DbGeography,System.String)">
            <summary>Tests if the SqlGeography instance is the same as the specified type.</summary>
            <returns>A string that specifies one of the 12 types exposed in the geography type hierarchy.</returns>
            <param name="geographyValue">The geography value.</param>
            <param name="geometryTypeName">A string that specifies one of the 12 types exposed in the geography type hierarchy.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.NumRings(System.Data.Entity.Spatial.DbGeography)">
            <summary>Returns the total number of rings in a Polygon instance.</summary>
            <returns>The total number of rings.</returns>
            <param name="geographyValue">The geography value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.Reduce(System.Data.Entity.Spatial.DbGeography,System.Nullable{System.Double})">
            <summary>Returns an approximation of the given geography instance produced by running the Douglas-Peucker algorithm on the instance with the given tolerance.</summary>
            <returns>
            Returns <see cref="T:System.Data.Entity.Spatial.DbGeography" />.
            </returns>
            <param name="geographyValue">The geography value.</param>
            <param name="tolerance">The tolerance to input to the Douglas-Peucker algorithm. tolerance must be a positive number.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.RingN(System.Data.Entity.Spatial.DbGeography,System.Nullable{System.Int32})">
            <summary>Returns the specified ring of the SqlGeography instance: 1 ≤ n ≤ NumRings().</summary>
            <returns>A SqlGeography object that represents the ring specified by n.</returns>
            <param name="geographyValue">The geography value.</param>
            <param name="index">An int expression between 1 and the number of rings in a polygon instance.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.PointGeometry(System.Nullable{System.Double},System.Nullable{System.Double},System.Nullable{System.Int32})">
            <summary>Constructs a geometry instance representing a Point instance from its x and y values and a spatial reference ID (SRID). </summary>
            <returns>The constructed geometry instance.</returns>
            <param name="xCoordinate">The x-coordinate of the Point being generated.</param>
            <param name="yCoordinate">The y-coordinate of the Point being generated</param>
            <param name="spatialReferenceId">The SRID of the geography instance.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.AsTextZM(System.Data.Entity.Spatial.DbGeometry)">
            <summary>Returns the Open Geospatial Consortium (OGC) Well-Known Text (WKT) representation of a geography instance augmented with any Z (elevation) and M (measure) values carried by the instance.</summary>
            <returns>The Open Geospatial Consortium (OGC) Well-Known Text (WKT) representation of a geometry instance.</returns>
            <param name="geometryValue">The geometry value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.BufferWithTolerance(System.Data.Entity.Spatial.DbGeometry,System.Nullable{System.Double},System.Nullable{System.Double},System.Nullable{System.Boolean})">
            <summary>Returns a geometric object representing the union of all point values whose distance from a geometry instance is less than or equal to a specified value, allowing for a specified tolerance.</summary>
            <returns>The union of all point values whose distance from a geometry instance is less than or equal to a specified value</returns>
            <param name="geometryValue">The geometry value.</param>
            <param name="distance">The distance.</param>
            <param name="tolerance">The specified tolerance.</param>
            <param name="relative">Specifying whether the tolerance value is relative or absolute.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.InstanceOf(System.Data.Entity.Spatial.DbGeometry,System.String)">
            <summary>Tests if the SqlGeometry instance is the same as the specified type.</summary>
            <returns>A string that specifies one of the 12 types exposed in the geography type hierarchy.</returns>
            <param name="geometryValue">The geometry value.</param>
            <param name="geometryTypeName">A string that specifies one of the 12 types exposed in the geography type hierarchy.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.Filter(System.Data.Entity.Spatial.DbGeometry,System.Data.Entity.Spatial.DbGeometry)">
            <summary>Offers a fast, index-only intersection method to determine if a geography instance intersects another SqlGeometry instance, assuming an index is available.</summary>
            <returns>True if a geography instance potentially intersects another SqlGeography instance; otherwise, false.</returns>
            <param name="geometryValue">The geometry value.</param>
            <param name="geometryOther">Another geography instance to compare against the instance on which Filter is invoked.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.MakeValid(System.Data.Entity.Spatial.DbGeometry)">
            <summary>Converts an invalid geometry instance into a geometry instance with a valid Open Geospatial Consortium (OGC) type. </summary>
            <returns>The converted geometry instance.</returns>
            <param name="geometryValue">The geometry value.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialFunctions.Reduce(System.Data.Entity.Spatial.DbGeometry,System.Nullable{System.Double})">
            <summary>Returns an approximation of the given geography instance produced by running the Douglas-Peucker algorithm on the instance with the given tolerance.</summary>
            <returns>
            Returns <see cref="T:System.Data.Entity.Spatial.DbGeometry" />.
            </returns>
            <param name="geometryValue">The geometry value.</param>
            <param name="tolerance">The tolerance to input to the Douglas-Peucker algorithm. tolerance must be a positive number.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlTypesAssemblyLoader.#ctor(System.Data.Entity.SqlServer.SqlTypesAssembly)">
            <summary>
            Used to create an instance of <see cref="T:System.Data.Entity.SqlServer.SqlTypesAssemblyLoader"/> for a specific SQL Types assembly
            such that it can be used for converting EF spatial types backed by one version to those backed by
            the version actually in use in this app domain.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlTypesAssemblyLoader.TryGetSqlTypesAssembly">
            <summary>
            Returns the highest available version of the Microsoft.SqlServer.Types assembly that could be
            located using Assembly.Load; may return <c>null</c> if no version of the assembly could be found.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlVersionUtils">
            <summary>
            This class is a simple utility class that determines the SQL Server version from the
            connection.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlVersionUtils.GetSqlVersion(System.Data.Common.DbConnection)">
            <summary>
            Get the SqlVersion from the connection. Returns one of Sql8, Sql9, Sql10, Sql11
            The passed connection must be open
            </summary>
            <param name="connection"> current sql connection </param>
            <returns> Sql Version for the current connection </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.Utilities.SqlDataReaderWrapper">
            <summary>
            This is a wrapper for <see cref="T:System.Data.SqlClient.SqlDataReader"/> that allows a mock implementation to be used.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.Utilities.DbExpressionExtensions.GetLeafNodes(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Func{System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Collections.Generic.IEnumerable{System.Data.Entity.Core.Common.CommandTrees.DbExpression}})">
            <summary>
            Uses a stack to non-recursively traverse a given tree structure and retrieve the leaf nodes.
            </summary>
            <param name="root"> The node that represents the root of the tree. </param>
            <param name="kind"> Expressions not of this kind are considered leaves. </param>
            <param name="getChildNodes">
            A function that traverses the tree by retrieving the <b>immediate</b> descendants of a (non-leaf) node.
            </param>
            <returns> An enumerable containing the leaf nodes. </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.ISqlFragment">
            <summary>
            Represents the sql fragment for any node in the query tree.
            </summary>
            <remarks>
            The nodes in a query tree produce various kinds of sql
            <list type="bullet">
                <item>A select statement.</item>
                <item>A reference to an extent. (symbol)</item>
                <item>A raw string.</item>
            </list>
            We have this interface to allow for a common return type for the methods
            in the expression visitor <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpressionVisitor`1"/>
            Add the endd of translation, the sql fragments are converted into real strings.
            </remarks>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.ISqlFragment.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            Write the string represented by this fragment into the stream.
            </summary>
            <param name="writer"> The stream that collects the strings. </param>
            <param name="sqlGenerator"> Context information used for renaming. The global lists are used to generated new names without collisions. </param>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.JoinSymbol">
            <summary>
            A Join symbol is a special kind of Symbol.
            It has to carry additional information
            <list type="bullet">
                <item>
                    ColumnList for the list of columns in the select clause if this
                    symbol represents a sql select statement.  This is set by
                    <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)"/>
                    .
                </item>
                <item>ExtentList is the list of extents in the select clause.</item>
                <item>
                    FlattenedExtentList - if the Join has multiple extents flattened at the
                    top level, we need this information to ensure that extent aliases are renamed
                    correctly in
                    <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)"/>
                </item>
                <item>
                    NameToExtent has all the extents in ExtentList as a dictionary.
                    This is used by
                    <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression)"/>
                    to flatten
                    record accesses.
                </item>
                <item>
                    IsNestedJoin - is used to determine whether a JoinSymbol is an
                    ordinary join symbol, or one that has a corresponding SqlSelectStatement.
                </item>
            </list>
            All the lists are set exactly once, and then used for lookups/enumerated.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.Symbol">
            <summary>
            <see cref="T:System.Data.Entity.SqlServer.SqlGen.SymbolTable"/>
            This class represents an extent/nested select statement,
            or a column.
            The important fields are Name, Type and NewName.
            NewName starts off the same as Name, and is then modified as necessary.
            The rest are used by special symbols.
            e.g. NeedsRenaming is used by columns to indicate that a new name must
            be picked for the column in the second phase of translation.
            IsUnnest is used by symbols for a collection expression used as a from clause.
            This allows <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddFromSymbol(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.SqlServer.SqlGen.Symbol,System.Boolean)"/> to add the column list
            after the alias.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.Symbol.columns">
            <summary>
            Used to track the columns originating from this Symbol when it is used
            in as a from extent in a SqlSelectStatement with a Join or as a From Extent
            in a Join Symbol.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.Symbol.outputColumns">
            <summary>
            Used to track the output columns of a SqlSelectStatement it represents
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Symbol.#ctor(System.String,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Collections.Generic.Dictionary{System.String,System.Data.Entity.SqlServer.SqlGen.Symbol},System.Boolean)">
            <summary>
            Use this constructor if the symbol represents a SqlStatement for which the output columns need to be tracked.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Symbol.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            Write this symbol out as a string for sql.  This is just
            the new name of the symbol (which could be the same as the old name).
            We rename columns here if necessary.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.OptionalColumn">
            <summary>
            Represents a column in a select list that should be printed only if it is later used.
            Such columns get added by <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)"/>.
            The SymbolUsageManager associated with the OptionalColumn has the information whether the column
            has been used based on its symbol.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.OptionalColumn.Append(System.Object)">
            <summary>
            Append to the "fragment" representing this column
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.OptionalColumn.WriteSqlIfUsed(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.String)">
            <summary>
            Writes that fragment that represents the optional column
            if the usage manager says it is used.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker">
            <summary>
            The Sql8ConformanceChecker walks a DbExpression tree and determines whether
            it should be rewritten in order to be translated to SQL appropriate for SQL Server 2000.
            The tree should be rewritten if it contains any of the following expressions:
            <list type="bullet">
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression"/>
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression"/>
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>
                </item>
            </list>
            Also, it throws if it determines that the tree can not
            be translated into SQL appropriate for SQL Server 2000.
            This happens if:
            <list type="bullet">
                <item>
                    The tree contains
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbApplyExpression"/>
                </item>
                <item>
                    The tree contains
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbLimitExpression"/>
                    with property Limit of type
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbParameterReferenceExpression"/>
                </item>
                <item>
                    The tree contains
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>
                    with property Count of type
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbParameterReferenceExpression"/>
                </item>
            </list>
            The visitor only checks for expressions for which the support differs between SQL Server 2000 and SQL Server 2005,
            but does not check/throw for expressions that are not supported for both providers.
            Implementation note: In the cases when the visitor encounters an expression that requires rewrite,
            it still needs to walk its structure in case something below it is not supported and needs to throw.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.NeedsRewrite(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            The entry point
            </summary>
            <returns> True if the tree needs to be rewriten, false otherwise </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.#ctor">
            <summary>
            Default Constructor
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)">
            <summary>
            Default handling for DbUnaryExpression-derived classes. Simply visits its argument
            </summary>
            <param name="expr"> The DbUnaryExpression to visit </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitBinaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbBinaryExpression)">
            <summary>
            Default handling for DbBinaryExpression-derived classes. Visits both arguments.
            </summary>
            <param name="expr"> The DbBinaryExpression to visit </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitAggregate(System.Data.Entity.Core.Common.CommandTrees.DbAggregate)">
            <summary>
            Walks the structure
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitExpressionBinding(System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding)">
            <summary>
            DbExpressionBinding handler
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Used as handler for expressions
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitSortClause(System.Data.Entity.Core.Common.CommandTrees.DbSortClause)">
            <summary>
            Used as handler for SortClauses
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitList``1(System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.ListElementHandler{``0},System.Collections.Generic.IList{``0})">
            <summary>
            Helper method for iterating a list
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitAggregateList(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbAggregate})">
            <summary>
            Handing for list of <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding"/>s.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitExpressionBindingList(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding})">
            <summary>
            Handing for list of <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding"/>s.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitExpressionList(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression})">
            <summary>
            Handing for list of <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpression"/>s.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitSortClauseList(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbSortClause})">
            <summary>
            Handling for list of <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSortClause"/>s.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Called when an <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpression"/> of an otherwise unrecognized type is encountered.
            </summary>
            <param name="expression"> The expression </param>
            <exception cref="T:System.NotSupportedException">
            Always thrown if this method is called, since it indicates that
            <paramref name="expression"/>
            is of an unsupported type
            </exception>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbAndExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitBinaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbBinaryExpression)"/>
            </summary>
            <param name="expression"> The DbAndExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbApplyExpression)">
            <summary>
            Not supported on SQL Server 2000.
            </summary>
            <param name="expression"> The DbApplyExpression that is being visited. </param>
            <exception cref="T:System.NotSupportedException">Always</exception>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbArithmeticExpression)">
            <summary>
            Default handling for DbArithmeticExpression. Visits all arguments.
            </summary>
            <param name="expression"> The DbArithmeticExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbCaseExpression)">
            <summary>
            Walks the strucutre
            </summary>
            <param name="expression"> The DbCaseExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbCastExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbCastExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbComparisonExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitBinaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbBinaryExpression)"/>
            </summary>
            <param name="expression"> The DbComparisonExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression)">
            <summary>
            Returns false
            </summary>
            <param name="expression"> The DbConstantExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbCrossJoinExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbCrossJoinExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbDerefExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DeRefExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbDistinctExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbDistinctExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbElementExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbElementExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbEntityRefExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbEntityRefExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression)">
            <summary>
            Returns true, the tree needs to be rewritten.
            </summary>
            <param name="expression"> The DbExceptExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbFilterExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Visits the arguments
            </summary>
            <param name="expression"> The DbFunctionExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbLambdaExpression)">
            <summary>
            Visits the arguments and lambda body
            </summary>
            <param name="expression"> The DbLambdaExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbGroupByExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression)">
            <summary>
            Returns true.
            </summary>
            <param name="expression"> The DbIntersectExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbIsEmptyExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbIsNullExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsOfExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbIsOfExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbJoinExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbJoinExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbLikeExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbLikeExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbLimitExpression)">
            <summary>
            Walks the structure
            </summary>
            <exception cref="T:System.NotSupportedException">expression.Limit is DbParameterReferenceExpression</exception>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNewInstanceExpression)">
            <summary>
            Walks the arguments
            </summary>
            <param name="expression"> The DbNewInstanceExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNotExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbNotExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNullExpression)">
            <summary>
            Returns false
            </summary>
            <param name="expression"> The DbNullExpression that is being visited. </param>
            <returns> false </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbOfTypeExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbOfTypeExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbOrExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitBinaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbBinaryExpression)"/>
            </summary>
            <param name="expression"> The DbOrExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbInExpression)">
            <summary>
            <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbInExpression"/>
            </summary>
            <param name="expression"> The DbInExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbParameterReferenceExpression)">
            <summary>
            Returns false
            </summary>
            <param name="expression"> The DbParameterReferenceExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbProjectExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression)">
            <summary>
            Returns false
            </summary>
            <param name="expression"> The DbPropertyExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbQuantifierExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbQuantifierExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbRefExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbRefExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbRefKeyExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbRefKeyExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbRelationshipNavigationExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbRelationshipNavigationExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbScanExpression)">
            <summary>
            Returns false;
            </summary>
            <param name="expression"> The DbScanExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression)">
            <summary>
            Resturns true
            </summary>
            <exception cref="T:System.NotSupportedException">expression.Count is DbParameterReferenceExpression</exception>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbSortExpression)">
            <summary>
            Walks the structure
            </summary>
            <param name="expression"> The DbSortExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbTreatExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitUnaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbUnaryExpression)"/>
            </summary>
            <param name="expression"> The DbTreatExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbUnionAllExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitBinaryExpression(System.Data.Entity.Core.Common.CommandTrees.DbBinaryExpression)"/>
            </summary>
            <param name="expression"> The DbUnionAllExpression that is being visited. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.Visit(System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression)">
            <summary>
            Returns false
            </summary>
            <param name="expression"> The DbVariableReferenceExpression that is being visited. </param>
            <returns> false </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.ListElementHandler`1">
            <summary>
            Used for <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.VisitList``1(System.Data.Entity.SqlServer.SqlGen.Sql8ConformanceChecker.ListElementHandler{``0},System.Collections.Generic.IList{``0})"/>
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter">
            <summary>
            Rewrites an expression tree to make it suitable for translation to SQL appropriate for SQL Server 2000
            In particular, it replaces expressions that are not directly supported on SQL Server 2000
            with alternative translations. The following expressions are translated:
            <list type="bullet">
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression"/>
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression"/>
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>
                </item>
            </list>
            The other expressions are copied unmodified.
            The new expression belongs to a new query command tree.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.Rewrite(System.Data.Entity.Core.Common.CommandTrees.DbQueryCommandTree)">
            <summary>
            The only entry point.
            Rewrites the given tree by replacing expressions that are not directly supported on SQL Server 2000
            with alterntive translations.
            </summary>
            <param name="originalTree"> The tree to rewrite </param>
            <returns> The new tree </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.#ctor(System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace)">
            <summary>
            Private Constructor.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.Visit(System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.Visit(System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)"/>
            Logicaly, <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/> translates to:
            SELECT Y.x1, Y.x2, ..., Y.xn
            FROM (
            SELECT X.x1, X.x2, ..., X.xn,
            FROM input AS X
            EXCEPT
            SELECT TOP(count) Z.x1, Z.x2, ..., Z.xn
            FROM input AS Z
            ORDER BY sk1, sk2, ...
            ) AS Y
            ORDER BY sk1, sk2, ...
            Here, input refers to the input of the <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>, and count to the count property of the
            <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>
            .
            The implementation of EXCEPT is non-duplicate eliminating, and does equality comparison only over the
            equality comparable columns of the input.
            This corresponds to the following expression tree:
            SORT
            |
            NON-DISTINCT EXCEPT  (specially translated,
            |
            | - Left:  clone of input
            | - Right:
            |
            Limit
            |
            | - Limit: Count
            | - Input
            |
            Sort
            |
            input
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind)">
            <summary>
            This method is invoked when tranforming <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression"/> and <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression"/> by doing comparison over all input columns.
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)">
            <summary>
            This method is used for translating <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression"/> and <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression"/>,
            and for translating the "Except" part of <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>.
            into the follwoing expression:
            A INTERSECT B, A EXCEPT B
            (DISTINCT)
            |
            FILTER
            |
            | - Input: A
            | - Predicate:(NOT)
            |
            ANY
            |
            | - Input: B
            | - Predicate:  (B.b1 = A.a1 or (B.b1 is null and A.a1 is null))
            AND (B.b2 = A.a2 or (B.b2 is null and A.a2 is null))
            AND ...
            AND (B.bn = A.an or (B.bn is null and A.an is null)))
            Here, A corresponds to right and B to left.
            (NOT) is present when transforming Except
            for the purpose of translating <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression"/> or <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>.
            (DISTINCT) is present when transforming for the purpose of translating
            <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression"/> or <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression"/>.
            For <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression"/>, the input to ANY is caped with project which projects out only
            the columns represented in the sortExpressionsOverLeft list and only these are used in the predicate.
            This is because we want to support skip over input with non-equal comarable columns and we have no way to recognize these.
            </summary>
            <param name="left"> </param>
            <param name="right"> </param>
            <param name="expressionKind"> </param>
            <param name="sortExpressionsOverLeft"> note that this list gets destroyed by this method </param>
            <param name="sortExpressionsBindingVariableName"> </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.FlattenProperties(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression})">
            <summary>
            Adds the flattened properties on the input to the flattenedProperties list.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.RemoveNonSortProperties(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String,System.String)">
            <summary>
            Helper method for
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)"/>
            Removes all pairs of property expressions from list1 and list2, for which the property expression in list1
            does not have a 'matching' property expression in list2.
            The lists list1 and list2 are known to not create duplicate, and the purpose of the sortList is just for this method.
            Thus, to optimize the match process, we remove the seen property expressions from the sort list in
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.HasMatchInList(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String,System.String)"/>
            when iterating both list simultaneously.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.HasMatchInList(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String,System.String)">
            <summary>
            Helper method for <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.RemoveNonSortProperties(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String,System.String)"/>
            Checks whether expr has a 'match' in the given list of property expressions.
            If it does, the matching expression is removed form the list, to speed up future matching.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.AreMatching(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression,System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression,System.String,System.String)">
            <summary>
            Determines whether two expressions match.
            They match if they are  of the shape
            expr1 -> DbPropertyExpression(... (DbPropertyExpression(DbVariableReferenceExpression(expr1BindingVariableName), nameX), ..., name1)
            expr1 -> DbPropertyExpression(... (DbPropertyExpression(DbVariableReferenceExpression(expr2BindingVariableName), nameX), ..., name1),
            i.e. if they only differ in the name of the binding.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.CapWithProject(System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression})">
            <summary>
            Helper method for
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.Sql8ExpressionRewriter.TransformIntersectOrExcept(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression},System.String)"/>
            Creates a
            <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression"/>
            over the given inputBinding that projects out the given flattenedProperties.
            and updates the flattenedProperties to be over the newly created project.
            </summary>
            <returns>
            An <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding"/> over the newly created <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression"/>
            </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder">
            <summary>
            This class is like StringBuilder.  While traversing the tree for the first time,
            we do not know all the strings that need to be appended e.g. things that need to be
            renamed, nested select statements etc.  So, we use a builder that can collect
            all kinds of sql fragments.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlBuilder.Append(System.Object)">
            <summary>
            Add an object to the list - we do not verify that it is a proper sql fragment
            since this is an internal method.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlBuilder.AppendLine">
            <summary>
            This is to pretty print the SQL.  The writer <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlWriter"/>
            needs to know about new lines so that it can add the right amount of
            indentation at the beginning of lines.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlBuilder.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            We delegate the writing of the fragment to the appropriate type.
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlBuilder.IsEmpty">
            <summary>
            Whether the builder is empty.  This is used by the <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression)"/>
            to determine whether a sql statement can be reused.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler">
            <summary>
            Enacapsulates the logic required to translate function calls represented as instances of DbFunctionExpression into SQL.
            There are several special cases that modify how the translation should proceed. These include:
            - 'Special' canonical functions, for which the function name or arguments differ between the EDM canonical function and the SQL function
            - 'Special' server functions, which are similar to the 'special' canonical functions but sourced by the SQL Server provider manifest
            - Niladic functions, which require the parentheses that would usually follow the function name to be omitted
            - Spatial canonical functions, which must translate to a static method call, instance method call, or instance property access against
            one of the built-in spatial CLR UDTs (geography/geometry).
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeStoreFunctionHandlers">
            <summary>
            All special store functions and their handlers
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeCanonicalFunctionHandlers">
            <summary>
            All special non-aggregate canonical functions and their handlers
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeFunctionNameToOperatorDictionary">
            <summary>
            Initalizes the mapping from functions to TSql operators
            for all functions that translate to TSql operators
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeDateAddFunctionNameToDatepartDictionary">
            <summary>
            Initalizes the mapping from names of canonical function for date/time addition
            to corresponding dateparts
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeDateDiffFunctionNameToDatepartDictionary">
            <summary>
            Initalizes the mapping from names of canonical function for date/time difference
            to corresponding dateparts
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeGeographyStaticMethodFunctionsDictionary">
            <summary>
            Initalizes the mapping from names of canonical function that represent static geography methods to their corresponding
            static method name, qualified with the 'geography::' prefix.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeGeographyInstancePropertyFunctionsDictionary">
            <summary>
            Initalizes the mapping from names of canonical function that represent geography instance properties to their corresponding
            store property name.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeRenamedGeographyInstanceMethodFunctions">
            <summary>
            Initalizes the mapping of canonical function name to instance method name for geography instance functions that differ in name from the sql server equivalent.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeGeometryStaticMethodFunctionsDictionary">
            <summary>
            Initalizes the mapping from names of canonical function that represent static geometry methods to their corresponding
            static method name, qualified with the 'geometry::' prefix.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeGeometryInstancePropertyFunctionsDictionary">
            <summary>
            Initalizes the mapping from names of canonical function that represent geometry instance properties to their corresponding
            store property name.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.InitializeRenamedGeometryInstanceMethodFunctions">
            <summary>
            Initalizes the mapping of canonical function name to instance method name for geometry instance functions that differ in name from the sql server equivalent.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.IsSpecialStoreFunction(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Determines whether the given function is a store function that
            requires special handling
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.IsSpecialCanonicalFunction(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Determines whether the given function is a canonical function that
            requires special handling
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.IsSpatialCanonicalFunction(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Data.Entity.Core.Metadata.Edm.PrimitiveTypeKind@)">
            <summary>
            Determines whether the given function is a canonical function the translates
            to a spatial (geography/geometry) property access or method call.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleFunctionDefault(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Default handling for functions.
            Translates them to FunctionName(arg1, arg2, ..., argn)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleFunctionDefaultGivenName(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.String)">
            <summary>
            Default handling for functions with a given name.
            Translates them to FunctionName(arg1, arg2, ..., argn)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleFunctionDefaultCastReturnValue(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.String,System.String)">
            <summary>
            Default handling for functions with a given name and given return value cast.
            Translates them to CAST(FunctionName(arg1, arg2, ..., argn) AS returnType)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleFunctionArgumentsDefault(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Data.Entity.SqlServer.SqlGen.SqlBuilder)">
            <summary>
            Default handling on function arguments.
            Appends the list of arguments to the given result
            If the function is niladic it does not append anything,
            otherwise it appends (arg1, arg2, .., argn)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleFunctionGivenNameBasedOnVersion(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.String,System.String)">
            <summary>
            Handler for functions that need to be translated to different store function based on version
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleSpecialStoreFunction(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for special build in functions
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleSpecialCanonicalFunction(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for special canonical functions
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleSpecialFunction(System.Collections.Generic.Dictionary{System.String,System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.FunctionHandler},System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Dispatches the special function processing to the appropriate handler
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleSpecialFunctionToOperator(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Boolean)">
            <summary>
            Handles functions that are translated into TSQL operators.
            The given function should have one or two arguments.
            Functions with one arguemnt are translated into
            op arg
            Functions with two arguments are translated into
            arg0 op arg1
            Also, the arguments can be optionaly enclosed in parethesis
            </summary>
            <param name="sqlgen"> </param>
            <param name="e"> </param>
            <param name="parenthesiseArguments"> Whether the arguments should be enclosed in parethesis </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleConcatFunction(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleSpecialFunctionToOperator(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Boolean)"></see>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionBitwise(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleSpecialFunctionToOperator(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Boolean)"></see>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleDatepartDateFunction(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handles special case in which datapart 'type' parameter is present. all the functions
            handles here have *only* the 1st parameter as datepart. datepart value is passed along
            the QP as string and has to be expanded as TSQL keyword.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDatepart(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for canonical functions for extracting date parts.
            For example:
            Year(date) -> DATEPART( year, date)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionGetTotalOffsetMinutes(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for canonical funcitons for GetTotalOffsetMinutes.
            GetTotalOffsetMinutes(e) --> Datepart(tzoffset, e)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDatepart(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.String,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for turning a canonical function into DATEPART
            Results in DATEPART(datepart, e)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionCurrentDateTime(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for the canonical function CurrentDateTime
            For Sql8 and Sql9:  CurrentDateTime() -> GetDate()
            For Sql10:          CurrentDateTime() -> SysDateTime()
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionCurrentUtcDateTime(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for the canonical function CurrentUtcDateTime
            For Sql8 and Sql9:  CurrentUtcDateTime() -> GetUtcDate()
            For Sql10:          CurrentUtcDateTime() -> SysUtcDateTime()
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionCurrentDateTimeOffset(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for the canonical function CurrentDateTimeOffset
            For Sql8 and Sql9:  throw
            For Sql10: CurrentDateTimeOffset() -> SysDateTimeOffset()
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionCreateDateTime(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            See <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateTimeTypeCreation(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.String,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Boolean,System.Boolean)"/> for exact translation
            Pre Katmai creates datetime.
            On Katmai creates datetime2.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionCreateDateTimeOffset(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            See <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateTimeTypeCreation(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.String,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Boolean,System.Boolean)"/> for exact translation
            Pre Katmai not supported.
            On Katmai creates datetimeoffset.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionCreateTime(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            See <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateTimeTypeCreation(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.String,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Boolean,System.Boolean)"/> for exact translation
            Pre Katmai not supported.
            On Katmai creates time.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateTimeTypeCreation(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.String,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Boolean,System.Boolean)">
            <summary>
            Helper for all date and time types creating functions.
            The given expression is in general trainslated into:
            CONVERT(@typename, [datePart] + [timePart] + [timeZonePart], 121), where the datePart and the timeZonePart are optional
            The individual parts are translated as:
            Date part:
            convert(varchar(255), @year) + '-' + convert(varchar(255), @month) + '-' + convert(varchar(255), @day)
            Time part:
            PRE KATMAI:  convert(varchar(255), @hour)+ ':' + convert(varchar(255), @minute)+ ':' + str(@second, 6, 3)
            KATMAI:  convert(varchar(255), @hour)+ ':' + convert(varchar(255), @minute)+ ':' + str(@second, 10, 7)
            Time zone part:
            (case when @tzoffset >= 0 then '+' else '-' end) + convert(varchar(255), ABS(@tzoffset)/60) + ':' + convert(varchar(255), ABS(@tzoffset)%60)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.AppendConvertToVarchar(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.SqlServer.SqlGen.SqlBuilder,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Helper method that wrapps the given expession with a conver to varchar(255)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionTruncateTime(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            TruncateTime(DateTime X)
            PreKatmai:    TRUNCATETIME(X) => CONVERT(DATETIME, CONVERT(VARCHAR(255), expression, 102),  102)
            Katmai:    TRUNCATETIME(X) => CONVERT(DATETIME2, CONVERT(VARCHAR(255), expression, 102),  102)
            TruncateTime(DateTimeOffset X)
            TRUNCATETIME(X) => CONVERT(datetimeoffset, CONVERT(VARCHAR(255), expression,  102)
            + ' 00:00:00 ' +  Right(convert(varchar(255), @arg, 121), 6),  102)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateAddKatmaiOrNewer(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for date addition functions supported only starting from Katmai
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateAdd(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for all date/time addition canonical functions.
            Translation, e.g.
            AddYears(datetime, number) =>  DATEADD(year, number, datetime)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateDiffKatmaiOrNewer(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Hanndler for date differencing functions supported only starting from Katmai
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionDateDiff(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for all date/time addition canonical functions.
            Translation, e.g.
            DiffYears(datetime, number) =>  DATEDIFF(year, number, datetime)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionIndexOf(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Function rename IndexOf -> CHARINDEX
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionNewGuid(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Function rename NewGuid -> NEWID
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionLength(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Function rename Length -> LEN
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionRound(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Round(numericExpression) -> Round(numericExpression, 0);
            Round(numericExpression, digits) -> Round(numericExpression, digits);
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionTruncate(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Truncate(numericExpression) -> Round(numericExpression, 0, 1); (does not exist as canonical function yet)
            Truncate(numericExpression, digits) -> Round(numericExpression, digits, 1);
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionRoundOrTruncate(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Boolean)">
            <summary>
            Common handler for the canonical functions ROUND and TRUNCATE
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionAbs(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handle the canonical function Abs().
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionTrim(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            TRIM(string) -> LTRIM(RTRIM(string))
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionToLower(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Function rename ToLower -> LOWER
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionToUpper(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Function rename ToUpper -> UPPER
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.TranslateConstantParameterForLike(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression,System.Data.Entity.SqlServer.SqlGen.SqlBuilder,System.Boolean,System.Boolean)">
            <summary>
            Function to translate the StartsWith, EndsWith and Contains canonical functions to LIKE expression in T-SQL
            and also add the trailing ESCAPE '~' when escaping of the search string for the LIKE expression has occurred
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionContains(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for Contains. Wraps the normal translation with a case statement
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionContains(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Data.Entity.SqlServer.SqlGen.SqlBuilder)">
            <summary>
            CONTAINS(arg0, arg1) => arg0 LIKE '%arg1%'
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionStartsWith(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for StartsWith. Wraps the normal translation with a case statement
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionStartsWith(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Data.Entity.SqlServer.SqlGen.SqlBuilder)">
            <summary>
            STARTSWITH(arg0, arg1) => arg0 LIKE 'arg1%'
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionEndsWith(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Handler for EndsWith. Wraps the normal translation with a case statement
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.HandleCanonicalFunctionEndsWith(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Data.Entity.SqlServer.SqlGen.SqlBuilder)">
            <summary>
            ENDSWITH(arg0, arg1) => arg0 LIKE '%arg1'
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.WrapPredicate(System.Func{System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Data.Entity.SqlServer.SqlGen.SqlBuilder,System.Data.Entity.SqlServer.SqlGen.SqlBuilder},System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Turns a predicate into a statement returning a bit
            PREDICATE => CASE WHEN (PREDICATE) THEN CAST(1 AS BIT) WHEN (NOT (PREDICATE)) CAST (O AS BIT) END
            The predicate is produced by the given predicateTranslator.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.WriteFunctionName(System.Data.Entity.SqlServer.SqlGen.SqlBuilder,System.Data.Entity.Core.Metadata.Edm.EdmFunction)">
            <summary>
            Writes the function name to the given SqlBuilder.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.IsStoreFunction(System.Data.Entity.Core.Metadata.Edm.EdmFunction)">
            <summary>
            Is this a Store function (ie) does it have the builtinAttribute specified and it is not a canonical function?
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.CastReturnTypeToInt64(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            determines if the function requires the return type be enforeced by use of a cast expression
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.CastReturnTypeToInt32(System.Data.Entity.SqlServer.SqlGen.SqlGenerator,System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            determines if the function requires the return type be enforeced by use of a cast expression
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.CastReturnTypeToInt16(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            determines if the function requires the return type be enforeced by use of a cast expression
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.CastReturnTypeToSingle(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            determines if the function requires the return type be enforeced by use of a cast expression
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlFunctionCallHandler.CastReturnTypeToGivenType(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression,System.Collections.Generic.ISet{System.String},System.Data.Entity.Core.Metadata.Edm.PrimitiveTypeKind)">
            <summary>
            Determines if the function requires the return type be enforced by use of a cast expression
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlGenerator">
            <summary>
            Translates the command object into a SQL string that can be executed on
            SQL Server 2000 and SQL Server 2005.
            </summary>
            <remarks>
            The translation is implemented as a visitor <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbExpressionVisitor`1"/>
            over the query tree.  It makes a single pass over the tree, collecting the sql
            fragments for the various nodes in the tree <see cref="T:System.Data.Entity.SqlServer.SqlGen.ISqlFragment"/>.
            The major operations are
            <list type="bullet">
                <item>
                    Select statement minimization.  Multiple nodes in the query tree
                    that can be part of a single SQL select statement are merged. e.g. a
                    Filter node that is the input of a Project node can typically share the
                    same SQL statement.
                </item>
                <item>
                    Alpha-renaming.  As a result of the statement minimization above, there
                    could be name collisions when using correlated subqueries
                    <example>
                        <code>Filter(
                            b = Project( c.x
                            c = Extent(foo)
                            )
                            exists (
                            Filter(
                            c = Extent(foo)
                            b.x = c.x
                            )
                            )
                            )</code>
                        The first Filter, Project and Extent will share the same SQL select statement.
                        The alias for the Project i.e. b, will be replaced with c.
                        If the alias c for the Filter within the exists clause is not renamed,
                        we will get <c>c.x = c.x</c>, which is incorrect.
                        Instead, the alias c within the second filter should be renamed to c1, to give
                        <c>c.x = c1.x</c> i.e. b is renamed to c, and c is renamed to c1.
                    </example>
                </item>
                <item>
                    Join flattening.  In the query tree, a list of join nodes is typically
                    represented as a tree of Join nodes, each with 2 children. e.g.
                    <example>
                        <code>a = Join(InnerJoin
                            b = Join(CrossJoin
                            c = Extent(foo)
                            d = Extent(foo)
                            )
                            e = Extent(foo)
                            on b.c.x = e.x
                            )</code>
                        If translated directly, this will be translated to
                        <code>FROM ( SELECT c.*, d.*
                            FROM foo as c
                            CROSS JOIN foo as d) as b
                            INNER JOIN foo as e on b.x' = e.x</code>
                        It would be better to translate this as
                        <code>FROM foo as c
                            CROSS JOIN foo as d
                            INNER JOIN foo as e on c.x = e.x</code>
                        This allows the optimizer to choose an appropriate join ordering for evaluation.
                    </example>
                </item>
                <item>
                    Select * and column renaming.  In the example above, we noticed that
                    in some cases we add
                    <c>SELECT * FROM ...</c>
                    to complete the SQL
                    statement. i.e. there is no explicit PROJECT list.
                    In this case, we enumerate all the columns available in the FROM clause
                    This is particularly problematic in the case of Join trees, since the columns
                    from the extents joined might have the same name - this is illegal.  To solve
                    this problem, we will have to rename columns if they are part of a SELECT *
                    for a JOIN node - we do not need renaming in any other situation.
                    <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)"/>
                    .
                </item>
            </list>
            <para> Renaming issues. When rows or columns are renamed, we produce names that are unique globally with respect to the query. The names are derived from the original names, with an integer as a suffix. e.g. CustomerId will be renamed to CustomerId1, CustomerId2 etc. Since the names generated are globally unique, they will not conflict when the columns of a JOIN SELECT statement are joined with another JOIN. </para>
            <para>
                Record flattening. SQL server does not have the concept of records. However, a join statement produces records. We have to flatten the record accesses into a simple <c>alias.column</c> form.
                <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression)"/>
            </para>
            <para>
                Building the SQL. There are 2 phases
                <list type="numbered">
                    <item>
                        Traverse the tree, producing a sql builder
                        <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
                    </item>
                    <item>
                        Write the SqlBuilder into a string, renaming the aliases and columns
                        as needed.
                    </item>
                </list>
                In the first phase, we traverse the tree. We cannot generate the SQL string right away, since
                <list type="bullet">
                    <item>The WHERE clause has to be visited before the from clause.</item>
                    <item>
                        extent aliases and column aliases need to be renamed.  To minimize
                        renaming collisions, all the names used must be known, before any renaming
                        choice is made.
                    </item>
                </list>
                To defer the renaming choices, we use symbols
                <see cref="T:System.Data.Entity.SqlServer.SqlGen.Symbol"/>
                . These are renamed in the second phase. Since visitor methods cannot transfer information to child nodes through parameters, we use some global stacks,
                <list type="bullet">
                    <item>
                        A stack for the current SQL select statement.  This is needed by
                        <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression)"/>
                        to create a
                        list of free variables used by a select statement.  This is needed for
                        alias renaming.
                    </item>
                    <item>
                        A stack for the join context.  When visiting an extent,
                        we need to know whether we are inside a join or not.  If we are inside
                        a join, we do not create a new SELECT statement.
                    </item>
                </list>
            </para>
            <para>
                Global state. To enable renaming, we maintain
                <list type="bullet">
                    <item>The set of all extent aliases used.</item>
                    <item>The set of all parameter names.</item>
                    <item>The set of all column names that may need to be renamed.</item>
                </list>
                Finally, we have a symbol table to lookup variable references. All references to the same extent have the same symbol.
            </para>
            <para>
                Sql select statement sharing. Each of the relational operator nodes
                <list type="bullet">
                    <item>Project</item>
                    <item>Filter</item>
                    <item>GroupBy</item>
                    <item>Sort/OrderBy</item>
                </list>
                can add its non-input (e.g. project, predicate, sort order etc.) to the SQL statement for the input, or create a new SQL statement. If it chooses to reuse the input's SQL statement, we play the following symbol table trick to accomplish renaming. The symbol table entry for the alias of the current node points to the symbol for the input in the input's SQL statement.
                <example>
                    <code>Project(b.x
                        b = Filter(
                        c = Extent(foo)
                        c.x = 5)
                        )</code>
                    The Extent node creates a new SqlSelectStatement.  This is added to the
                    symbol table by the Filter as {c, Symbol(c)}.  Thus, <c>c.x</c> is resolved to
                    <c>Symbol(c).x</c>.
                    Looking at the project node, we add {b, Symbol(c)} to the symbol table if the
                    SQL statement is reused, and {b, Symbol(b)}, if there is no reuse.
                    Thus, <c>b.x</c> is resolved to <c>Symbol(c).x</c> if there is reuse, and to
                    <c>Symbol(b).x</c> if there is no reuse.
                </example>
            </para>
            </remarks>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.selectStatementStack">
            <summary>
            Every relational node has to pass its SELECT statement to its children
            This allows them (DbVariableReferenceExpression eventually) to update the list of
            outer extents (free variables) used by this select statement.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.isParentAJoinStack">
            <summary>
            Nested joins and extents need to know whether they should create
            a new Select statement, or reuse the parent's.  This flag
            indicates whether the parent is a join or not.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.isVarRefSingle">
            <summary>
            VariableReferenceExpressions are allowed only as children of DbPropertyExpression
            or MethodExpression.  The cheapest way to ensure this is to set the following
            property in DbVariableReferenceExpression and reset it in the allowed parent expressions.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator._candidateParametersToForceNonUnicode">
            <summary>
            Maintain the list of (string) DbParameterReferenceExpressions that should be compensated, viz.
            forced to non-unicode format. A parameter is added to the list if it is being compared to a
            non-unicode store column and none of its other usages in the query tree, disqualify it
            (For example - if the parameter is also being projected or compared to a unicode column)
            The goal of the compensation is to have the store index picked up by the server.
            String constants are also compensated and the decision is local, unlike parameters.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator._forceNonUnicode">
            <summary>
            Set and reset in DbComparisonExpression and DbLikeExpression visit methods. Maintains
            global state information that the children of these nodes are candidates for compensation.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator._ignoreForceNonUnicodeFlag">
            <summary>
            Set when it is is safe to ignore the unicode/non-unicode aspect. See <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitIsNullExpression(System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression,System.Boolean)"/> for an example.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlGen.SqlGenerator._sqlVersion">
            <summary>
            The current SQL Server version
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.#ctor(System.Data.Entity.SqlServer.SqlVersion)">
            <summary>
            Basic constructor.
            </summary>
            <param name="sqlVersion"> server version </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GenerateSql(System.Data.Entity.Core.Common.CommandTrees.DbCommandTree,System.Data.Entity.SqlServer.SqlVersion,System.Collections.Generic.List{System.Data.SqlClient.SqlParameter}@,System.Data.CommandType@,System.Collections.Generic.HashSet{System.String}@)">
            <summary>
            General purpose static function that can be called from System.Data assembly
            </summary>
            <param name="tree"> command tree </param>
            <param name="sqlVersion"> Server version </param>
            <param name="parameters"> Parameters to add to the command tree corresponding to constants in the command tree. Used only in ModificationCommandTrees. </param>
            <param name="commandType"> CommandType for generated command. </param>
            <param name="paramsToForceNonUnicode"> </param>
            <returns> The string representing the SQL to be executed. </returns>\
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GenerateSql(System.Data.Entity.Core.Common.CommandTrees.DbQueryCommandTree,System.Collections.Generic.HashSet{System.String}@)">
            <summary>
            Translate a command tree to a SQL string.
            The input tree could be translated to either a SQL SELECT statement
            or a SELECT expression.  This choice is made based on the return type
            of the expression
            CollectionType => select statement
            non collection type => select expression
            </summary>
            <returns> The string representing the SQL to be executed. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.ISqlFragment)">
            <summary>
            Convert the SQL fragments to a string. Writes a string representing the SQL to be executed
            into the specified writer.
            </summary>
            <param name="writer"> </param>
            <param name="sqlStatement">The fragment to be emitted</param>
            <returns>The writer specified for fluent continuations. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbAndExpression)">
            <summary>
            Translate(left) AND Translate(right)
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> .
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbApplyExpression)">
            <summary>
            An apply is just like a join, so it shares the common join processing
            in <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitJoinExpression(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding},System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpression)"/>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/> .
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbArithmeticExpression)">
            <summary>
            For binary expressions, we delegate to <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitBinaryExpression(System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression)"/>.
            We handle the other expressions directly.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbCaseExpression)">
            <summary>
            If the ELSE clause is null, we do not write it out.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbComparisonExpression)">
            <summary>
            The parser generates Not(Equals(...)) for &lt;&gt;.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> .
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.CheckIfForceNonUnicodeRequired(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Checks if the arguments of the input Comparison or Like expression are candidates
            for compensation. If yes, sets global state variable - _forceNonUnicode.
            </summary>
            <param name="e"> DBComparisonExpression or DbLikeExpression </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.MatchPatternForForcingNonUnicode(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            The grammar for the pattern that we are looking for is -
            Pattern := Target OP Source | Source OP Target
            OP := Like | In | Comparison
            Source := Non-unicode DbPropertyExpression
            Target := Target FUNC Target | DbConstantExpression | DBParameterExpression
            FUNC := CONCAT | RTRIM | LTRIM | TRIM | SUBSTRING | TOLOWER | TOUPPER | REVERSE | REPLACE
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.MatchTargetPatternForForcingNonUnicode(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Matches the non-terminal symbol "target" in above grammar.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.MatchSourcePatternForForcingNonUnicode(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Determines if the expression represents a non-unicode string column(char/varchar store type)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsConstParamOrNullExpressionUnicodeNotSpecified(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Determines if the expression represents a string constant or parameter with the facet, unicode=null.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitConstant(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression,System.Boolean)">
            <summary>
            Generate tsql for a constant. Avoid the explicit cast (if possible) when
            the isCastOptional parameter is set
            </summary>
            <param name="e"> the constant expression </param>
            <param name="isCastOptional"> can we avoid the CAST </param>
            <returns> the tsql fragment </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AssertValidDouble(System.Double)">
            <summary>
            Helper method for <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitConstant(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression,System.Boolean)"/>
            </summary>
            <param name="value"> A double value </param>
            <exception cref="T:System.NotSupportedException">
            If a value of positive or negative infinity, or
            <see cref="F:System.Double.NaN"/>
            is specified
            </exception>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AssertValidSingle(System.Single)">
            <summary>
            Helper method for <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitConstant(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression,System.Boolean)"/>
            </summary>
            <param name="value"> A single value </param>
            <exception cref="T:System.NotSupportedException">
            If a value of positive or negative infinity, or
            <see cref="F:System.Single.NaN"/>
            is specified
            </exception>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.WrapWithCastIfNeeded(System.Boolean,System.String,System.String,System.Data.Entity.SqlServer.SqlGen.SqlBuilder)">
            <summary>
            Helper function for <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitConstant(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression,System.Boolean)"/>
            Appends the given constant value to the result either 'as is' or wrapped with a cast to the given type.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression)">
            <summary>
            We do not pass constants as parameters.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> . Strings are wrapped in single quotes and escaped. Numbers are written literally.
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbDistinctExpression)">
            <summary>
            The DISTINCT has to be added to the beginning of SqlSelectStatement.Select,
            but it might be too late for that.  So, we use a flag on SqlSelectStatement
            instead, and add the "DISTINCT" in the second phase.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbElementExpression)">
            <summary>
            An element expression returns a scalar - so it is translated to
            ( Select ... )
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbUnionAllExpression)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Only concrete expression types will be visited.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbScanExpression)">
            <returns>
            If we are in a Join context, returns a <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> with the extent name, otherwise, a new
            <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            with the From field set.
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GetTargetTSql(System.Data.Entity.Core.Metadata.Edm.EntitySetBase)">
            <summary>
            Gets escaped TSql identifier describing this entity set.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)">
            <summary>
            The bodies of <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/>, <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbGroupByExpression)"/>,
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression)"/>, <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbSortExpression)"/> are similar.
            Each does the following.
            <list type="number">
                <item>Visit the input expression</item>
                <item>
                    Determine if the input's SQL statement can be reused, or a new
                    one must be created.
                </item>
                <item>Create a new symbol table scope</item>
                <item>
                    Push the Sql statement onto a stack, so that children can
                    update the free variable list.
                </item>
                <item>Visit the non-input expression.</item>
                <item>Cleanup</item>
            </list>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Lambda functions are not supported.
            The functions supported are:
            <list type="number">
                <item>Canonical Functions - We recognize these by their dataspace, it is DataSpace.CSpace</item>
                <item>Store Functions - We recognize these by the BuiltInAttribute and not being Canonical</item>
                <item>User-defined Functions - All the rest</item>
            </list>
            We handle Canonical and Store functions the same way: If they are in the list of functions
            that need special handling, we invoke the appropriate handler, otherwise we translate them to
            FunctionName(arg1, arg2, ..., argn).
            We translate user-defined functions to NamespaceName.FunctionName(arg1, arg2, ..., argn).
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbGroupByExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/> for general details.
            We modify both the GroupBy and the Select fields of the SqlSelectStatement.
            GroupBy gets just the keys without aliases,
            and Select gets the keys and the aggregates with aliases.
            Sql Server does not support arbitrarily complex expressions inside aggregates,
            and requires keys to have reference to the input scope,
            so in some cases we create a nested query in which we alias the arguments to the aggregates.
            The exact limitations of Sql Server are:
            <list type="number">
                <item>
                    If an expression being aggregated contains an outer reference, then that outer
                    reference must be the only column referenced in the expression (SQLBUDT #488741)
                </item>
                <item>
                    Sql Server cannot perform an aggregate function on an expression containing
                    an aggregate or a subquery. (SQLBUDT #504600)
                </item>
                <item>
                    Sql Server requries each GROUP BY expression (key) to contain at least one column
                    that is not an outer reference. (SQLBUDT #616523)
                </item>
                <item>
                    Aggregates on the right side of an APPLY cannot reference columns from the left side.
                    (SQLBUDT #617683)
                </item>
            </list>
            The default translation, without inner query is:
            SELECT
            kexp1 AS key1, kexp2 AS key2,... kexpn AS keyn,
            aggf1(aexpr1) AS agg1, .. aggfn(aexprn) AS aggn
            FROM input AS a
            GROUP BY kexp1, kexp2, .. kexpn
            When we inject an innner query, the equivalent translation is:
            SELECT
            key1 AS key1, key2 AS key2, .. keyn AS keys,
            aggf1(agg1) AS agg1, aggfn(aggn) AS aggn
            FROM (
            SELECT
            kexp1 AS key1, kexp2 AS key2,... kexpn AS keyn,
            aexpr1 AS agg1, .. aexprn AS aggn
            FROM input AS a
            ) as a
            GROUP BY key1, key2, keyn
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbUnionAllExpression)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression)">
            <summary>
            Not(IsEmpty) has to be handled specially, so we delegate to
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitIsEmptyExpression(System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression,System.Boolean)"/>.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> . <code>[NOT] EXISTS( ... )</code>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression)">
            <summary>
            Not(IsNull) is handled specially, so we delegate to
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitIsNullExpression(System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression,System.Boolean)"/>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> <code>IS [NOT] NULL</code>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsOfExpression)">
            <summary>
            No error is raised if the store cannot support this.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbCrossJoinExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitJoinExpression(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding},System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpression)"/>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/> .
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbJoinExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitJoinExpression(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding},System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpression)"/>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/> .
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbLikeExpression)">
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbLimitExpression)">
            <summary>
            Translates to TOP expression. For Sql8, limit can only be a constant expression
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNewInstanceExpression)">
            <summary>
            DbNewInstanceExpression is allowed as a child of DbProjectExpression only.
            If anyone else is the parent, we throw.
            We also perform special casing for collections - where we could convert
            them into Unions
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitNewInstanceExpression(System.Data.Entity.Core.Common.CommandTrees.DbNewInstanceExpression,System.Boolean,System.Collections.Generic.Dictionary{System.String,System.Data.Entity.SqlServer.SqlGen.Symbol}@)"/> for the actual implementation.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNotExpression)">
            <summary>
            The Not expression may cause the translation of its child to change.
            These children are
            <list type="bullet">
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbNotExpression"/>
                    NOT(Not(x)) becomes x
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression"/>
                    NOT EXISTS becomes EXISTS
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression"/>
                    IS NULL becomes IS NOT NULL
                </item>
                <item>
                    <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbComparisonExpression"/>
                    = becomes &lt;&gt;
                </item>
            </list>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNullExpression)">
            <returns>
            <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbOfTypeExpression)">
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbOrExpression)">
            <summary>
            Visit a DbOrExpression and consider the subexpressions
            for whether to generate OR conditions or an IN clause.
            </summary>
            <param name="e"> DbOrExpression to be visited </param>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> fragment of SQL generated
            </returns>
            <seealso cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbAndExpression)"/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbInExpression)">
            <summary>
            Visits a DbInExpression and generates the corresponding SQL fragment.
            </summary>
            <param name="e">
            A <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbInExpression"/> that specifies the expression to be visited.
            </param>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> that specifies the generated SQL fragment.
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.TryTranslateIntoIn(System.Data.Entity.Core.Common.CommandTrees.DbOrExpression,System.Data.Entity.SqlServer.SqlGen.ISqlFragment@)">
            <summary>
            Determine if a DbOrExpression can be optimized into one or more IN clauses
            and generate an ISqlFragment if it is possible.
            </summary>
            <param name="e"> DbOrExpression to attempt translation upon </param>
            <param name="sqlFragment"> Fragment of SQL generated </param>
            <returns> True if an IN clause is possible and sqlFragment has been generated, false otherwise </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsKeyForIn(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Determines if a DbExpression is a valid key for the purposes of generating an In clause optimization.
            </summary>
            <param name="e"> DbExpression to consider </param>
            <returns> True if the expression can be used as a key, false otherwise </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.TryAddExpressionForIn(System.Data.Entity.Core.Common.CommandTrees.DbBinaryExpression,System.Collections.Generic.IDictionary{System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression}})">
            <summary>
            Looks at both sides of a DbBinaryExpression to consider if either side is a valid candidate to
            be a key and if so adds it to the KeyToListMap as a key with the other side as the value.
            </summary>
            <param name="e"> DbBinaryExpression to consider </param>
            <param name="values"> KeyToListMap to add the sides of the binary expression to </param>
            <returns> True if the expression was added, false otherwise </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.HasBuiltMapForIn(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Collections.Generic.IDictionary{System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression}})">
            <summary>
            Attempts to build a KeyToListMap containing valid references and the appropriate value equality
            tests associated with each so that they can be optimized into IN clauses. Calls itself recursively
            to consider additional OR branches.
            </summary>
            <param name="e"> DbExpression representing the branch to evaluate </param>
            <param name="values"> KeyToListMap to which to add references and value equality tests encountered </param>
            <returns> True if this branch contained just equality tests or further OR branches, false otherwise </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbParameterReferenceExpression)">
            <summary>
            This method handles the DBParameterReference expressions. If the parameter is in
            a part of the tree, which matches our criteria for forcing to non-unicode, then
            we add it to the list of candidate parameters. If the parameter occurs in a different
            usage scenario, then disqualify it from being forced to non-unicode.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/> for the general ideas.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            </returns>
            <seealso cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression)">
            <summary>
            This method handles record flattening, which works as follows.
            consider an expression <c>Prop(y, Prop(x, Prop(d, Prop(c, Prop(b, Var(a)))))</c>
            where a,b,c are joins, d is an extent and x and y are fields.
            b has been flattened into a, and has its own SELECT statement.
            c has been flattened into b.
            d has been flattened into c.
            We visit the instance, so we reach Var(a) first.  This gives us a (join)symbol.
            Symbol(a).b gives us a join symbol, with a SELECT statement i.e. Symbol(b).
            From this point on , we need to remember Symbol(b) as the source alias,
            and then try to find the column.  So, we use a SymbolPair.
            We have reached the end when the symbol no longer points to a join symbol.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.JoinSymbol"/> if we have not reached the first Join node that has a SELECT statement. A
            <see cref="T:System.Data.Entity.SqlServer.SqlGen.SymbolPair"/>
            if we have seen the JoinNode, and it has a SELECT statement. A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/> with {Input}.propertyName otherwise.
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbQuantifierExpression)">
            <summary>
            Any(input, x) => Exists(Filter(input,x))
            All(input, x) => Not Exists(Filter(input, not(x))
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbSkipExpression)">
            <summary>
            For Sql9 it translates to:
            SELECT Y.x1, Y.x2, ..., Y.xn
            FROM (
            SELECT X.x1, X.x2, ..., X.xn, row_number() OVER (ORDER BY sk1, sk2, ...) AS [row_number]
            FROM input as X
            ) as Y
            WHERE Y.[row_number] &gt; count
            ORDER BY sk1, sk2, ...
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbSortExpression)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            </returns>
            <seealso cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbTreatExpression)">
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbUnionAllExpression)">
            <summary>
            This code is shared by <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbExceptExpression)"/>
            and <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIntersectExpression)"/>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitSetOpExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String)"/>
            Since the left and right expression may not be Sql select statements,
            we must wrap them up to look like SQL select statements.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression)">
            <summary>
            This method determines whether an extent from an outer scope(free variable)
            is used in the CurrentSelectStatement.
            An extent in an outer scope, if its symbol is not in the FromExtents
            of the CurrentSelectStatement.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.Symbol"/> .
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitAggregate(System.Data.Entity.Core.Common.CommandTrees.DbAggregate,System.Object)">
            <summary>
            Aggregates are not visited by the normal visitor walk.
            </summary>
            <param name="aggregate"> The aggregate go be translated </param>
            <param name="aggregateArgument"> The translated aggregate argument </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.ParenthesizeExpressionIfNeeded(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.SqlServer.SqlGen.SqlBuilder)">
            <summary>
            Dump out an expression - optionally wrap it with parantheses if possible
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitBinaryExpression(System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Handler for inline binary expressions.
            Produces left op right.
            For associative operations does flattening.
            Puts parenthesis around the arguments if needed.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.FlattenAssociativeExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Creates a flat list of the associative arguments.
            For example, for ((A1 + (A2 - A3)) + A4) it will create A1, (A2 - A3), A4
            Only 'unfolds' the given arguments that are of the given expression kind.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.ExtractAssociativeArguments(System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.Collections.Generic.List{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Helper method for FlattenAssociativeExpression.
            Creates a flat list of the associative arguments and appends to the given argument list.
            For example, for ((A1 + (A2 - A3)) + A4) it will add A1, (A2 - A3), A4 to the list.
            Only 'unfolds' the given expression if it is of the given expression kind.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitComparisonExpression(System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Private handler for comparison expressions - almost identical to VisitBinaryExpression.
            We special case constants, so that we don't emit unnecessary casts
            </summary>
            <param name="op"> the comparison op </param>
            <param name="left"> the left-side expression </param>
            <param name="right"> the right-side expression </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitInputExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Data.Entity.SqlServer.SqlGen.Symbol@)">
            <summary>
            This is called by the relational nodes.  It does the following
            <list>
                <item>
                    If the input is not a SqlSelectStatement, it assumes that the input
                    is a collection expression, and creates a new SqlSelectStatement
                </item>
            </list>
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/> and the main fromSymbol for this select statement.
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitIsEmptyExpression(System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression,System.Boolean)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression)"/>
            </summary>
            <param name="e"> </param>
            <param name="negate"> Was the parent a DbNotExpression? </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitCollectionConstructor(System.Data.Entity.Core.Common.CommandTrees.DbNewInstanceExpression)">
            <summary>
            Translate a NewInstance(Element(X)) expression into
            "select top(1) * from X"
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitIsNullExpression(System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression,System.Boolean)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsNullExpression)"/>
            </summary>
            <param name="e"> </param>
            <param name="negate"> Was the parent a DbNotExpression? </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitJoinExpression(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding},System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            This handles the processing of join expressions.
            The extents on a left spine are flattened, while joins
            not on the left spine give rise to new nested sub queries.
            Joins work differently from the rest of the visiting, in that
            the parent (i.e. the join node) creates the SqlSelectStatement
            for the children to use.
            The "parameter" IsInJoinContext indicates whether a child extent should
            add its stuff to the existing SqlSelectStatement, or create a new SqlSelectStatement
            By passing true, we ask the children to add themselves to the parent join,
            by passing false, we ask the children to create new Select statements for
            themselves.
            This method is called from <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbApplyExpression)"/> and
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbJoinExpression)"/>.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.ProcessJoinInputResult(System.Data.Entity.SqlServer.SqlGen.ISqlFragment,System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding,System.Int32)">
            <summary>
            This is called from <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitJoinExpression(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding},System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind,System.String,System.Data.Entity.Core.Common.CommandTrees.DbExpression)"/>.
            This is responsible for maintaining the symbol table after visiting
            a child of a join expression.
            The child's sql statement may need to be completed.
            The child's result could be one of
            <list type="number">
                <item>The same as the parent's - this is treated specially.</item>
                <item>A sql select statement, which may need to be completed</item>
                <item>An extent - just copy it to the from clause</item>
                <item>
                    Anything else (from a collection-valued expression) -
                    unnest and copy it.
                </item>
            </list>
            If the input was a Join, we need to create a new join symbol,
            otherwise, we create a normal symbol.
            We then call AddFromSymbol to add the AS clause, and update the symbol table.
            If the child's result was the same as the parent's, we have to clean up
            the list of symbols in the FromExtents list, since this contains symbols from
            the children of both the parent and the child.
            The happens when the child visited is a Join, and is the leftmost child of
            the parent.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitNewInstanceExpression(System.Data.Entity.Core.Common.CommandTrees.DbNewInstanceExpression,System.Boolean,System.Collections.Generic.Dictionary{System.String,System.Data.Entity.SqlServer.SqlGen.Symbol}@)">
            <summary>
            We assume that this is only called as a child of a Project.
            This replaces <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbNewInstanceExpression)"/>, since
            we do not allow DbNewInstanceExpression as a child of any node other than
            DbProjectExpression.
            We write out the translation of each of the columns in the record.
            </summary>
            <returns>
            A <see cref="T:System.Data.Entity.SqlServer.SqlGen.SqlBuilder"/>
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitSetOpExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String)">
            <summary>
            Handler for set operations
            It generates left separator right.
            Only for SQL 8.0 it may need to create a new select statement
            above the set operation if the left child's output columns got renamed
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.Data.Entity.SqlServer.SqlGen.Symbol,System.Collections.Generic.List{System.Data.Entity.SqlServer.SqlGen.Symbol},System.Collections.Generic.Dictionary{System.String,System.Data.Entity.SqlServer.SqlGen.Symbol})">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)"/>
            Add the column names from the referenced extent/join to the
            select statement.
            If the symbol is a JoinSymbol, we recursively visit all the extents,
            halting at real extents and JoinSymbols that have an associated SqlSelectStatement.
            The column names for a real extent can be derived from its type.
            The column names for a Join Select statement can be got from the
            list of columns that was created when the Join's select statement
            was created.
            We do the following for each column.
            <list type="number">
                <item>Add the SQL string for each column to the SELECT clause</item>
                <item>
                    Add the column to the list of columns - so that it can
                    become part of the "type" of a JoinSymbol
                </item>
                <item>
                    Check if the column name collides with a previous column added
                    to the same select statement.  Flag both the columns for renaming if true.
                </item>
                <item>Add the column to a name lookup dictionary for collision detection.</item>
            </list>
            </summary>
            <param name="selectStatement"> The select statement that started off as SELECT * </param>
            <param name="symbol"> The symbol containing the type information for the columns to be added. </param>
            <param name="columnList">
            Columns that have been added to the Select statement. This is created in
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)"/>
            .
            </param>
            <param name="columnDictionary"> A dictionary of the columns above. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.CreateOptionalColumn(System.Data.Entity.SqlServer.SqlGen.Symbol,System.Data.Entity.SqlServer.SqlGen.Symbol)">
            <summary>
            Creates an optional column and registers the corresponding symbol with
            the optionalColumnUsageManager it has not already been registered.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddColumn(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.Data.Entity.SqlServer.SqlGen.Symbol,System.Collections.Generic.List{System.Data.Entity.SqlServer.SqlGen.Symbol},System.Collections.Generic.Dictionary{System.String,System.Data.Entity.SqlServer.SqlGen.Symbol},System.String)">
            <summary>
            Helper method for AddColumns. Adds a column with the given column name
            to the Select list of the given select statement.
            </summary>
            <param name="selectStatement"> The select statement to whose SELECT part the column should be added </param>
            <param name="symbol"> The symbol from which the column to be added originated </param>
            <param name="columnList">
            Columns that have been added to the Select statement. This is created in
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)"/>
            .
            </param>
            <param name="columnDictionary"> A dictionary of the columns above. </param>
            <param name="columnName"> The name of the column to be added. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddDefaultColumns(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement)">
            <summary>
            Expands Select * to "select the_list_of_columns"
            If the columns are taken from an extent, they are written as
            {original_column_name AS Symbol(original_column)} to allow renaming.
            If the columns are taken from a Join, they are written as just
            {original_column_name}, since there cannot be a name collision.
            We concatenate the columns from each of the inputs to the select statement.
            Since the inputs may be joins that are flattened, we need to recurse.
            The inputs are inferred from the symbols in FromExtents.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddFromSymbol(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.SqlServer.SqlGen.Symbol)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddFromSymbol(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.SqlServer.SqlGen.Symbol,System.Boolean)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddFromSymbol(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.SqlServer.SqlGen.Symbol,System.Boolean)">
            <summary>
            This method is called after the input to a relational node is visited.
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression)"/> and <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.ProcessJoinInputResult(System.Data.Entity.SqlServer.SqlGen.ISqlFragment,System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding,System.Int32)"/>
            There are 2 scenarios
            <list type="number">
                <item>
                    The fromSymbol is new i.e. the select statement has just been
                    created, or a join extent has been added.
                </item>
                <item>The fromSymbol is old i.e. we are reusing a select statement.</item>
            </list>
            If we are not reusing the select statement, we have to complete the
            FROM clause with the alias
            <code>-- if the input was an extent
                FROM = [SchemaName].[TableName]
                -- if the input was a Project
                FROM = (SELECT ... FROM ... WHERE ...)</code>
            These become
            <code>-- if the input was an extent
                FROM = [SchemaName].[TableName] AS alias
                -- if the input was a Project
                FROM = (SELECT ... FROM ... WHERE ...) AS alias</code>
            and look like valid FROM clauses.
            Finally, we have to add the alias to the global list of aliases used,
            and also to the current symbol table.
            </summary>
            <param name="selectStatement"> </param>
            <param name="inputVarName"> The alias to be used. </param>
            <param name="fromSymbol"> </param>
            <param name="addToSymbolTable"> </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AddSortKeys(System.Data.Entity.SqlServer.SqlGen.SqlBuilder,System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbSortClause})">
            <summary>
            Translates a list of SortClauses.
            Used in the translation of OrderBy
            </summary>
            <param name="orderByClause"> The SqlBuilder to which the sort keys should be appended </param>
            <param name="sortKeys"> </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.CreateNewSelectStatement(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Data.Entity.SqlServer.SqlGen.Symbol@)">
            <summary>
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.CreateNewSelectStatement(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Boolean,System.Data.Entity.SqlServer.SqlGen.Symbol@)"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.CreateNewSelectStatement(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.String,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Boolean,System.Data.Entity.SqlServer.SqlGen.Symbol@)">
            <summary>
            This is called after a relational node's input has been visited, and the
            input's sql statement cannot be reused.  <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbProjectExpression)"/>
            When the input's sql statement cannot be reused, we create a new sql
            statement, with the old one as the from clause of the new statement.
            The old statement must be completed i.e. if it has an empty select list,
            the list of columns must be projected out.
            If the old statement being completed has a join symbol as its from extent,
            the new statement must have a clone of the join symbol as its extent.
            We cannot reuse the old symbol, but the new select statement must behave
            as though it is working over the "join" record.
            </summary>
            <returns> A new select statement, with the old one as the from clause. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.EscapeSingleQuote(System.String,System.Boolean)">
            <summary>
            Before we embed a string literal in a SQL string, we should
            convert all ' to '', and enclose the whole string in single quotes.
            </summary>
            <returns> The escaped sql string. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GetSqlPrimitiveType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
            Returns the sql primitive/native type name.
            It will include size, precision or scale depending on type information present in the
            type facets
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.HandleCountExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Handles the expression represending DbLimitExpression.Limit and DbSkipExpression.Count.
            If it is a constant expression, it simply does to string thus avoiding casting it to the specific value
            (which would be done if <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression)"/> is called)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsApplyExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            This is used to determine if a particular expression is an Apply operation.
            This is only the case when the DbExpressionKind is CrossApply or OuterApply.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsJoinExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            This is used to determine if a particular expression is a Join operation.
            This is true for DbCrossJoinExpression and DbJoinExpression, the
            latter of which may have one of several different ExpressionKinds.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsComplexExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            This is used to determine if a calling expression needs to place
            round brackets around the translation of the expression e.
            Constants, parameters and properties do not require brackets,
            everything else does.
            </summary>
            <returns> true, if the expression needs brackets </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsCompatible(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind)">
            <summary>
            Determine if the owner expression can add its unique sql to the input's
            SqlSelectStatement
            </summary>
            <param name="result"> The SqlSelectStatement of the input to the relational node. </param>
            <param name="expressionKind"> The kind of the expression node(not the input's) </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.QuoteIdentifier(System.String)">
            <summary>
            We use the normal box quotes for SQL server.  We do not deal with ANSI quotes
            i.e. double quotes.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitExpressionEnsureSqlStatement(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Simply calls <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitExpressionEnsureSqlStatement(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Boolean,System.Boolean)"/>
            with addDefaultColumns set to true and markAllDefaultColumnsAsUsed set to false.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitExpressionEnsureSqlStatement(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Boolean,System.Boolean)">
            <summary>
            This is called from <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GenerateSql(System.Data.Entity.Core.Common.CommandTrees.DbQueryCommandTree,System.Collections.Generic.HashSet{System.String}@)"/>
            and nodes which require a select statement as an argument e.g. <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbIsEmptyExpression)"/>,
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbUnionAllExpression)"/>.
            SqlGenerator needs its child to have a proper alias if the child is
            just an extent or a join.
            The normal relational nodes result in complete valid SQL statements.
            For the rest, we need to treat them as there was a dummy
            <code>-- originally {expression}
                                            -- change that to
                                            SELECT *
                                            FROM {expression} as c</code>
            DbLimitExpression needs to start the statement but not add the default columns
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.VisitFilterExpression(System.Data.Entity.Core.Common.CommandTrees.DbExpressionBinding,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Boolean)">
            <summary>
            This method is called by <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbFilterExpression)"/> and
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbQuantifierExpression)"/>
            </summary>
            <param name="input"> </param>
            <param name="predicate"> </param>
            <param name="negatePredicate">
            This is passed from <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbQuantifierExpression)"/> in the All(...) case.
            </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.WrapNonQueryExtent(System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement,System.Data.Entity.SqlServer.SqlGen.ISqlFragment,System.Data.Entity.Core.Common.CommandTrees.DbExpressionKind)">
            <summary>
            If the sql fragment for an input expression is not a SqlSelect statement
            or other acceptable form (e.g. an extent as a SqlBuilder), we need
            to wrap it in a form acceptable in a FROM clause.  These are
            primarily the
            <list type="bullet">
                <item>The set operation expressions - union all, intersect, except</item>
                <item>TVFs, which are conceptually similar to tables</item>
            </list>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GroupByAggregatesNeedInnerQuery(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbAggregate},System.String)">
            <summary>
            Helper method for the Group By visitor
            Returns true if at least one of the aggregates in the given list
            has an argument that is not a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression"/> and is not
            a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression"/> over <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression"/>,
            either potentially capped with a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbCastExpression"/>
            This is really due to the following two limitations of Sql Server:
            <list type="number">
                <item>
                    If an expression being aggregated contains an outer reference, then that outer
                    reference must be the only column referenced in the expression (SQLBUDT #488741)
                </item>
                <item>
                    Sql Server cannot perform an aggregate function on an expression containing
                    an aggregate or a subquery. (SQLBUDT #504600)
                </item>
            </list>
            Potentially, we could furhter optimize this.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GroupByAggregateNeedsInnerQuery(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String)">
            <summary>
            Returns true if the given expression is not a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression"/> or a
            <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression"/> over  a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression"/>
            referencing the given inputVarRefName, either
            potentially capped with a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbCastExpression"/>.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GroupByKeysNeedInnerQuery(System.Collections.Generic.IList{System.Data.Entity.Core.Common.CommandTrees.DbExpression},System.String)">
            <summary>
            Helper method for the Group By visitor
            Returns true if at least one of the expressions in the given list
            is not <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression"/> over <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression"/>
            referencing the given inputVarRefName potentially capped with a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbCastExpression"/>.
            This is really due to the following limitation: Sql Server requires each GROUP BY expression
            (key) to contain at least one column that is not an outer reference. (SQLBUDT #616523)
            Potentially, we could further optimize this.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GroupByKeyNeedsInnerQuery(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String)">
            <summary>
            Returns true if the given expression is not <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression"/> over
            <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression"/> referencing the given inputVarRefName
            potentially capped with a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbCastExpression"/>.
            This is really due to the following limitation: Sql Server requires each GROUP BY expression
            (key) to contain at least one column that is not an outer reference. (SQLBUDT #616523)
            Potentially, we could further optimize this.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.GroupByExpressionNeedsInnerQuery(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String,System.Boolean)">
            <summary>
            Helper method for processing Group By keys and aggregates.
            Returns true if the given expression is not a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbConstantExpression"/>
            (and allowConstants is specified)or a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression"/> over
            a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression"/> referencing the given inputVarRefName,
            either potentially capped with a <see cref="T:System.Data.Entity.Core.Common.CommandTrees.DbCastExpression"/>.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AssertKatmaiOrNewer(System.Data.Entity.Core.Metadata.Edm.PrimitiveTypeKind)">
            <summary>
            Throws not supported exception if the server is pre-katmai
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.AssertKatmaiOrNewer(System.Data.Entity.Core.Common.CommandTrees.DbFunctionExpression)">
            <summary>
            Throws not supported exception if the server is pre-katmai
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.CurrentSelectStatement">
            <summary>
            The top of the stack
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.IsParentAJoin">
            <summary>
            Determine if the parent is a join.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.KeyFieldExpressionComparer">
            <summary>
            Required by the KeyToListMap to allow certain DbExpression subclasses to be used as a key
            which is not normally possible given their lack of Equals and GetHashCode implementations
            for testing object value equality.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.KeyFieldExpressionComparer.Equals(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Compare two DbExpressions to see if they are equal for the purposes of
            our key management. We only support DbPropertyExpression, DbParameterReferenceExpression,
            VariableReferenceExpression and DbCastExpression types. Everything else will fail to
            be considered equal.
            </summary>
            <param name="x"> First DbExpression to consider for equality </param>
            <param name="y"> Second DbExpression to consider for equality </param>
            <returns> True if the types are allowed and equal, false otherwise </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.KeyFieldExpressionComparer.GetHashCode(System.Data.Entity.Core.Common.CommandTrees.DbExpression)">
            <summary>
            Calculates a hashcode for a given number of DbExpression subclasses to allow the KeyToListMap
            to efficiently and reliably locate existing keys.
            </summary>
            <param name="obj"> DbExpression to calculate a hashcode for </param>
            <returns> Integer containing the hashcode </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectClauseBuilder">
            <summary>
            This class is used for building the SELECT clause of a Sql Statement
            It is used to gather information about required and optional columns
            and whether TOP and DISTINCT should be specified.
            The underlying SqlBuilder is used for gathering the required columns.
            The list of OptionalColumns is used for gathering the optional columns.
            Whether a given OptionalColumn should be written is known only after the entire
            command tree has been processed.
            The IsDistinct property indicates that we want distinct columns.
            This is given out of band, since the input expression to the select clause
            may already have some columns projected out, and we use append-only SqlBuilders.
            The DISTINCT is inserted when we finally write the object into a string.
            Also, we have a Top property, which is non-null if the number of results should
            be limited to certain number. It is given out of band for the same reasons as DISTINCT.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlSelectClauseBuilder.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            Writes the string representing the Select statement:
            SELECT (DISTINCT) (TOP topClause) (optionalColumns) (requiredColumns)
            If Distinct is specified or this is part of a top most statement
            all optional columns are marked as used.
            Optional columns are only written if marked as used.
            In addition, if no required columns are specified and no optional columns are
            marked as used, the first optional column is written.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlSelectClauseBuilder.WriteOptionalColumns(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            Writes the optional columns that are used.
            If this is the topmost statement or distict is specifed as part of the same statement
            all optoinal columns are written.
            </summary>
            <returns> Whether at least one column got written </returns>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlSelectClauseBuilder.IsDistinct">
            <summary>
            Do we need to add a DISTINCT at the beginning of the SELECT
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlSelectClauseBuilder.IsEmpty">
            <summary>
            Whether any columns have been specified.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement">
            <summary>
            A SqlSelectStatement represents a canonical SQL SELECT statement.
            It has fields for the 5 main clauses
            <list type="number">
                <item>SELECT</item>
                <item>FROM</item>
                <item>WHERE</item>
                <item>GROUP BY</item>
                <item>ORDER BY</item>
            </list>
            We do not have HAVING, since the CQT does not have such a node.
            Each of the fields is a SqlBuilder, so we can keep appending SQL strings
            or other fragments to build up the clause.
            The FromExtents contains the list of inputs in use for the select statement.
            There is usually just one element in this - Select statements for joins may
            temporarily have more than one.
            If the select statement is created by a Join node, we maintain a list of
            all the extents that have been flattened in the join in AllJoinExtents
            <example>
                in J(j1= J(a,b), c)
                FromExtents has 2 nodes JoinSymbol(name=j1, ...) and Symbol(name=c)
                AllJoinExtents has 3 nodes Symbol(name=a), Symbol(name=b), Symbol(name=c)
            </example>
            If any expression in the non-FROM clause refers to an extent in a higher scope,
            we add that extent to the OuterExtents list.  This list denotes the list
            of extent aliases that may collide with the aliases used in this select statement.
            It is set by <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression)"/>.
            An extent is an outer extent if it is not one of the FromExtents.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            Write out a SQL select statement as a string.
            We have to
            <list type="number">
                <item>
                    Check whether the aliases extents we use in this statement have
                    to be renamed.
                    We first create a list of all the aliases used by the outer extents.
                    For each of the FromExtents( or AllJoinExtents if it is non-null),
                    rename it if it collides with the previous list.
                </item>
                <item>Write each of the clauses (if it exists) as a string</item>
            </list>
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement.OutputColumnsRenamed">
            <summary>
            Whether the columns ouput by this sql statement were renamed from what given in the command tree.
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.SqlSelectStatement.OutputColumns">
            <summary>
            A dictionary of output columns
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SqlWriter">
            <summary>
            This extends IndentedTextWriter/StringWriter primarily to add the ability to add an indent
            to each line that is written out.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SymbolPair">
            <summary>
            The SymbolPair exists to solve the record flattening problem.
            <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression)"/>
            Consider a property expression D(v, "j3.j2.j1.a.x")
            where v is a VarRef, j1, j2, j3 are joins, a is an extent and x is a columns.
            This has to be translated eventually into {j'}.{x'}
            The source field represents the outermost SqlStatement representing a join
            expression (say j2) - this is always a Join symbol.
            The column field keeps moving from one join symbol to the next, until it
            stops at a non-join symbol.
            This is returned by <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbPropertyExpression)"/>,
            but never makes it into a SqlBuilder.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SymbolTable">
            <summary>
            The symbol table is quite primitive - it is a stack with a new entry for
            each scope.  Lookups search from the top of the stack to the bottom, until
            an entry is found.
            The symbols are of the following kinds
            <list type="bullet">
                <item>
                    <see cref="T:System.Data.Entity.SqlServer.SqlGen.Symbol"/>
                    represents tables (extents/nested selects/unnests)
                </item>
                <item>
                    <see cref="T:System.Data.Entity.SqlServer.SqlGen.JoinSymbol"/>
                    represents Join nodes
                </item>
                <item>
                    <see cref="T:System.Data.Entity.SqlServer.SqlGen.Symbol"/>
                    columns.
                </item>
            </list>
            Symbols represent names <see cref="M:System.Data.Entity.SqlServer.SqlGen.SqlGenerator.Visit(System.Data.Entity.Core.Common.CommandTrees.DbVariableReferenceExpression)"/> to be resolved,
            or things to be renamed.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.SymbolUsageManager">
            <summary>
            Tracks the usage of symbols.
            When registering a symbol with the usage manager if an input symbol is specified,
            than the usage of the two is 'connected' - if one ever gets marked as used,
            the other one becomes 'used' too.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlGen.TopClause">
            <summary>
            TopClause represents the a TOP expression in a SqlSelectStatement.
            It has a count property, which indicates how many TOP rows should be selected and a
            boolen WithTies property.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.TopClause.#ctor(System.Data.Entity.SqlServer.SqlGen.ISqlFragment,System.Boolean)">
            <summary>
            Creates a TopClause with the given topCount and withTies.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.TopClause.#ctor(System.Int32,System.Boolean)">
            <summary>
            Creates a TopClause with the given topCount and withTies.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlGen.TopClause.WriteSql(System.Data.Entity.SqlServer.SqlGen.SqlWriter,System.Data.Entity.SqlServer.SqlGen.SqlGenerator)">
            <summary>
            Write out the TOP part of sql select statement
            It basically writes TOP (X) [WITH TIES].
            The brackets around X are ommited for Sql8.
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.TopClause.WithTies">
            <summary>
            Do we need to add a WITH_TIES to the top statement
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlGen.TopClause.TopCount">
            <summary>
            How many top rows should be selected.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlProviderManifest">
            <summary>
            The Provider Manifest for SQL Server
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlProviderManifest.varcharMaxSize">
            <summary>
            Maximum size of SQL Server unicode
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Data.Entity.SqlServer.SqlProviderManifest"/> class.
            </summary>
            <param name="manifestToken"> A token used to infer the capabilities of the store. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.EscapeLikeText(System.String,System.Boolean,System.Boolean@)">
            <summary>
            Function to detect wildcard characters %, _, [ and ^ and escape them with a preceding ~
            This escaping is used when StartsWith, EndsWith and Contains canonical and CLR functions
            are translated to their equivalent LIKE expression
            NOTE: This code has been copied from LinqToSql
            </summary>
            <param name="text"> Original input as specified by the user </param>
            <param name="alwaysEscapeEscapeChar"> escape the escape character ~ regardless whether wildcard characters were encountered </param>
            <param name="usedEscapeChar"> true if the escaping was performed, false if no escaping was required </param>
            <returns> The escaped string that can be used as pattern in a LIKE expression </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.GetDbInformation(System.String)">
            <summary>
            Providers should override this to return information specific to their provider.
            This method should never return null.
            </summary>
            <param name="informationType"> The name of the information to be retrieved. </param>
            <returns> An XmlReader at the begining of the information requested. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.GetEdmType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
            This method takes a type and a set of facets and returns the best mapped equivalent type
            in EDM.
            </summary>
            <param name="storeType"> A TypeUsage encapsulating a store type and a set of facets </param>
            <returns> A TypeUsage encapsulating an EDM type and a set of facets </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.GetStoreType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
            This method takes a type and a set of facets and returns the best mapped equivalent type
            in SQL Server, taking the store version into consideration.
            </summary>
            <param name="edmType"> A TypeUsage encapsulating an EDM type and a set of facets </param>
            <returns> A TypeUsage encapsulating a store type and a set of facets </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.SupportsEscapingLikeArgument(System.Char@)">
            <summary>
            Returns true, SqlClient supports escaping strings to be used as arguments to like
            The escape character is '~'
            </summary>
            <param name="escapeCharacter"> The character '~' </param>
            <returns> True </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.EscapeLikeArgument(System.String)">
            <summary>
            Escapes the wildcard characters and the escape character in the given argument.
            </summary>
            <returns> Equivalent to the argument, with the wildcard characters and the escape character escaped </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderManifest.SupportsInExpression">
            <summary>
            Returns a boolean that specifies whether the corresponding provider can handle expression trees 
            containing instances of DbInExpression.
            The Sql provider handles instances of DbInExpression.
            </summary>
            <returns> <c>true</c>. </returns>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlProviderServices">
            <summary>
            The DbProviderServices implementation for the SqlClient provider for SQL Server.
            </summary>
            <remarks>
            Note that instance of this type also resolve additional provider services for Microsoft SQL Server
            when this type is registered as an EF provider either using an entry in the application's config file
            or through code-based registration in <see cref="T:System.Data.Entity.DbConfiguration"/>.
            The services resolved are:
            Requests for <see cref="T:System.Data.Entity.Infrastructure.IDbConnectionFactory"/> are resolved to a Singleton instance of
            <see cref="T:System.Data.Entity.Infrastructure.SqlConnectionFactory"/> to create connections to SQL Express by default.
            Requests for <see cref="T:System.Func`1"/> for the invariant name "System.Data.SqlClient"
            for any server name are resolved to a delegate that returns a <see cref="T:System.Data.Entity.SqlServer.DefaultSqlExecutionStrategy"/>
            to provide a non-retrying policy for SQL Server.
            Requests for <see cref="T:System.Data.Entity.Migrations.Sql.MigrationSqlGenerator"/> for the invariant name "System.Data.SqlClient" are
            resolved to <see cref="T:System.Data.Entity.SqlServer.SqlServerMigrationSqlGenerator"/> instances to provide default Migrations SQL
            generation for SQL Server.
            Requests for <see cref="T:System.Data.Entity.Spatial.DbSpatialServices"/> for the invariant name "System.Data.SqlClient" are
            resolved to a Singleton instance of <see cref="T:System.Data.Entity.SqlServer.SqlSpatialServices"/> to provide default spatial
            services for SQL Server.
            </remarks>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlProviderServices.ProviderInvariantName">
            <summary>
            This is the well-known string using in configuration files and code-based configuration as
            the "provider invariant name" used to specify Microsoft SQL Server for ADO.NET and
            Entity Framework provider services.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.#ctor">
            <summary>
            Private constructor to ensure only Singleton instance is created.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlProviderServices._providerInstance">
            <summary>
            Singleton object
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.RegisterInfoMessageHandler(System.Data.Common.DbConnection,System.Action{System.String})">
            <summary>
            Registers a handler to process non-error messages coming from the database provider.
            </summary>
            <param name="connection"> The connection to receive information for. </param>
            <param name="handler"> The handler to process messages. </param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.CreateDbCommandDefinition(System.Data.Entity.Core.Common.DbProviderManifest,System.Data.Entity.Core.Common.CommandTrees.DbCommandTree)">
            <summary>
            Create a Command Definition object, given the connection and command tree
            </summary>
            <param name="providerManifest"> provider manifest that was determined from metadata </param>
            <param name="commandTree"> command tree for the statement </param>
            <returns> an executable command definition object </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.CreateCommand(System.Data.Entity.Core.Common.DbProviderManifest,System.Data.Entity.Core.Common.CommandTrees.DbCommandTree)">
            <summary>
            Create a SqlCommand object, given the provider manifest and command tree
            </summary>
            <param name="providerManifest"> provider manifest </param>
            <param name="commandTree"> command tree for the statement </param>
            <returns> a command object </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.SetDbParameterValue(System.Data.Common.DbParameter,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Object)">
            <summary>
            Sets the parameter value and appropriate facets for the given <see cref="T:System.Data.Entity.Core.Metadata.Edm.TypeUsage"/>.
            </summary>
            <param name="parameter">The parameter.</param>
            <param name="parameterType">The type of the parameter.</param>
            <param name="value">The value of the parameter.</param>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetDbProviderManifestToken(System.Data.Common.DbConnection)">
            <summary>
            Returns provider manifest token for a given connection.
            </summary>
            <param name="connection"> Connection to find manifest token from. </param>
            <returns> The provider manifest token for the specified connection. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetDbProviderManifest(System.String)">
            <summary>
            Returns the provider manifest by using the specified version information.
            </summary>
            <param name="versionHint"> The token information associated with the provider manifest. </param>
            <returns> The provider manifest by using the specified version information. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetDbSpatialDataReader(System.Data.Common.DbDataReader,System.String)">
            <summary>
            Gets a spatial data reader for SQL Server.
            </summary>
            <param name="fromReader"> The reader where the spatial data came from. </param>
            <param name="versionHint"> The manifest token associated with the provider manifest. </param>
            <returns> The spatial data reader. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.DbGetSpatialServices(System.String)">
            <summary>
            Gets a spatial data reader for SQL Server.
            </summary>
            <param name="versionHint"> The manifest token associated with the provider manifest. </param>
            <returns> The spatial data reader. </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.CreateSqlParameter(System.String,System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Data.Entity.Core.Metadata.Edm.ParameterMode,System.Object,System.Boolean,System.Data.Entity.SqlServer.SqlVersion)">
            <summary>
            Creates a SqlParameter given a name, type, and direction
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.EnsureSqlParameterValue(System.Object)">
            <summary>
            Validates that the specified value is compatible with SqlParameter and if not, attempts to return an appropriate value that is.
            Currently only spatial values (DbGeography/DbGeometry) may not be directly usable with SqlParameter. For these types, an instance
            of the corresponding SQL Server CLR spatial UDT will be manufactured based on the spatial data contained in
            <paramref name="value" />.
            If <paramref name="value" /> is an instance of DbGeography/DbGeometry that was read from SQL Server by this provider, then the wrapped
            CLR UDT value is available via the ProviderValue property (see SqlSpatialServices for the full conversion process from instances of
            DbGeography/DbGeometry to instances of the CLR SqlGeography/SqlGeometry UDTs)
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetSqlDbType(System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Boolean,System.Data.Entity.SqlServer.SqlVersion,System.Nullable{System.Int32}@,System.Nullable{System.Byte}@,System.Nullable{System.Byte}@,System.String@)">
            <summary>
            Determines SqlDbType for the given primitive type. Extracts facet
            information as well.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetParameterSize(System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Boolean)">
            <summary>
            Determines preferred value for SqlParameter.Size. Returns null
            where there is no preference.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetKatmaiDateTimePrecision(System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Boolean)">
            <summary>
            Returns SqlParameter.Precision where the type facet exists. Otherwise,
            returns null or the maximum available precision to avoid truncation (which can occur
            for output parameters).
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetParameterPrecision(System.Data.Entity.Core.Metadata.Edm.TypeUsage,System.Nullable{System.Byte})">
            <summary>
            Returns SqlParameter.Precision where the type facet exists. Otherwise,
            returns null.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetScale(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
            Returns SqlParameter.Scale where the type facet exists. Otherwise,
            returns null.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetStringDbType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
            Chooses the appropriate SqlDbType for the given string type.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetBinaryDbType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
            Chooses the appropriate SqlDbType for the given binary type.
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.DbCreateDatabaseScript(System.String,System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
            Generates a data definition language (DDL) script that creates schema objects 
            (tables, primary keys, foreign keys) based on the contents of the StoreItemCollection 
            parameter and targeted for the version of the database corresponding to the provider manifest token.
            </summary>
            <param name="providerManifestToken"> The provider manifest token identifying the target version. </param>
            <param name="storeItemCollection"> The structure of the database. </param>
            <returns>
            A DDL script that creates schema objects based on the contents of the StoreItemCollection parameter 
            and targeted for the version of the database corresponding to the provider manifest token.
            </returns>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.DbCreateDatabase(System.Data.Common.DbConnection,System.Nullable{System.Int32},System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
            Create the database and the database objects.
            If initial catalog is not specified, but AttachDBFilename is specified, we generate a random database name based on the AttachDBFilename.
            Note: this causes pollution of the db, as when the connection string is later used, the mdf will get attached under a different name.
            However if we try to replicate the name under which it would be attached, the following scenario would fail:
            The file does not exist, but registered with database.
            The user calls:  If (DatabaseExists) DeleteDatabase
            CreateDatabase
            For further details on the behavior when AttachDBFilename is specified see Dev10# 188936
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetLdfFileName(System.String)">
            <summary>
            Get the Ldf name given the Mdf full name
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GenerateDatabaseName(System.String)">
            <summary>
            Generates database name based on the given mdfFileName.
            The logic is replicated from System.Web.DataAccess.SqlConnectionHelper
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.GetMdfFileName(System.String)">
            <summary>
            Get the full mdf file name given the attachDBFile value from the connection string
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.DbDatabaseExists(System.Data.Common.DbConnection,System.Nullable{System.Int32},System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
            Determines whether the database for the given connection exists.
            There are three cases:
            1.  Initial Catalog = X, AttachDBFilename = null:   (SELECT Count(*) FROM sys.databases WHERE [name]= X) > 0
            2.  Initial Catalog = X, AttachDBFilename = F:      if (SELECT Count(*) FROM sys.databases WHERE [name]= X) >  true,
            if not, try to open the connection and then return (SELECT Count(*) FROM sys.databases WHERE [name]= X) > 0
            3.  Initial Catalog = null, AttachDBFilename = F:   Try to open the connection. If that succeeds the result is true, otherwise
            if the there are no databases corresponding to the given file return false, otherwise throw.
            Note: We open the connection to cover the scenario when the mdf exists, but is not attached.
            Given that opening the connection would auto-attach it, it would not be appropriate to return false in this case.
            Also note that checking for the existence of the file does not work for a remote server.  (Dev11 #290487)
            For further details on the behavior when AttachDBFilename is specified see Dev10# 188936
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderServices.DbDeleteDatabase(System.Data.Common.DbConnection,System.Nullable{System.Int32},System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
            Delete the database for the given connection.
            There are three cases:
            1.  If Initial Catalog is specified (X) drop database X
            2.  Else if AttachDBFilename is specified (F) drop all the databases corresponding to F
            if none throw
            3.  If niether the catalog not the file name is specified - throw
            Note that directly deleting the files does not work for a remote server.  However, even for not attached
            databases the current logic would work assuming the user does: if (DatabaseExists) DeleteDatabase
            </summary>
            <param name="connection"> Connection </param>
            <param name="commandTimeout"> Timeout for internal commands. </param>
            <param name="storeItemCollection"> Item Collection. </param>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlProviderServices.Instance">
            <summary>
            The Singleton instance of the SqlProviderServices type.
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlProviderServices.TruncateDecimalsToScale">
            <summary>
            Set this flag to false to prevent <see cref="T:System.Decimal"/> values from being truncated to
            the scale (number of decimal places) defined for the column. The default value is true,
            indicating that decimal values will be truncated, in order to prevent breaking existing
            applications that depend on this behavior.
            </summary>
            <remarks>
            With this flag set to true <see cref="T:System.Data.SqlClient.SqlParameter"/> objects are created with their Scale
            properties set. When this flag is set to false then the Scale properties are not set, meaning
            that the truncation behavior of SqlParameter is avoided.
            </remarks>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlProviderUtilities.GetRequiredSqlConnection(System.Data.Common.DbConnection)">
            <summary>
            Requires that the given connection is of type  T.
            Returns the connection or throws.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlSpatialDataReader">
            <summary>
            SqlClient specific implementation of <see cref="T:System.Data.Entity.Spatial.DbSpatialDataReader"/>
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialDataReader.GetGeography(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialDataReader.GetGeometry(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialDataReader.IsGeographyColumn(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialDataReader.IsGeometryColumn(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlSpatialDataReader.CreateBinaryReadDelegate(System.Type)">
            <summary>
            Builds and compiles the Expression equivalent of the following:
            (BinaryReader r) => { var result = new SpatialType(); result.Read(r); return r; }
            The construct/read pattern is preferred over casting the result of calling GetValue on the DataReader,
            because constructing the value directly allows client code to specify the type, rather than SqlClient using
            the server-specified assembly qualified type name from TDS to try to locate the correct type on the client.
            </summary>
        </member>
        <member name="P:System.Data.Entity.SqlServer.SqlSpatialServices.NativeTypesAvailable">
            <inheritdoc />
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlTypesAssembly">
            <summary>
            SqlTypesAssembly allows for late binding to the capabilities of a specific version of the Microsoft.SqlServer.Types assembly
            </summary>
        </member>
        <member name="M:System.Data.Entity.SqlServer.SqlTypesAssembly.#ctor">
            <summary>
            For testing.
            </summary>
        </member>
        <member name="T:System.Data.Entity.SqlServer.SqlVersion">
            <summary>
            This enumeration describes the current SQL Server version.
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlVersion.Sql8">
            <summary>
            SQL Server 8 (2000).
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlVersion.Sql9">
            <summary>
            SQL Server 9 (2005).
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlVersion.Sql10">
            <summary>
            SQL Server 10 (2008).
            </summary>
        </member>
        <member name="F:System.Data.Entity.SqlServer.SqlVersion.Sql11">
            <summary>
            SQL Server 11 (2012).
            </summary>
        </member>
    </members>
</doc>
