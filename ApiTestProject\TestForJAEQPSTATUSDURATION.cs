﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Services.AstroenergyService;
using Services.CoreBusRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiTestProject
{
    [Microsoft.VisualStudio.TestTools.UnitTesting.TestClass]
    public class TestForJAEQPSTATUSDURATION
    {
        private readonly TaskRepository _taskRepository;

        public TestForJAEQPSTATUSDURATION()
        {
            _taskRepository = new TaskRepository();
        }

        [TestMethod]
        public void GetEqpStatusDurationMaxDateTime()
        {
            var d = _taskRepository.GetEqpStatusDurationMaxDateTime().GetAwaiter().GetResult();
            Console.WriteLine(d);
            Assert.IsNotNull(d);
        }

        [TestMethod]
        public void GetJaEqpStatusPendingData()
        {
            var list = _taskRepository.GetJaEqpStatusPendingData(new DateTime(2024, 1, 1, 1, 0, 0)).GetAwaiter().GetResult();
            Assert.IsTrue(list?.Count > 0);
        }

        [TestMethod]
        public void GetJaEqpStatusOldStatus()
        {
            var list = _taskRepository.GetJaEqpStatusPendingData(new DateTime(2024, 1, 1, 1, 0, 0)).GetAwaiter().GetResult();
            var d = _taskRepository.GetEqpStatusDurationLatestDataList(list).GetAwaiter().GetResult();
            Assert.IsTrue(d?.Count > 0);
        }

        [TestMethod]
        public void SP_EQPSTATUS_Statistics()
        {
            TaskService taskService = new TaskService();
            var result = taskService.SP_EQPSTATUS_Statistics();
        }
    }
}
