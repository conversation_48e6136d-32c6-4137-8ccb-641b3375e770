<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.IO</name>
    </assembly>
    <members>
        <member name="T:System.Strings">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:System.Strings.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:System.Strings.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:System.Strings.GenericInvalidData">
            <summary>
              Looks up a localized string similar to Found invalid data while decoding..
            </summary>
        </member>
        <member name="T:System.IO.InvalidDataException">
            <summary>
            The exception that is thrown when a data stream is in an invalid format.
            </summary>
        </member>
        <member name="M:System.IO.InvalidDataException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IO.InvalidDataException" /> class.
            </summary>
        </member>
        <member name="M:System.IO.InvalidDataException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IO.InvalidDataException" /> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:System.IO.InvalidDataException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IO.InvalidDataException" /> class with a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
        </member>
    </members>
</doc>
