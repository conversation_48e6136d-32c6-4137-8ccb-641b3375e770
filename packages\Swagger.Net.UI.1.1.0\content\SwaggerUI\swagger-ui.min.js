$(function(){$.fn.vAlign=function(){return this.each(function(c){var a=$(this).height();var d=$(this).parent().height();var b=(d-a)/2;$(this).css("margin-top",b)})};$.fn.stretchFormtasticInputWidthToParent=function(){return this.each(function(b){var d=$(this).closest("form").innerWidth();var c=parseInt($(this).closest("form").css("padding-left"),10)+parseInt($(this).closest("form").css("padding-right"),10);var a=parseInt($(this).css("padding-left"),10)+parseInt($(this).css("padding-right"),10);$(this).css("width",d-c-a)})};$("form.formtastic li.string input, form.formtastic textarea").stretchFormtasticInputWidthToParent();$("ul.downplayed li div.content p").vAlign();$("form.sandbox").submit(function(){var a=true;$(this).find("input.required").each(function(){$(this).removeClass("error");if($(this).val()==""){$(this).addClass("error");$(this).wiggle();a=false}});return a})});function clippyCopiedCallback(b){$("#api_key_copied").fadeIn().delay(1000).fadeOut()}function log(){if(window.console){console.log.apply(console,arguments)}}var Docs={shebang:function(){var b=$.param.fragment().split("/");b.shift();switch(b.length){case 1:var d="resource_"+b[0];Docs.expandEndpointListForResource(b[0]);$("#"+d).slideto({highlight:false});break;case 2:Docs.expandEndpointListForResource(b[0]);$("#"+d).slideto({highlight:false});var c=b.join("_");var a=c+"_content";Docs.expandOperation($("#"+a));$("#"+c).slideto({highlight:false});break}},toggleEndpointListForResource:function(b){var a=$("li#resource_"+b+" ul.endpoints");if(a.is(":visible")){Docs.collapseEndpointListForResource(b)}else{Docs.expandEndpointListForResource(b)}},expandEndpointListForResource:function(b){$("#resource_"+b).addClass("active");var a=$("li#resource_"+b+" ul.endpoints");a.slideDown()},collapseEndpointListForResource:function(b){$("#resource_"+b).removeClass("active");var a=$("li#resource_"+b+" ul.endpoints");a.slideUp()},expandOperationsForResource:function(a){Docs.expandEndpointListForResource(a);$("li#resource_"+a+" li.operation div.content").each(function(){Docs.expandOperation($(this))})},collapseOperationsForResource:function(a){Docs.expandEndpointListForResource(a);$("li#resource_"+a+" li.operation div.content").each(function(){Docs.collapseOperation($(this))})},expandOperation:function(a){a.slideDown()},collapseOperation:function(a){a.slideUp()}};(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.main=b(function(e,n,d,l,k){d=d||e.helpers;var i="",c,h,o=this,f="function",m=d.helperMissing,g=void 0,j=this.escapeExpression;i+="\n<div class='container' id='resources_container'>\n    <ul id='resources'>\n    </ul>\n\n    <div class=\"footer\">\n        <br>\n        <br>\n        <h4 style=\"color: #999\">[<span style=\"font-variant: small-caps\">base url</span>: ";h=d.basePath;c=h||n.basePath;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"basePath",{hash:{}})}}i+=j(c)+"]</h4>\n    </div>\n</div>";return i})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.operation=b(function(g,s,q,k,t){q=q||g.helpers;var r="",i,f,h,o,n=this,e="function",p=q.helperMissing,c=void 0,d=this.escapeExpression;function m(x,w){var u="",v;u+="\n                <h4>Implementation Notes</h4>\n                <p>";h=q.notes;v=h||x.notes;if(typeof v===e){v=v.call(x,{hash:{}})}else{if(v===c){v=p.call(x,"notes",{hash:{}})}}if(v||v===0){u+=v}u+="</p>\n                ";return u}function l(v,u){return"\n                    "}function j(v,u){return"\n                    <div class='sandbox_header'>\n                        <input class='submit' name='commit' type='button' value='Try it out!' />\n                        <a href='#' class='response_hider' style='display:none'>Hide Response</a>\n                        <img alt='Throbber' class='response_throbber' src='http://swagger.wordnik.com/images/throbber.gif' style='display:none' />\n                    </div>\n                    "}r+="\n    <ul class='operations' >\n        <li class='";h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+" operation' id='";h=q.resourceName;i=h||s.resourceName;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"resourceName",{hash:{}})}}r+=d(i)+"_";h=q.nickname;i=h||s.nickname;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"nickname",{hash:{}})}}r+=d(i)+"_";h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+"'>\n            <div class='heading'>\n                <h3>\n                  <span class='http_method'>\n                    <a href='#!/";h=q.resourceName;i=h||s.resourceName;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"resourceName",{hash:{}})}}r+=d(i)+"/";h=q.nickname;i=h||s.nickname;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"nickname",{hash:{}})}}r+=d(i)+"_";h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+'\' class="toggleOperation">';h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+"</a>\n                  </span>\n                  <span class='path'>\n                    <a href='#!/";h=q.resourceName;i=h||s.resourceName;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"resourceName",{hash:{}})}}r+=d(i)+"/";h=q.nickname;i=h||s.nickname;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"nickname",{hash:{}})}}r+=d(i)+"_";h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+'\' class="toggleOperation">';h=q.pathJson;i=h||s.pathJson;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"pathJson",{hash:{}})}}r+=d(i)+"</a>\n                  </span>\n                </h3>\n                <ul class='options'>\n                    <li>\n                        <a href='#!/";h=q.resourceName;i=h||s.resourceName;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"resourceName",{hash:{}})}}r+=d(i)+"/";h=q.nickname;i=h||s.nickname;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"nickname",{hash:{}})}}r+=d(i)+"_";h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+'\' class="toggleOperation">';h=q.summary;i=h||s.summary;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"summary",{hash:{}})}}if(i||i===0){r+=i}r+="</a>\n                    </li>\n                </ul>\n            </div>\n            <div class='content' id='";h=q.resourceName;i=h||s.resourceName;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"resourceName",{hash:{}})}}r+=d(i)+"_";h=q.nickname;i=h||s.nickname;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"nickname",{hash:{}})}}r+=d(i)+"_";h=q.httpMethod;i=h||s.httpMethod;if(typeof i===e){i=i.call(s,{hash:{}})}else{if(i===c){i=p.call(s,"httpMethod",{hash:{}})}}r+=d(i)+"_content' style='display:none'>\n                ";h=q.notes;i=h||s.notes;f=q["if"];o=n.program(1,m,t);o.hash={};o.fn=o;o.inverse=n.noop;i=f.call(s,i,o);if(i||i===0){r+=i}r+="\n                <form accept-charset='UTF-8' action='#' class='sandbox' method='post'>\n                    <div style='margin:0;padding:0;display:inline'></div>\n                    <h4>Parameters</h4>\n                    <table class='fullwidth'>\n                        <thead>\n                        <tr>\n                            <th>Parameter</th>\n                            <th>Value</th>\n                            <th>Description</th>\n                        </tr>\n                        </thead>\n                        <tbody class=\"operation-params\">\n\n                        </tbody>\n                    </table>\n                    ";h=q.isReadOnly;i=h||s.isReadOnly;f=q["if"];o=n.program(3,l,t);o.hash={};o.fn=o;o.inverse=n.program(5,j,t);i=f.call(s,i,o);if(i||i===0){r+=i}r+="\n                </form>\n                <div class='response' style='display:none'>\n                    <h4>Request URL</h4>\n                    <div class='block request_url'></div>\n                    <h4>Response Body</h4>\n                    <div class='block response_body'></div>\n                    <h4>Response Code</h4>\n                    <div class='block response_code'></div>\n                    <h4>Response Headers</h4>\n                    <div class='block response_headers'></div>\n                </div>\n            </div>\n        </li>\n    </ul>\n";return r})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.param=b(function(h,v,t,m,w){t=t||h.helpers;var u="",j,g,i,r,q=this,e="function",s=t.helperMissing,c=void 0,d=this.escapeExpression;function p(B,A){var x="",z,y;x+="\n		";i=t.defaultValue;z=i||B.defaultValue;y=t["if"];r=q.program(2,o,A);r.hash={};r.fn=r;r.inverse=q.program(4,n,A);z=y.call(B,z,r);if(z||z===0){x+=z}x+="\n		\n	";return x}function o(A,z){var x="",y;x+="\n			<textarea class='body-textarea' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"'>";i=t.defaultValue;y=i||A.defaultValue;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"defaultValue",{hash:{}})}}x+=d(y)+"</textarea>\n		";return x}function n(A,z){var x="",y;x+="\n		    <textarea class='body-textarea' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"'></textarea>\n		";return x}function l(B,A){var x="",z,y;x+="\n		";i=t.defaultValue;z=i||B.defaultValue;y=t["if"];r=q.program(7,k,A);r.hash={};r.fn=r;r.inverse=q.program(9,f,A);z=y.call(B,z,r);if(z||z===0){x+=z}x+="\n	";return x}function k(A,z){var x="",y;x+="\n			<input minlength='0' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"' placeholder='' type='text' value='";i=t.defaultValue;y=i||A.defaultValue;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"defaultValue",{hash:{}})}}x+=d(y)+"'/>\n		";return x}function f(A,z){var x="",y;x+="\n		    <input minlength='0' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"' placeholder='' type='text' value=''/>\n		";return x}u+="<td class='code'>";i=t.name;j=i||v.name;if(typeof j===e){j=j.call(v,{hash:{}})}else{if(j===c){j=s.call(v,"name",{hash:{}})}}u+=d(j)+"</td>\n<td>\n	\n	";i=t.isBody;j=i||v.isBody;g=t["if"];r=q.program(1,p,w);r.hash={};r.fn=r;r.inverse=q.program(6,l,w);j=g.call(v,j,r);if(j||j===0){u+=j}u+="\n\n</td>\n<td width='500'>";i=t.description;j=i||v.description;if(typeof j===e){j=j.call(v,{hash:{}})}else{if(j===c){j=s.call(v,"description",{hash:{}})}}if(j||j===0){u+=j}u+="</td>\n\n";return u})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.param_list=b(function(h,v,t,m,x){t=t||h.helpers;var u="",k,g,j,r,q=this,e="function",s=t.helperMissing,c=void 0,d=this.escapeExpression;function p(z,y){return"\n        "}function o(C,B){var y="",A,z;y+="\n            ";j=t.defaultValue;A=j||C.defaultValue;z=t["if"];r=q.program(4,n,B);r.hash={};r.fn=r;r.inverse=q.program(6,l,B);A=z.call(C,A,r);if(A||A===0){y+=A}y+="\n        ";return y}function n(z,y){return"\n            "}function l(z,y){return"\n                <option selected=\"\" value=''></option>\n            "}function i(C,B){var y="",A,z;y+="\n            ";j=t.isDefault;A=j||C.isDefault;z=t["if"];r=q.program(9,f,B);r.hash={};r.fn=r;r.inverse=q.program(11,w,B);A=z.call(C,A,r);if(A||A===0){y+=A}y+="\n        ";return y}function f(B,A){var y="",z;y+="\n                <option value='";j=t.value;z=j||B.value;if(typeof z===e){z=z.call(B,{hash:{}})}else{if(z===c){z=s.call(B,"value",{hash:{}})}}y+=d(z)+"'>";j=t.value;z=j||B.value;if(typeof z===e){z=z.call(B,{hash:{}})}else{if(z===c){z=s.call(B,"value",{hash:{}})}}y+=d(z)+" (default)</option>\n            ";return y}function w(B,A){var y="",z;y+="\n                <option value='";j=t.value;z=j||B.value;if(typeof z===e){z=z.call(B,{hash:{}})}else{if(z===c){z=s.call(B,"value",{hash:{}})}}y+=d(z)+"'>";j=t.value;z=j||B.value;if(typeof z===e){z=z.call(B,{hash:{}})}else{if(z===c){z=s.call(B,"value",{hash:{}})}}y+=d(z)+"</option>\n            ";return y}u+="<td class='code'>";j=t.name;k=j||v.name;if(typeof k===e){k=k.call(v,{hash:{}})}else{if(k===c){k=s.call(v,"name",{hash:{}})}}u+=d(k)+"</td>\n<td>\n    <select name='";j=t.name;k=j||v.name;if(typeof k===e){k=k.call(v,{hash:{}})}else{if(k===c){k=s.call(v,"name",{hash:{}})}}u+=d(k)+"'>\n        ";j=t.required;k=j||v.required;g=t["if"];r=q.program(1,p,x);r.hash={};r.fn=r;r.inverse=q.program(3,o,x);k=g.call(v,k,r);if(k||k===0){u+=k}u+="\n        ";j=t.allowableValues;k=j||v.allowableValues;k=(k===null||k===undefined||k===false?k:k.descriptiveValues);g=t.each;r=q.program(8,i,x);r.hash={};r.fn=r;r.inverse=q.noop;k=g.call(v,k,r);if(k||k===0){u+=k}u+="\n    </select>\n</td>\n<td width='500'>";j=t.description;k=j||v.description;if(typeof k===e){k=k.call(v,{hash:{}})}else{if(k===c){k=s.call(v,"description",{hash:{}})}}if(k||k===0){u+=k}u+="</td>\n\n";return u})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.param_readonly=b(function(g,q,f,o,n){f=f||g.helpers;var l="",d,s,k,j,r=this,h="function",p=f.helperMissing,i=void 0,m=this.escapeExpression;function e(w,v){var t="",u;t+="\n        <textarea class='body-textarea' readonly='readonly' name='";k=f.name;u=k||w.name;if(typeof u===h){u=u.call(w,{hash:{}})}else{if(u===i){u=p.call(w,"name",{hash:{}})}}t+=m(u)+"'>";k=f.defaultValue;u=k||w.defaultValue;if(typeof u===h){u=u.call(w,{hash:{}})}else{if(u===i){u=p.call(w,"defaultValue",{hash:{}})}}t+=m(u)+"</textarea>\n    ";return t}function c(w,v){var t="",u;t+="\n        ";k=f.defaultValue;u=k||w.defaultValue;if(typeof u===h){u=u.call(w,{hash:{}})}else{if(u===i){u=p.call(w,"defaultValue",{hash:{}})}}t+=m(u)+"\n    ";return t}l+="<td class='code'>";k=f.name;d=k||q.name;if(typeof d===h){d=d.call(q,{hash:{}})}else{if(d===i){d=p.call(q,"name",{hash:{}})}}l+=m(d)+"</td>\n<td>\n    ";k=f.isBody;d=k||q.isBody;s=f["if"];j=r.program(1,e,n);j.hash={};j.fn=j;j.inverse=r.program(3,c,n);d=s.call(q,d,j);if(d||d===0){l+=d}l+="\n</td>\n<td width='500'>";k=f.description;d=k||q.description;if(typeof d===h){d=d.call(q,{hash:{}})}else{if(d===i){d=p.call(q,"description",{hash:{}})}}if(d||d===0){l+=d}l+="</td>\n\n";return l})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.param_readonly_required=b(function(g,q,f,o,n){f=f||g.helpers;var l="",d,s,k,j,r=this,h="function",p=f.helperMissing,i=void 0,m=this.escapeExpression;function e(w,v){var t="",u;t+="\n        <textarea class='body-textarea'  readonly='readonly' placeholder='(required)' name='";k=f.name;u=k||w.name;if(typeof u===h){u=u.call(w,{hash:{}})}else{if(u===i){u=p.call(w,"name",{hash:{}})}}t+=m(u)+"'>";k=f.defaultValue;u=k||w.defaultValue;if(typeof u===h){u=u.call(w,{hash:{}})}else{if(u===i){u=p.call(w,"defaultValue",{hash:{}})}}t+=m(u)+"</textarea>\n    ";return t}function c(w,v){var t="",u;t+="\n        ";k=f.defaultValue;u=k||w.defaultValue;if(typeof u===h){u=u.call(w,{hash:{}})}else{if(u===i){u=p.call(w,"defaultValue",{hash:{}})}}t+=m(u)+"\n    ";return t}l+="<td class='code required'>";k=f.name;d=k||q.name;if(typeof d===h){d=d.call(q,{hash:{}})}else{if(d===i){d=p.call(q,"name",{hash:{}})}}l+=m(d)+"</td>\n<td>\n    ";k=f.isBody;d=k||q.isBody;s=f["if"];j=r.program(1,e,n);j.hash={};j.fn=j;j.inverse=r.program(3,c,n);d=s.call(q,d,j);if(d||d===0){l+=d}l+="\n</td>\n<td width='500'>";k=f.description;d=k||q.description;if(typeof d===h){d=d.call(q,{hash:{}})}else{if(d===i){d=p.call(q,"description",{hash:{}})}}if(d||d===0){l+=d}l+="</td>\n";return l})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.param_required=b(function(h,v,t,m,w){t=t||h.helpers;var u="",j,g,i,r,q=this,e="function",s=t.helperMissing,c=void 0,d=this.escapeExpression;function p(B,A){var x="",z,y;x+="\n		";i=t.defaultValue;z=i||B.defaultValue;y=t["if"];r=q.program(2,o,A);r.hash={};r.fn=r;r.inverse=q.program(4,n,A);z=y.call(B,z,r);if(z||z===0){x+=z}x+="\n		\n	";return x}function o(A,z){var x="",y;x+="\n			<textarea class='body-textarea' placeholder='(required)' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"'>";i=t.defaultValue;y=i||A.defaultValue;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"defaultValue",{hash:{}})}}x+=d(y)+"</textarea>\n		";return x}function n(A,z){var x="",y;x+="\n		    <textarea class='body-textarea' placeholder='(required)' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"'></textarea>\n		";return x}function l(B,A){var x="",z,y;x+="\n		";i=t.defaultValue;z=i||B.defaultValue;y=t["if"];r=q.program(7,k,A);r.hash={};r.fn=r;r.inverse=q.program(9,f,A);z=y.call(B,z,r);if(z||z===0){x+=z}x+="\n	";return x}function k(A,z){var x="",y;x+="\n		    <input class='required' minlength='1' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"' placeholder='(required)' type='text' value='";i=t.defaultValue;y=i||A.defaultValue;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"defaultValue",{hash:{}})}}x+=d(y)+"'/>\n		";return x}function f(A,z){var x="",y;x+="\n		    <input class='required' minlength='1' name='";i=t.name;y=i||A.name;if(typeof y===e){y=y.call(A,{hash:{}})}else{if(y===c){y=s.call(A,"name",{hash:{}})}}x+=d(y)+"' placeholder='(required)' type='text' value=''/>\n		";return x}u+="<td class='code required'>";i=t.name;j=i||v.name;if(typeof j===e){j=j.call(v,{hash:{}})}else{if(j===c){j=s.call(v,"name",{hash:{}})}}u+=d(j)+"</td>\n<td>\n	";i=t.isBody;j=i||v.isBody;g=t["if"];r=q.program(1,p,w);r.hash={};r.fn=r;r.inverse=q.program(6,l,w);j=g.call(v,j,r);if(j||j===0){u+=j}u+="\n</td>\n<td width='500'>\n    <strong>";i=t.description;j=i||v.description;if(typeof j===e){j=j.call(v,{hash:{}})}else{if(j===c){j=s.call(v,"description",{hash:{}})}}if(j||j===0){u+=j}u+="</strong>\n</td>\n";return u})})();(function(){var b=Handlebars.template,a=Handlebars.templates=Handlebars.templates||{};a.resource=b(function(e,n,d,l,k){d=d||e.helpers;var i="",c,h,o=this,f="function",m=d.helperMissing,g=void 0,j=this.escapeExpression;i+="<div class='heading'>\n    <h2>\n        <a href='#!/";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"' onclick=\"Docs.toggleEndpointListForResource('";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"');\">/";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"</a>\n    </h2>\n    <ul class='options'>\n        <li>\n            <a href='#!/";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"' id='endpointListTogger_";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"'\n               onclick=\"Docs.toggleEndpointListForResource('";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"');\">Show/Hide</a>\n        </li>\n        <li>\n            <a href='#' onclick=\"Docs.collapseOperationsForResource('";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"'); return false;\">\n                List Operations\n            </a>\n        </li>\n        <li>\n            <a href='#' onclick=\"Docs.expandOperationsForResource('";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"'); return false;\">\n                Expand Operations\n            </a>\n        </li>\n        <li>\n            <a href='";h=d.url;c=h||n.url;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"url",{hash:{}})}}i+=j(c)+"'>Raw</a>\n        </li>\n    </ul>\n</div>\n<ul class='endpoints' id='";h=d.name;c=h||n.name;if(typeof c===f){c=c.call(n,{hash:{}})}else{if(c===g){c=m.call(n,"name",{hash:{}})}}i+=j(c)+"_endpoint_list' style='display:none'>\n\n</ul>\n";return i})})();(function(){var d,h,b,g,a,f,c={}.hasOwnProperty,e=function(l,j){for(var i in j){if(c.call(j,i)){l[i]=j[i]}}function k(){this.constructor=l}k.prototype=j.prototype;l.prototype=new k();l.__super__=j.prototype;return l};f=(function(j){e(i,j);function i(){return i.__super__.constructor.apply(this,arguments)}i.prototype.routes={"":"load"};i.prototype.dom_id="swagger_ui";i.prototype.options=null;i.prototype.api=null;i.prototype.headerView=null;i.prototype.mainView=null;i.prototype.initialize=function(k){var l=this;if(k==null){k={}}if(k.dom_id!=null){this.dom_id=k.dom_id;delete k.dom_id}if(!($("#"+this.dom_id)!=null)){$("body").append('<div id="'+this.dom_id+'"></div>')}this.options=k;this.options.success=function(){return l.render()};this.options.progress=function(m){return l.showMessage(m)};this.options.failure=function(m){return l.onLoadFailure(m)};this.headerView=new d({el:$("#header")});return this.headerView.on("update-swagger-ui",function(m){return l.updateSwaggerUi(m)})};i.prototype.updateSwaggerUi=function(k){this.options.discoveryUrl=k.discoveryUrl;this.options.apiKey=k.apiKey;return this.load()};i.prototype.load=function(){var k;if((k=this.mainView)!=null){k.clear()}this.headerView.update(this.options.discoveryUrl,this.options.apiKey);this.api=new SwaggerApi(this.options);return Backbone.history.start({pushState:true})};i.prototype.render=function(){var k=this;this.showMessage("Finished Loading Resource Information. Rendering Swagger UI...");this.mainView=new h({model:this.api,el:$("#"+this.dom_id)}).render();this.showMessage();return setTimeout(function(){return Docs.shebang()},400)};i.prototype.showMessage=function(k){if(k==null){k=""}$("#message-bar").removeClass("message-fail");$("#message-bar").addClass("message-success");return $("#message-bar").html(k)};i.prototype.onLoadFailure=function(k){if(k==null){k=""}$("#message-bar").removeClass("message-success");$("#message-bar").addClass("message-fail");return $("#message-bar").html(k)};return i})(Backbone.Router);window.SwaggerUi=f;d=(function(j){e(i,j);function i(){return i.__super__.constructor.apply(this,arguments)}i.prototype.events={"click #show-pet-store-icon":"showPetStore","click #show-wordnik-dev-icon":"showWordnikDev","click #explore":"showCustom","keyup #input_baseUrl":"showCustomOnKeyup","keyup #input_apiKey":"showCustomOnKeyup"};i.prototype.initialize=function(){};i.prototype.showPetStore=function(k){return this.trigger("update-swagger-ui",{discoveryUrl:"http://petstore.swagger.wordnik.com/api/resources.json",apiKey:"special-key"})};i.prototype.showWordnikDev=function(k){return this.trigger("update-swagger-ui",{discoveryUrl:"http://api.wordnik.com/v4/resources.json",apiKey:""})};i.prototype.showCustomOnKeyup=function(k){if(k.keyCode===13){return this.showCustom()}};i.prototype.showCustom=function(k){if(k!=null){k.preventDefault()}return this.trigger("update-swagger-ui",{discoveryUrl:$("#input_baseUrl").val(),apiKey:$("#input_apiKey").val()})};i.prototype.update=function(l,m,k){if(k==null){k=false}$("#input_baseUrl").val(l);$("#input_apiKey").val(m);if(k){return this.trigger("update-swagger-ui",{discoveryUrl:l,apiKey:m})}};return i})(Backbone.View);h=(function(i){e(j,i);function j(){return j.__super__.constructor.apply(this,arguments)}j.prototype.initialize=function(){};j.prototype.render=function(){var n,m,k,l;$(this.el).html(Handlebars.templates.main(this.model));l=this.model.resourcesArray;for(m=0,k=l.length;m<k;m++){n=l[m];this.addResource(n)}return this};j.prototype.addResource=function(l){var k;k=new a({model:l,tagName:"li",id:"resource_"+l.name,className:"resource"});return $("#resources").append(k.render().el)};j.prototype.clear=function(){return $(this.el).html("")};return j})(Backbone.View);a=(function(j){e(i,j);function i(){return i.__super__.constructor.apply(this,arguments)}i.prototype.initialize=function(){};i.prototype.render=function(){var l,n,k,m;$(this.el).html(Handlebars.templates.resource(this.model));m=this.model.operationsArray;for(n=0,k=m.length;n<k;n++){l=m[n];this.addOperation(l)}return this};i.prototype.addOperation=function(k){var l;l=new b({model:k,tagName:"li",className:"endpoint"});return $(".endpoints",$(this.el)).append(l.render().el)};return i})(Backbone.View);b=(function(j){e(i,j);function i(){return i.__super__.constructor.apply(this,arguments)}i.prototype.events={"click .submit":"submitOperation","click .response_hider":"hideResponse","click .toggleOperation":"toggleOperationContent"};i.prototype.initialize=function(){};i.prototype.render=function(){var l,o,n,k,m;l=jQuery.inArray(this.model.httpMethod,this.model.supportedSubmitMethods())>=0;if(!l){this.model.isReadOnly=true}$(this.el).html(Handlebars.templates.operation(this.model));m=this.model.parameters;for(n=0,k=m.length;n<k;n++){o=m[n];this.addParameter(o)}return this};i.prototype.addParameter=function(l){var k;k=new g({model:l,tagName:"tr",readOnly:this.model.isReadOnly});return $(".operation-params",$(this.el)).append(k.render().el)};i.prototype.submitOperation=function(){var z,y,m,x,A,k,n,u,p,s,r,w,l,t,q,v=this;m=$(".sandbox",$(this.el));y=true;m.find("input.required").each(function(){var o=this;$(this).removeClass("error");if(jQuery.trim($(this).val())===""){$(this).addClass("error");$(this).wiggle({callback:function(){return $(o).focus()}});return y=false}});if(y){k={};t=m.serializeArray();for(s=0,w=t.length;s<w;s++){n=t[s];if((n.value!=null)&&jQuery.trim(n.value).length>0){k[n.name]=n.value}}z=null;q=this.model.parameters;for(r=0,l=q.length;r<l;r++){p=q[r];if(p.paramType==="body"){z=k[p.name]}}log("bodyParam = "+z);x=null;A=this.model.supportHeaderParams()?(x=this.model.getHeaderParams(k),this.model.urlify(k,false)):this.model.urlify(k,true);log("submitting "+A);$(".request_url",$(this.el)).html("<pre>"+A+"</pre>");$(".response_throbber",$(this.el)).show();u={type:this.model.httpMethod,url:A,headers:x,data:z,dataType:"json",error:function(B,C,o){return v.showErrorStatus(B,C,o)},success:function(o){return v.showResponse(o)},complete:function(o){return v.showCompleteStatus(o)}};if(u.type.toLowerCase()==="post"||u.type.toLowerCase()==="put"){u.contentType="application/json"}return jQuery.ajax(u)}};i.prototype.hideResponse=function(k){if(k!=null){k.preventDefault()}$(".response",$(this.el)).slideUp();return $(".response_hider",$(this.el)).fadeOut()};i.prototype.showResponse=function(k){var l;l=JSON.stringify(k,null,"\t").replace(/\n/g,"<br>");return $(".response_body",$(this.el)).html(l)};i.prototype.showErrorStatus=function(k){return this.showStatus(k)};i.prototype.showCompleteStatus=function(k){return this.showStatus(k)};i.prototype.showStatus=function(m){var l;try{l="<pre>"+JSON.stringify(JSON.parse(m.responseText),null,2).replace(/\n/g,"<br>")+"</pre>"}catch(k){l="<span style='color:red'>&nbsp;&nbsp;&nbsp;[unable to parse as json; raw response below]</span><br><pre>"+m.responseText+"</pre>"}$(".response_code",$(this.el)).html("<pre>"+m.status+"</pre>");$(".response_body",$(this.el)).html(l);$(".response_headers",$(this.el)).html("<pre>"+m.getAllResponseHeaders()+"</pre>");$(".response",$(this.el)).slideDown();$(".response_hider",$(this.el)).show();return $(".response_throbber",$(this.el)).hide()};i.prototype.toggleOperationContent=function(){var k;k=$("#"+this.model.resourceName+"_"+this.model.nickname+"_"+this.model.httpMethod+"_content");if(k.is(":visible")){return Docs.collapseOperation(k)}else{return Docs.expandOperation(k)}};return i})(Backbone.View);g=(function(j){e(i,j);function i(){return i.__super__.constructor.apply(this,arguments)}i.prototype.initialize=function(){};i.prototype.render=function(){var k;if(this.model.paramType==="body"){this.model.isBody=true}k=this.template();$(this.el).html(k(this.model));return this};i.prototype.template=function(){if(this.model.isList){return Handlebars.templates.param_list}else{if(this.options.readOnly){if(this.model.required){return Handlebars.templates.param_readonly_required}else{return Handlebars.templates.param_readonly}}else{if(this.model.required){return Handlebars.templates.param_required}else{return Handlebars.templates.param}}}};return i})(Backbone.View)}).call(this);